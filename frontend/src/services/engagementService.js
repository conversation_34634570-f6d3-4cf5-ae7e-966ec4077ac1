import api from './api';

export const engagementService = {
  // Get all engagements with optional filters
  getEngagements: (params = {}) => {
    return api.get('/engagements/', { params });
  },

  // Get engagement by ID
  getEngagement: (id) => {
    return api.get(`/engagements/${id}/`);
  },

  // Create new engagement
  createEngagement: (engagementData) => {
    return api.post('/engagements/', engagementData);
  },

  // Update engagement
  updateEngagement: (id, engagementData) => {
    return api.put(`/engagements/${id}/`, engagementData);
  },

  // Delete engagement
  deleteEngagement: (id) => {
    return api.delete(`/engagements/${id}/`);
  },

  // Get dashboard statistics
  getDashboardStats: () => {
    return api.get('/engagements/dashboard-stats/');
  },

  // Start engagement
  startEngagement: (id) => {
    return api.post(`/engagements/${id}/start/`);
  },

  // Complete engagement
  completeEngagement: (id, completionData) => {
    return api.post(`/engagements/${id}/complete/`, completionData);
  },

  // Get engagement team
  getEngagementTeam: (id) => {
    return api.get(`/engagements/${id}/team/`);
  },

  // Add team member
  addTeamMember: (engagementId, memberData) => {
    return api.post(`/engagements/${engagementId}/team/`, memberData);
  },

  // Remove team member
  removeTeamMember: (engagementId, memberId) => {
    return api.delete(`/engagements/${engagementId}/team/${memberId}/`);
  },

  // Update team member
  updateTeamMember: (engagementId, memberId, memberData) => {
    return api.put(`/engagements/${engagementId}/team/${memberId}/`, memberData);
  },

  // Get engagement checklist
  getChecklist: (engagementId) => {
    return api.get(`/engagements/${engagementId}/checklist/`);
  },

  // Update checklist item
  updateChecklistItem: (engagementId, itemId, itemData) => {
    return api.put(`/engagements/${engagementId}/checklist/${itemId}/`, itemData);
  },

  // Generate engagement letter
  generateEngagementLetter: (engagementId, letterData) => {
    return api.post(`/engagements/${engagementId}/generate-letter/`, letterData);
  },

  // Get engagement letter
  getEngagementLetter: (engagementId) => {
    return api.get(`/engagements/${engagementId}/letter/`);
  },

  // Update engagement letter
  updateEngagementLetter: (engagementId, letterData) => {
    return api.put(`/engagements/${engagementId}/letter/`, letterData);
  },

  // Send engagement letter
  sendEngagementLetter: (engagementId) => {
    return api.post(`/engagements/${engagementId}/send-letter/`);
  },

  // Get engagement progress
  getEngagementProgress: (id) => {
    return api.get(`/engagements/${id}/progress/`);
  },

  // Update engagement status
  updateEngagementStatus: (id, status, notes = '') => {
    return api.post(`/engagements/${id}/update-status/`, { status, notes });
  },

  // Get engagement timeline
  getEngagementTimeline: (id) => {
    return api.get(`/engagements/${id}/timeline/`);
  },

  // Add timeline entry
  addTimelineEntry: (engagementId, entryData) => {
    return api.post(`/engagements/${engagementId}/timeline/`, entryData);
  },

  // Get engagement documents
  getEngagementDocuments: (id) => {
    return api.get(`/engagements/${id}/documents/`);
  },

  // Get engagement working papers
  getEngagementWorkingPapers: (id) => {
    return api.get(`/engagements/${id}/working-papers/`);
  },

  // Get engagement risk assessment
  getEngagementRiskAssessment: (id) => {
    return api.get(`/engagements/${id}/risk-assessment/`);
  },

  // Clone engagement
  cloneEngagement: (id, cloneData) => {
    return api.post(`/engagements/${id}/clone/`, cloneData);
  },

  // Archive engagement
  archiveEngagement: (id) => {
    return api.post(`/engagements/${id}/archive/`);
  },

  // Export engagement data
  exportEngagement: (id, format = 'pdf') => {
    return api.get(`/engagements/${id}/export/`, {
      params: { format },
      responseType: 'blob'
    });
  },

  // Get engagement templates
  getEngagementTemplates: () => {
    return api.get('/engagements/templates/');
  },

  // Create engagement from template
  createFromTemplate: (templateId, engagementData) => {
    return api.post(`/engagements/templates/${templateId}/create/`, engagementData);
  },

  // Get upcoming deadlines
  getUpcomingDeadlines: (days = 30) => {
    return api.get('/engagements/upcoming-deadlines/', { params: { days } });
  },

  // Get overdue engagements
  getOverdueEngagements: () => {
    return api.get('/engagements/overdue/');
  },

  // Get engagement analytics
  getEngagementAnalytics: (params = {}) => {
    return api.get('/engagements/analytics/', { params });
  },

  // Bulk update engagements
  bulkUpdateEngagements: (engagementIds, updateData) => {
    return api.post('/engagements/bulk-update/', {
      engagement_ids: engagementIds,
      update_data: updateData
    });
  }
};
