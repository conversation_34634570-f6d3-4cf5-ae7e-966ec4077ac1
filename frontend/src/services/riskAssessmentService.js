import api from './api';

export const riskAssessmentService = {
  // Get all risk assessments with optional filters
  getAssessments: (params = {}) => {
    return api.get('/risk-assessment/assessments/', { params });
  },

  // Get risk assessment by ID
  getAssessment: (id) => {
    return api.get(`/risk-assessment/assessments/${id}/`);
  },

  // Create new risk assessment
  createAssessment: (assessmentData) => {
    return api.post('/risk-assessment/assessments/', assessmentData);
  },

  // Update risk assessment
  updateAssessment: (id, assessmentData) => {
    return api.put(`/risk-assessment/assessments/${id}/`, assessmentData);
  },

  // Delete risk assessment
  deleteAssessment: (id) => {
    return api.delete(`/risk-assessment/assessments/${id}/`);
  },

  // Create assessment for engagement
  createAssessmentForEngagement: (engagementId, financialData = {}) => {
    return api.post('/risk-assessment/assessments/create_assessment/', {
      engagement_id: engagementId,
      financial_data: financialData
    });
  },

  // Generate audit plan from risk assessment
  generateAuditPlan: (assessmentId) => {
    return api.post(`/risk-assessment/assessments/${assessmentId}/generate_audit_plan/`);
  },

  // Approve risk assessment
  approveAssessment: (id) => {
    return api.post(`/risk-assessment/assessments/${id}/approve_assessment/`);
  },

  // Analyze trial balance
  analyzeTrialBalance: (engagementId, trialBalanceData) => {
    return api.post('/risk-assessment/assessments/analyze_trial_balance/', {
      engagement_id: engagementId,
      trial_balance_data: trialBalanceData
    });
  },

  // Get risk dashboard data
  getRiskDashboard: () => {
    return api.get('/risk-assessment/assessments/risk_dashboard/');
  },

  // Risk Factors
  getRiskFactors: (params = {}) => {
    return api.get('/risk-assessment/risk-factors/', { params });
  },

  createRiskFactor: (factorData) => {
    return api.post('/risk-assessment/risk-factors/', factorData);
  },

  updateRiskFactor: (id, factorData) => {
    return api.put(`/risk-assessment/risk-factors/${id}/`, factorData);
  },

  deleteRiskFactor: (id) => {
    return api.delete(`/risk-assessment/risk-factors/${id}/`);
  },

  // Audit Plans
  getAuditPlans: (params = {}) => {
    return api.get('/risk-assessment/audit-plans/', { params });
  },

  getAuditPlan: (id) => {
    return api.get(`/risk-assessment/audit-plans/${id}/`);
  },

  createAuditPlan: (planData) => {
    return api.post('/risk-assessment/audit-plans/', planData);
  },

  updateAuditPlan: (id, planData) => {
    return api.put(`/risk-assessment/audit-plans/${id}/`, planData);
  },

  deleteAuditPlan: (id) => {
    return api.delete(`/risk-assessment/audit-plans/${id}/`);
  },

  // Approve audit plan
  approveAuditPlan: (id) => {
    return api.post(`/risk-assessment/audit-plans/${id}/approve_plan/`);
  },

  // Get resource summary for audit plan
  getResourceSummary: (id) => {
    return api.get(`/risk-assessment/audit-plans/${id}/resource_summary/`);
  },

  // Audit Procedures
  getAuditProcedures: (params = {}) => {
    return api.get('/risk-assessment/procedures/', { params });
  },

  getAuditProcedure: (id) => {
    return api.get(`/risk-assessment/procedures/${id}/`);
  },

  createAuditProcedure: (procedureData) => {
    return api.post('/risk-assessment/procedures/', procedureData);
  },

  updateAuditProcedure: (id, procedureData) => {
    return api.put(`/risk-assessment/procedures/${id}/`, procedureData);
  },

  deleteAuditProcedure: (id) => {
    return api.delete(`/risk-assessment/procedures/${id}/`);
  },

  // Start audit procedure
  startProcedure: (id) => {
    return api.post(`/risk-assessment/procedures/${id}/start_procedure/`);
  },

  // Complete audit procedure
  completeProcedure: (id, completionData) => {
    return api.post(`/risk-assessment/procedures/${id}/complete_procedure/`, completionData);
  },

  // Materiality Calculations
  getMaterialityCalculations: (params = {}) => {
    return api.get('/risk-assessment/materiality/', { params });
  },

  getMaterialityCalculation: (id) => {
    return api.get(`/risk-assessment/materiality/${id}/`);
  },

  // Risk Templates
  getRiskTemplates: (params = {}) => {
    return api.get('/risk-assessment/templates/', { params });
  },

  getRiskTemplate: (id) => {
    return api.get(`/risk-assessment/templates/${id}/`);
  },

  createRiskTemplate: (templateData) => {
    return api.post('/risk-assessment/templates/', templateData);
  },

  updateRiskTemplate: (id, templateData) => {
    return api.put(`/risk-assessment/templates/${id}/`, templateData);
  },

  deleteRiskTemplate: (id) => {
    return api.delete(`/risk-assessment/templates/${id}/`);
  },

  // Apply risk template to engagement
  applyRiskTemplate: (templateId, engagementId) => {
    return api.post(`/risk-assessment/templates/${templateId}/apply_template/`, {
      engagement_id: engagementId
    });
  },

  // Get template recommendations
  getTemplateRecommendations: (industry, entityType) => {
    return api.get('/risk-assessment/templates/template_recommendations/', {
      params: { industry, entity_type: entityType }
    });
  },

  // Calculate materiality
  calculateMateriality: (financialData, riskLevel, basePreference = 'AUTO') => {
    return api.post('/risk-assessment/calculate-materiality/', {
      financial_data: financialData,
      risk_level: riskLevel,
      base_preference: basePreference
    });
  },

  // Get risk assessment statistics
  getRiskAssessmentStats: () => {
    return api.get('/risk-assessment/statistics/');
  },

  // Export risk assessment
  exportRiskAssessment: (id, format = 'pdf') => {
    return api.get(`/risk-assessment/assessments/${id}/export/`, {
      params: { format },
      responseType: 'blob'
    });
  },

  // Import risk assessment
  importRiskAssessment: (file) => {
    const formData = new FormData();
    formData.append('file', file);
    return api.post('/risk-assessment/assessments/import/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // Get risk trends
  getRiskTrends: (params = {}) => {
    return api.get('/risk-assessment/trends/', { params });
  },

  // Get industry risk benchmarks
  getIndustryBenchmarks: (industry) => {
    return api.get('/risk-assessment/industry-benchmarks/', {
      params: { industry }
    });
  },

  // Validate risk assessment
  validateRiskAssessment: (assessmentData) => {
    return api.post('/risk-assessment/validate/', assessmentData);
  },

  // Get risk assessment history
  getRiskAssessmentHistory: (id) => {
    return api.get(`/risk-assessment/assessments/${id}/history/`);
  },

  // Clone risk assessment
  cloneRiskAssessment: (id, newEngagementId) => {
    return api.post(`/risk-assessment/assessments/${id}/clone/`, {
      new_engagement_id: newEngagementId
    });
  },

  // Get risk assessment comments
  getRiskAssessmentComments: (id) => {
    return api.get(`/risk-assessment/assessments/${id}/comments/`);
  },

  // Add risk assessment comment
  addRiskAssessmentComment: (id, comment) => {
    return api.post(`/risk-assessment/assessments/${id}/comments/`, { comment });
  }
};
