import api from './api';

export const documentService = {
  // Upload document
  uploadDocument: (formData) => {
    return api.post('/documents/upload/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // Get all documents with optional filters
  getDocuments: (params = {}) => {
    return api.get('/documents/', { params });
  },

  // Get document by ID
  getDocument: (id) => {
    return api.get(`/documents/${id}/`);
  },

  // Update document
  updateDocument: (id, documentData) => {
    return api.put(`/documents/${id}/`, documentData);
  },

  // Delete document
  deleteDocument: (id) => {
    return api.delete(`/documents/${id}/`);
  },

  // Process document with AI
  processDocument: (id) => {
    return api.post(`/documents/${id}/process/`);
  },

  // Get document processing status
  getProcessingStatus: (id) => {
    return api.get(`/documents/${id}/processing-status/`);
  },

  // Get document analysis results
  getAnalysisResults: (id) => {
    return api.get(`/documents/${id}/analysis/`);
  },

  // Get extracted entities
  getExtractedEntities: (id) => {
    return api.get(`/documents/${id}/entities/`);
  },

  // Get document anomalies
  getDocumentAnomalies: (id) => {
    return api.get(`/documents/${id}/anomalies/`);
  },

  // Mark anomaly as resolved
  resolveAnomaly: (documentId, anomalyId, resolution) => {
    return api.post(`/documents/${documentId}/anomalies/${anomalyId}/resolve/`, {
      resolution
    });
  },

  // Approve document
  approveDocument: (id, approvalData) => {
    return api.post(`/documents/${id}/approve/`, approvalData);
  },

  // Reject document
  rejectDocument: (id, rejectionData) => {
    return api.post(`/documents/${id}/reject/`, rejectionData);
  },

  // Download document
  downloadDocument: (id) => {
    return api.get(`/documents/${id}/download/`, {
      responseType: 'blob'
    });
  },

  // Get document preview
  getDocumentPreview: (id) => {
    return api.get(`/documents/${id}/preview/`);
  },

  // Search documents
  searchDocuments: (query, filters = {}) => {
    return api.get('/documents/search/', {
      params: { q: query, ...filters }
    });
  },

  // Bulk process documents
  bulkProcessDocuments: (documentIds) => {
    return api.post('/documents/bulk-process/', {
      document_ids: documentIds
    });
  },

  // Get document templates
  getDocumentTemplates: () => {
    return api.get('/documents/templates/');
  },

  // Create document template
  createDocumentTemplate: (templateData) => {
    return api.post('/documents/templates/', templateData);
  },

  // Update document template
  updateDocumentTemplate: (id, templateData) => {
    return api.put(`/documents/templates/${id}/`, templateData);
  },

  // Delete document template
  deleteDocumentTemplate: (id) => {
    return api.delete(`/documents/templates/${id}/`);
  },

  // Apply template to document
  applyTemplate: (documentId, templateId) => {
    return api.post(`/documents/${documentId}/apply-template/`, {
      template_id: templateId
    });
  },

  // Get document statistics
  getDocumentStats: () => {
    return api.get('/documents/statistics/');
  },

  // Get processing queue status
  getProcessingQueue: () => {
    return api.get('/documents/processing-queue/');
  },

  // Retry failed processing
  retryProcessing: (id) => {
    return api.post(`/documents/${id}/retry-processing/`);
  },

  // Get document history
  getDocumentHistory: (id) => {
    return api.get(`/documents/${id}/history/`);
  },

  // Add document comment
  addDocumentComment: (documentId, comment) => {
    return api.post(`/documents/${documentId}/comments/`, { comment });
  },

  // Get document comments
  getDocumentComments: (id) => {
    return api.get(`/documents/${id}/comments/`);
  },

  // Update document comment
  updateDocumentComment: (documentId, commentId, comment) => {
    return api.put(`/documents/${documentId}/comments/${commentId}/`, { comment });
  },

  // Delete document comment
  deleteDocumentComment: (documentId, commentId) => {
    return api.delete(`/documents/${documentId}/comments/${commentId}/`);
  },

  // Share document
  shareDocument: (id, shareData) => {
    return api.post(`/documents/${id}/share/`, shareData);
  },

  // Get shared documents
  getSharedDocuments: () => {
    return api.get('/documents/shared/');
  },

  // Revoke document sharing
  revokeSharing: (id, shareId) => {
    return api.delete(`/documents/${id}/share/${shareId}/`);
  },

  // Export documents
  exportDocuments: (documentIds, format = 'zip') => {
    return api.post('/documents/export/', {
      document_ids: documentIds,
      format
    }, {
      responseType: 'blob'
    });
  },

  // Get document insights
  getDocumentInsights: (params = {}) => {
    return api.get('/documents/insights/', { params });
  },

  // Classify document
  classifyDocument: (id) => {
    return api.post(`/documents/${id}/classify/`);
  },

  // Get classification suggestions
  getClassificationSuggestions: (id) => {
    return api.get(`/documents/${id}/classification-suggestions/`);
  },

  // Update document classification
  updateDocumentClassification: (id, classification) => {
    return api.put(`/documents/${id}/classification/`, { classification });
  }
};
