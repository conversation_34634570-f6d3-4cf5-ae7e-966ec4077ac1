import api from './api';

export const clientService = {
  // Get all clients with optional filters
  getClients: (params = {}) => {
    return api.get('/clients/', { params });
  },

  // Get client by ID
  getClient: (id) => {
    return api.get(`/clients/${id}/`);
  },

  // Create new client
  createClient: (clientData) => {
    return api.post('/clients/', clientData);
  },

  // Update client
  updateClient: (id, clientData) => {
    return api.put(`/clients/${id}/`, clientData);
  },

  // Delete client
  deleteClient: (id) => {
    return api.delete(`/clients/${id}/`);
  },

  // Get client dashboard stats
  getDashboardStats: () => {
    return api.get('/clients/dashboard-stats/');
  },

  // Search clients
  searchClients: (query) => {
    return api.get('/clients/search/', { params: { q: query } });
  },

  // Get client audit history
  getAuditHistory: (clientId) => {
    return api.get(`/clients/${clientId}/audit-history/`);
  },

  // Validate PAN number
  validatePAN: (panNumber) => {
    return api.post('/clients/validate-pan/', { pan_number: panNumber });
  },

  // Validate GST number
  validateGST: (gstNumber) => {
    return api.post('/clients/validate-gst/', { gst_number: gstNumber });
  },

  // Get client engagements
  getClientEngagements: (clientId) => {
    return api.get(`/clients/${clientId}/engagements/`);
  },

  // Export clients data
  exportClients: (format = 'excel') => {
    return api.get('/clients/export/', {
      params: { format },
      responseType: 'blob'
    });
  },

  // Import clients data
  importClients: (file) => {
    const formData = new FormData();
    formData.append('file', file);
    return api.post('/clients/import/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // Get client statistics
  getClientStats: () => {
    return api.get('/clients/statistics/');
  },

  // Archive client
  archiveClient: (id) => {
    return api.post(`/clients/${id}/archive/`);
  },

  // Restore archived client
  restoreClient: (id) => {
    return api.post(`/clients/${id}/restore/`);
  }
};
