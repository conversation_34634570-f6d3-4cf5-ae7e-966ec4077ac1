import React, { useState, useCallback } from 'react';
import {
  Box,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  LinearProgress,
  Chip,
  Grid,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Tooltip,
  Divider
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Description as FileIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Visibility as ViewIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  SmartToy as AIIcon
} from '@mui/icons-material';
import { useDropzone } from 'react-dropzone';
import { documentService } from '../../services/documentService';

const DocumentUpload = ({ clientId, engagementId, onUploadComplete }) => {
  const [files, setFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});
  const [processingResults, setProcessingResults] = useState({});
  const [selectedFile, setSelectedFile] = useState(null);
  const [showResults, setShowResults] = useState(false);

  const onDrop = useCallback((acceptedFiles) => {
    const newFiles = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      status: 'pending',
      progress: 0,
      error: null,
      result: null
    }));
    setFiles(prev => [...prev, ...newFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'text/csv': ['.csv'],
      'image/*': ['.jpg', '.jpeg', '.png', '.tiff']
    },
    maxSize: 10 * 1024 * 1024 // 10MB
  });

  const uploadFiles = async () => {
    setUploading(true);
    
    for (const fileItem of files) {
      if (fileItem.status !== 'pending') continue;

      try {
        // Update file status to uploading
        setFiles(prev => prev.map(f => 
          f.id === fileItem.id ? { ...f, status: 'uploading', progress: 0 } : f
        ));

        const formData = new FormData();
        formData.append('file', fileItem.file);
        formData.append('client_id', clientId);
        if (engagementId) {
          formData.append('engagement_id', engagementId);
        }
        formData.append('document_name', fileItem.file.name);
        formData.append('document_type', detectDocumentType(fileItem.file.name));

        // Simulate upload progress
        const progressInterval = setInterval(() => {
          setFiles(prev => prev.map(f => {
            if (f.id === fileItem.id && f.progress < 90) {
              return { ...f, progress: f.progress + 10 };
            }
            return f;
          }));
        }, 200);

        const response = await documentService.uploadDocument(formData);

        clearInterval(progressInterval);

        // Update file status to processing
        setFiles(prev => prev.map(f => 
          f.id === fileItem.id ? { 
            ...f, 
            status: 'processing', 
            progress: 100,
            documentId: response.data.id 
          } : f
        ));

        // Start processing status check
        checkProcessingStatus(fileItem.id, response.data.id);

      } catch (error) {
        setFiles(prev => prev.map(f => 
          f.id === fileItem.id ? { 
            ...f, 
            status: 'error', 
            error: error.response?.data?.message || 'Upload failed' 
          } : f
        ));
      }
    }

    setUploading(false);
  };

  const checkProcessingStatus = async (fileId, documentId) => {
    try {
      const response = await documentService.getDocument(documentId);
      const document = response.data;

      if (document.processing_status === 'COMPLETED') {
        setFiles(prev => prev.map(f => 
          f.id === fileId ? { 
            ...f, 
            status: 'completed',
            result: document,
            hasAnomalies: document.has_anomalies,
            requiresReview: document.requires_review
          } : f
        ));
        
        setProcessingResults(prev => ({
          ...prev,
          [fileId]: document
        }));

        if (onUploadComplete) {
          onUploadComplete(document);
        }
      } else if (document.processing_status === 'FAILED') {
        setFiles(prev => prev.map(f => 
          f.id === fileId ? { 
            ...f, 
            status: 'error',
            error: document.processing_error || 'Processing failed'
          } : f
        ));
      } else {
        // Still processing, check again in 2 seconds
        setTimeout(() => checkProcessingStatus(fileId, documentId), 2000);
      }
    } catch (error) {
      setFiles(prev => prev.map(f => 
        f.id === fileId ? { 
          ...f, 
          status: 'error',
          error: 'Failed to check processing status'
        } : f
      ));
    }
  };

  const detectDocumentType = (filename) => {
    const name = filename.toLowerCase();
    if (name.includes('bank') || name.includes('statement')) return 'BANK_STATEMENT';
    if (name.includes('gst') || name.includes('gstr')) return 'GST_REPORT';
    if (name.includes('26as')) return 'FORM_26AS';
    if (name.includes('invoice') || name.includes('bill')) return 'INVOICE';
    if (name.includes('balance') || name.includes('sheet')) return 'BALANCE_SHEET';
    return 'OTHERS';
  };

  const removeFile = (fileId) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const viewResults = (fileItem) => {
    setSelectedFile(fileItem);
    setShowResults(true);
  };

  const getStatusIcon = (status, hasAnomalies, requiresReview) => {
    switch (status) {
      case 'completed':
        if (hasAnomalies || requiresReview) {
          return <WarningIcon color="warning" />;
        }
        return <SuccessIcon color="success" />;
      case 'error':
        return <ErrorIcon color="error" />;
      case 'processing':
      case 'uploading':
        return <RefreshIcon className="animate-spin" color="primary" />;
      default:
        return <FileIcon color="action" />;
    }
  };

  const getStatusText = (status, hasAnomalies, requiresReview) => {
    switch (status) {
      case 'completed':
        if (hasAnomalies) return 'Completed with anomalies';
        if (requiresReview) return 'Completed - requires review';
        return 'Completed successfully';
      case 'error':
        return 'Processing failed';
      case 'processing':
        return 'Processing with AI...';
      case 'uploading':
        return 'Uploading...';
      default:
        return 'Ready to upload';
    }
  };

  return (
    <Box>
      {/* Upload Area */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box
            {...getRootProps()}
            sx={{
              border: '2px dashed',
              borderColor: isDragActive ? 'primary.main' : 'grey.300',
              borderRadius: 2,
              p: 4,
              textAlign: 'center',
              cursor: 'pointer',
              bgcolor: isDragActive ? 'action.hover' : 'background.paper',
              transition: 'all 0.2s ease-in-out'
            }}
          >
            <input {...getInputProps()} />
            <UploadIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              {isDragActive ? 'Drop files here' : 'Drag & drop files here'}
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              or click to select files
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Supported: PDF, Excel, CSV, Images (max 10MB each)
            </Typography>
          </Box>
        </CardContent>
      </Card>

      {/* File List */}
      {files.length > 0 && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Files ({files.length})
              </Typography>
              <Button
                variant="contained"
                onClick={uploadFiles}
                disabled={uploading || files.every(f => f.status !== 'pending')}
                startIcon={<UploadIcon />}
              >
                Upload & Process
              </Button>
            </Box>

            <List>
              {files.map((fileItem, index) => (
                <React.Fragment key={fileItem.id}>
                  <ListItem>
                    <ListItemIcon>
                      {getStatusIcon(fileItem.status, fileItem.hasAnomalies, fileItem.requiresReview)}
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body1">
                            {fileItem.file.name}
                          </Typography>
                          <Chip
                            label={detectDocumentType(fileItem.file.name)}
                            size="small"
                            variant="outlined"
                          />
                          {fileItem.hasAnomalies && (
                            <Chip
                              label="Anomalies Detected"
                              size="small"
                              color="warning"
                              icon={<WarningIcon />}
                            />
                          )}
                          {fileItem.requiresReview && (
                            <Chip
                              label="Requires Review"
                              size="small"
                              color="info"
                            />
                          )}
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {getStatusText(fileItem.status, fileItem.hasAnomalies, fileItem.requiresReview)}
                          </Typography>
                          {(fileItem.status === 'uploading' || fileItem.status === 'processing') && (
                            <LinearProgress
                              variant={fileItem.status === 'processing' ? 'indeterminate' : 'determinate'}
                              value={fileItem.progress}
                              sx={{ mt: 1 }}
                            />
                          )}
                          {fileItem.error && (
                            <Alert severity="error" sx={{ mt: 1 }}>
                              {fileItem.error}
                            </Alert>
                          )}
                        </Box>
                      }
                    />
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      {fileItem.status === 'completed' && (
                        <Tooltip title="View AI Analysis Results">
                          <IconButton onClick={() => viewResults(fileItem)}>
                            <AIIcon />
                          </IconButton>
                        </Tooltip>
                      )}
                      <Tooltip title="Remove">
                        <IconButton onClick={() => removeFile(fileItem.id)}>
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </ListItem>
                  {index < files.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </CardContent>
        </Card>
      )}

      {/* Processing Results Dialog */}
      <Dialog open={showResults} onClose={() => setShowResults(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <AIIcon />
            AI Processing Results
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedFile?.result && (
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Document: {selectedFile.file.name}
                </Typography>
                <Chip
                  label={`Confidence: ${(selectedFile.result.confidence_score * 100).toFixed(1)}%`}
                  color={selectedFile.result.confidence_score > 0.8 ? 'success' : 'warning'}
                />
              </Grid>

              {/* Extracted Data */}
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle1" gutterBottom>
                  Extracted Information
                </Typography>
                <Box sx={{ bgcolor: 'grey.50', p: 2, borderRadius: 1 }}>
                  {selectedFile.result.extracted_data?.amounts && (
                    <Typography variant="body2">
                      <strong>Amounts Found:</strong> {selectedFile.result.extracted_data.amounts.length}
                    </Typography>
                  )}
                  {selectedFile.result.extracted_data?.dates && (
                    <Typography variant="body2">
                      <strong>Dates Found:</strong> {selectedFile.result.extracted_data.dates.length}
                    </Typography>
                  )}
                  {selectedFile.result.extracted_data?.entities && (
                    <Typography variant="body2">
                      <strong>Entities Found:</strong> {selectedFile.result.extracted_data.entities.length}
                    </Typography>
                  )}
                </Box>
              </Grid>

              {/* Anomalies */}
              {selectedFile.hasAnomalies && (
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" gutterBottom color="warning.main">
                    Anomalies Detected
                  </Typography>
                  <Alert severity="warning">
                    This document contains unusual patterns that require manual review.
                  </Alert>
                </Grid>
              )}

              {/* Extracted Text Preview */}
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>
                  Extracted Text (Preview)
                </Typography>
                <Box
                  sx={{
                    bgcolor: 'grey.50',
                    p: 2,
                    borderRadius: 1,
                    maxHeight: 200,
                    overflow: 'auto',
                    fontFamily: 'monospace',
                    fontSize: '0.875rem'
                  }}
                >
                  {selectedFile.result.extracted_text?.substring(0, 500)}
                  {selectedFile.result.extracted_text?.length > 500 && '...'}
                </Box>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowResults(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DocumentUpload;
