import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Badge
} from '@mui/material';
import {
  Gavel as ComplianceIcon,
  Schedule as DeadlineIcon,
  Warning as WarningIcon,
  CheckCircle as CompleteIcon,
  Assignment as FormIcon,
  AccountBalance as RegulatoryIcon,
  TrendingUp as TrendIcon,
  Notifications as NotificationIcon,
  FileDownload as DownloadIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Bar, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip as Recharts<PERSON>ool<PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Line } from 'recharts';

const ComplianceTracker = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [complianceData, setComplianceData] = useState({
    overview: {
      total_requirements: 45,
      completed: 38,
      pending: 5,
      overdue: 2,
      compliance_score: 84.4
    },
    deadlines: [],
    forms: [],
    regulations: []
  });
  const [loading, setLoading] = useState(false);
  const [selectedRequirement, setSelectedRequirement] = useState(null);
  const [updateDialog, setUpdateDialog] = useState(false);

  useEffect(() => {
    fetchComplianceData();
  }, []);

  const fetchComplianceData = async () => {
    setLoading(true);
    // Mock data - replace with actual API call
    setTimeout(() => {
      setComplianceData({
        overview: {
          total_requirements: 45,
          completed: 38,
          pending: 5,
          overdue: 2,
          compliance_score: 84.4
        },
        deadlines: [
          {
            id: 1,
            title: 'GST Return Filing - GSTR-3B',
            due_date: '2024-07-20',
            status: 'PENDING',
            priority: 'HIGH',
            client: 'TechCorp Solutions',
            form_type: 'GST',
            days_remaining: 11
          },
          {
            id: 2,
            title: 'TDS Return - Form 24Q',
            due_date: '2024-07-31',
            status: 'IN_PROGRESS',
            priority: 'MEDIUM',
            client: 'Manufacturing Ltd',
            form_type: 'TDS',
            days_remaining: 22
          },
          {
            id: 3,
            title: 'Annual Return - Form AOC-4',
            due_date: '2024-07-15',
            status: 'OVERDUE',
            priority: 'HIGH',
            client: 'Green Energy',
            form_type: 'MCA',
            days_remaining: -5
          }
        ],
        forms: [
          {
            id: 1,
            form_name: 'Form 3CD',
            description: 'Tax Audit Report',
            frequency: 'Annual',
            last_filed: '2023-09-30',
            next_due: '2024-09-30',
            status: 'UPCOMING',
            clients_applicable: 12
          },
          {
            id: 2,
            form_name: 'GSTR-1',
            description: 'GST Return for Outward Supplies',
            frequency: 'Monthly',
            last_filed: '2024-06-11',
            next_due: '2024-07-11',
            status: 'COMPLETED',
            clients_applicable: 25
          },
          {
            id: 3,
            form_name: 'Form 26AS',
            description: 'Tax Credit Statement',
            frequency: 'Quarterly',
            last_filed: '2024-04-15',
            next_due: '2024-07-15',
            status: 'PENDING',
            clients_applicable: 18
          }
        ],
        regulations: [
          {
            id: 1,
            regulation: 'Companies Act 2013',
            section: 'Section 139',
            requirement: 'Appointment of Auditors',
            compliance_status: 'COMPLIANT',
            last_review: '2024-06-01',
            next_review: '2024-12-01'
          },
          {
            id: 2,
            regulation: 'Income Tax Act 1961',
            section: 'Section 44AB',
            requirement: 'Tax Audit Requirement',
            compliance_status: 'COMPLIANT',
            last_review: '2024-05-15',
            next_review: '2024-09-30'
          },
          {
            id: 3,
            regulation: 'GST Act 2017',
            section: 'Section 35',
            requirement: 'GST Audit',
            compliance_status: 'REVIEW_REQUIRED',
            last_review: '2024-03-31',
            next_review: '2024-07-31'
          }
        ]
      });
      setLoading(false);
    }, 1000);
  };

  const getStatusColor = (status) => {
    const colors = {
      'COMPLETED': 'success',
      'COMPLIANT': 'success',
      'IN_PROGRESS': 'primary',
      'PENDING': 'warning',
      'UPCOMING': 'info',
      'OVERDUE': 'error',
      'REVIEW_REQUIRED': 'warning'
    };
    return colors[status] || 'default';
  };

  const getPriorityColor = (priority) => {
    const colors = {
      'HIGH': 'error',
      'MEDIUM': 'warning',
      'LOW': 'success'
    };
    return colors[priority] || 'default';
  };

  const formatDaysRemaining = (days) => {
    if (days < 0) return `${Math.abs(days)} days overdue`;
    if (days === 0) return 'Due today';
    if (days === 1) return '1 day remaining';
    return `${days} days remaining`;
  };

  const complianceScoreData = [
    { name: 'Completed', value: complianceData.overview.completed, color: '#4caf50' },
    { name: 'Pending', value: complianceData.overview.pending, color: '#ff9800' },
    { name: 'Overdue', value: complianceData.overview.overdue, color: '#f44336' }
  ];

  const renderOverviewTab = () => (
    <Grid container spacing={3}>
      {/* Key Metrics */}
      <Grid item xs={12} md={8}>
        <Grid container spacing={2}>
          <Grid item xs={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <ComplianceIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                <Typography variant="h4" color="primary">
                  {complianceData.overview.total_requirements}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Requirements
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <CompleteIcon sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                <Typography variant="h4" color="success.main">
                  {complianceData.overview.completed}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Completed
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <DeadlineIcon sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
                <Typography variant="h4" color="warning.main">
                  {complianceData.overview.pending}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Pending
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <WarningIcon sx={{ fontSize: 40, color: 'error.main', mb: 1 }} />
                <Typography variant="h4" color="error.main">
                  {complianceData.overview.overdue}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Overdue
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Grid>

      {/* Compliance Score */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>Compliance Score</Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Typography variant="h3" color="primary" sx={{ mr: 1 }}>
                {complianceData.overview.compliance_score}%
              </Typography>
              <TrendIcon color="success" />
            </Box>
            <LinearProgress
              variant="determinate"
              value={complianceData.overview.compliance_score}
              sx={{ height: 10, borderRadius: 5 }}
            />
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Based on completed requirements
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* Compliance Distribution Chart */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>Compliance Distribution</Typography>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={complianceScoreData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {complianceScoreData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <RechartsTooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </Grid>

      {/* Upcoming Deadlines */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>Upcoming Deadlines</Typography>
            <List>
              {complianceData.deadlines.slice(0, 5).map((deadline) => (
                <ListItem key={deadline.id}>
                  <ListItemIcon>
                    <Badge
                      badgeContent={Math.abs(deadline.days_remaining)}
                      color={deadline.days_remaining < 0 ? 'error' : deadline.days_remaining <= 7 ? 'warning' : 'primary'}
                    >
                      <DeadlineIcon />
                    </Badge>
                  </ListItemIcon>
                  <ListItemText
                    primary={deadline.title}
                    secondary={`${deadline.client} • ${formatDaysRemaining(deadline.days_remaining)}`}
                  />
                  <ListItemSecondaryAction>
                    <Chip
                      label={deadline.status}
                      color={getStatusColor(deadline.status)}
                      size="small"
                    />
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderDeadlinesTab = () => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">Compliance Deadlines</Typography>
          <Box>
            <Button startIcon={<AddIcon />} variant="outlined" sx={{ mr: 1 }}>
              Add Deadline
            </Button>
            <Button startIcon={<RefreshIcon />} onClick={fetchComplianceData}>
              Refresh
            </Button>
          </Box>
        </Box>
        
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Requirement</TableCell>
                <TableCell>Client</TableCell>
                <TableCell>Due Date</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Priority</TableCell>
                <TableCell>Days Remaining</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {complianceData.deadlines.map((deadline) => (
                <TableRow key={deadline.id}>
                  <TableCell>
                    <Box>
                      <Typography variant="subtitle2">{deadline.title}</Typography>
                      <Chip label={deadline.form_type} size="small" variant="outlined" />
                    </Box>
                  </TableCell>
                  <TableCell>{deadline.client}</TableCell>
                  <TableCell>{new Date(deadline.due_date).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <Chip
                      label={deadline.status}
                      color={getStatusColor(deadline.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={deadline.priority}
                      color={getPriorityColor(deadline.priority)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography
                      variant="body2"
                      color={deadline.days_remaining < 0 ? 'error' : deadline.days_remaining <= 7 ? 'warning.main' : 'text.primary'}
                    >
                      {formatDaysRemaining(deadline.days_remaining)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Tooltip title="Edit">
                      <IconButton size="small">
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Download Form">
                      <IconButton size="small">
                        <DownloadIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </CardContent>
    </Card>
  );

  const renderFormsTab = () => (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>Compliance Forms</Typography>
        <Grid container spacing={2}>
          {complianceData.forms.map((form) => (
            <Grid item xs={12} md={6} lg={4} key={form.id}>
              <Card variant="outlined">
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <FormIcon color="primary" />
                    <Chip
                      label={form.status}
                      color={getStatusColor(form.status)}
                      size="small"
                    />
                  </Box>
                  <Typography variant="h6" gutterBottom>{form.form_name}</Typography>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    {form.description}
                  </Typography>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2">
                      <strong>Frequency:</strong> {form.frequency}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Last Filed:</strong> {new Date(form.last_filed).toLocaleDateString()}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Next Due:</strong> {new Date(form.next_due).toLocaleDateString()}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Applicable Clients:</strong> {form.clients_applicable}
                    </Typography>
                  </Box>
                  <Button variant="outlined" size="small" fullWidth>
                    View Details
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </CardContent>
    </Card>
  );

  const renderRegulationsTab = () => (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>Regulatory Compliance</Typography>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Regulation</TableCell>
                <TableCell>Section</TableCell>
                <TableCell>Requirement</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Last Review</TableCell>
                <TableCell>Next Review</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {complianceData.regulations.map((regulation) => (
                <TableRow key={regulation.id}>
                  <TableCell>
                    <Box>
                      <Typography variant="subtitle2">{regulation.regulation}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {regulation.section}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>{regulation.section}</TableCell>
                  <TableCell>{regulation.requirement}</TableCell>
                  <TableCell>
                    <Chip
                      label={regulation.compliance_status.replace('_', ' ')}
                      color={getStatusColor(regulation.compliance_status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>{new Date(regulation.last_review).toLocaleDateString()}</TableCell>
                  <TableCell>{new Date(regulation.next_review).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <Tooltip title="Review">
                      <IconButton size="small">
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </CardContent>
    </Card>
  );

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Compliance Tracker
      </Typography>

      <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)} sx={{ mb: 3 }}>
        <Tab label="Overview" />
        <Tab label="Deadlines" />
        <Tab label="Forms" />
        <Tab label="Regulations" />
      </Tabs>

      {activeTab === 0 && renderOverviewTab()}
      {activeTab === 1 && renderDeadlinesTab()}
      {activeTab === 2 && renderFormsTab()}
      {activeTab === 3 && renderRegulationsTab()}
    </Box>
  );
};

export default ComplianceTracker;
