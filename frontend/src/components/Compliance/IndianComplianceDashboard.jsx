import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Badge,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Stepper,
  Step,
  StepLabel,
  StepContent
} from '@mui/material';
import {
  AccountBalance as MCAIcon,
  Assignment as FormIcon,
  Schedule as DeadlineIcon,
  Warning as WarningIcon,
  CheckCircle as CompleteIcon,
  Gavel as ComplianceIcon,
  Description as XBRLIcon,
  TrendingUp as TrendIcon,
  Notifications as NotificationIcon,
  FileDownload as DownloadIcon,
  Upload as UploadIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  ExpandMore as ExpandMoreIcon,
  CurrencyRupee as RupeeIcon,
  Business as BusinessIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, LineChart, Line } from 'recharts';

const IndianComplianceDashboard = ({ clientId }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [complianceData, setComplianceData] = useState({
    overview: {
      total_requirements: 28,
      completed: 22,
      pending: 4,
      overdue: 2,
      compliance_score: 78.6
    },
    mca_filings: [],
    xbrl_filings: [],
    roc_compliance: [],
    upcoming_deadlines: []
  });
  const [loading, setLoading] = useState(false);
  const [selectedFiling, setSelectedFiling] = useState(null);
  const [filingDialog, setFilingDialog] = useState(false);

  useEffect(() => {
    fetchComplianceData();
  }, [clientId]);

  const fetchComplianceData = async () => {
    setLoading(true);
    // Mock data - replace with actual API call
    setTimeout(() => {
      setComplianceData({
        overview: {
          total_requirements: 28,
          completed: 22,
          pending: 4,
          overdue: 2,
          compliance_score: 78.6
        },
        mca_filings: [
          {
            id: 1,
            form_number: 'AOC-4',
            form_name: 'Financial Statements',
            due_date: '2024-09-30',
            status: 'PENDING',
            priority: 'HIGH',
            penalty_amount: 0,
            estimated_hours: 8,
            assigned_to: 'CA Rajesh Kumar'
          },
          {
            id: 2,
            form_number: 'MGT-7',
            form_name: 'Annual Return',
            due_date: '2024-08-15',
            status: 'COMPLETED',
            priority: 'HIGH',
            penalty_amount: 0,
            completion_date: '2024-08-10',
            filing_reference: 'MGT7202400123'
          },
          {
            id: 3,
            form_number: 'DIR-12',
            form_name: 'Change in Director',
            due_date: '2024-07-05',
            status: 'OVERDUE',
            priority: 'MEDIUM',
            penalty_amount: 5000,
            days_overdue: 15
          }
        ],
        xbrl_filings: [
          {
            id: 1,
            filing_type: 'ANNUAL',
            reporting_period: 'FY 2023-24',
            due_date: '2024-09-30',
            status: 'DRAFT',
            taxonomy: 'Ind AS Taxonomy v2024',
            validation_score: 85,
            prepared_by: 'CA Priya Sharma'
          },
          {
            id: 2,
            filing_type: 'QUARTERLY',
            reporting_period: 'Q1 2024-25',
            due_date: '2024-08-15',
            status: 'FILED',
            taxonomy: 'Ind AS Taxonomy v2024',
            validation_score: 98,
            filing_reference: 'XBRL202400456'
          }
        ],
        roc_compliance: [
          {
            id: 1,
            compliance_type: 'ANNUAL_RETURN',
            form_number: 'MGT-7',
            due_date: '2024-08-15',
            status: 'FILED',
            srn_number: 'SRN123456789',
            fees_paid: 300
          },
          {
            id: 2,
            compliance_type: 'FINANCIAL_STATEMENTS',
            form_number: 'AOC-4',
            due_date: '2024-09-30',
            status: 'PENDING',
            estimated_fees: 500
          }
        ],
        upcoming_deadlines: [
          {
            id: 1,
            title: 'AOC-4 Filing',
            due_date: '2024-09-30',
            days_remaining: 45,
            priority: 'HIGH',
            type: 'MCA_FILING'
          },
          {
            id: 2,
            title: 'XBRL Annual Filing',
            due_date: '2024-09-30',
            days_remaining: 45,
            priority: 'HIGH',
            type: 'XBRL_FILING'
          },
          {
            id: 3,
            title: 'GST Annual Return',
            due_date: '2024-12-31',
            days_remaining: 137,
            priority: 'MEDIUM',
            type: 'GST_FILING'
          }
        ]
      });
      setLoading(false);
    }, 1000);
  };

  const getStatusColor = (status) => {
    const colors = {
      'COMPLETED': 'success',
      'FILED': 'success',
      'PENDING': 'warning',
      'DRAFT': 'info',
      'OVERDUE': 'error',
      'UNDER_REVIEW': 'primary',
      'APPROVED': 'success',
      'REJECTED': 'error'
    };
    return colors[status] || 'default';
  };

  const getPriorityColor = (priority) => {
    const colors = {
      'HIGH': 'error',
      'MEDIUM': 'warning',
      'LOW': 'success'
    };
    return colors[priority] || 'default';
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const complianceScoreData = [
    { name: 'Completed', value: complianceData.overview.completed, color: '#4caf50' },
    { name: 'Pending', value: complianceData.overview.pending, color: '#ff9800' },
    { name: 'Overdue', value: complianceData.overview.overdue, color: '#f44336' }
  ];

  const renderOverviewTab = () => (
    <Grid container spacing={3}>
      {/* Key Metrics */}
      <Grid item xs={12} md={8}>
        <Grid container spacing={2}>
          <Grid item xs={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <MCAIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                <Typography variant="h4" color="primary">
                  {complianceData.overview.total_requirements}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Requirements
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <CompleteIcon sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                <Typography variant="h4" color="success.main">
                  {complianceData.overview.completed}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Completed
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <DeadlineIcon sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
                <Typography variant="h4" color="warning.main">
                  {complianceData.overview.pending}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Pending
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <WarningIcon sx={{ fontSize: 40, color: 'error.main', mb: 1 }} />
                <Typography variant="h4" color="error.main">
                  {complianceData.overview.overdue}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Overdue
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Grid>

      {/* Compliance Score */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>Indian Compliance Score</Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Typography variant="h3" color="primary" sx={{ mr: 1 }}>
                {complianceData.overview.compliance_score}%
              </Typography>
              <TrendIcon color="success" />
            </Box>
            <LinearProgress
              variant="determinate"
              value={complianceData.overview.compliance_score}
              sx={{ height: 10, borderRadius: 5 }}
            />
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              MCA, ROC, XBRL & Other Compliances
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* Compliance Distribution */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>Compliance Status Distribution</Typography>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={complianceScoreData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {complianceScoreData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <RechartsTooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </Grid>

      {/* Upcoming Deadlines */}
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>Upcoming Deadlines</Typography>
            <List>
              {complianceData.upcoming_deadlines.slice(0, 5).map((deadline) => (
                <ListItem key={deadline.id}>
                  <ListItemIcon>
                    <Badge
                      badgeContent={deadline.days_remaining}
                      color={deadline.days_remaining <= 7 ? 'error' : deadline.days_remaining <= 30 ? 'warning' : 'primary'}
                    >
                      <DeadlineIcon />
                    </Badge>
                  </ListItemIcon>
                  <ListItemText
                    primary={deadline.title}
                    secondary={`Due: ${new Date(deadline.due_date).toLocaleDateString()} • ${deadline.days_remaining} days remaining`}
                  />
                  <ListItemSecondaryAction>
                    <Chip
                      label={deadline.type}
                      size="small"
                      variant="outlined"
                    />
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderMCAFilingsTab = () => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">MCA Filings</Typography>
          <Box>
            <Button startIcon={<AddIcon />} variant="outlined" sx={{ mr: 1 }}>
              New Filing
            </Button>
            <Button startIcon={<RefreshIcon />} onClick={fetchComplianceData}>
              Refresh
            </Button>
          </Box>
        </Box>
        
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Form</TableCell>
                <TableCell>Due Date</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Priority</TableCell>
                <TableCell>Penalty</TableCell>
                <TableCell>Assigned To</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {complianceData.mca_filings.map((filing) => (
                <TableRow key={filing.id}>
                  <TableCell>
                    <Box>
                      <Typography variant="subtitle2">{filing.form_number}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {filing.form_name}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {new Date(filing.due_date).toLocaleDateString()}
                    </Typography>
                    {filing.days_overdue && (
                      <Typography variant="caption" color="error">
                        {filing.days_overdue} days overdue
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={filing.status}
                      color={getStatusColor(filing.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={filing.priority}
                      color={getPriorityColor(filing.priority)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {filing.penalty_amount > 0 ? (
                      <Typography variant="body2" color="error">
                        {formatCurrency(filing.penalty_amount)}
                      </Typography>
                    ) : (
                      <Typography variant="body2" color="success.main">
                        No Penalty
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {filing.assigned_to || 'Unassigned'}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Tooltip title="View Details">
                      <IconButton size="small" onClick={() => {
                        setSelectedFiling(filing);
                        setFilingDialog(true);
                      }}>
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Edit">
                      <IconButton size="small">
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Download Form">
                      <IconButton size="small">
                        <DownloadIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </CardContent>
    </Card>
  );

  const renderXBRLFilingsTab = () => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">XBRL Filings</Typography>
          <Box>
            <Button startIcon={<AddIcon />} variant="outlined" sx={{ mr: 1 }}>
              Create XBRL
            </Button>
            <Button startIcon={<UploadIcon />} variant="outlined" sx={{ mr: 1 }}>
              Upload Instance
            </Button>
            <Button startIcon={<RefreshIcon />} onClick={fetchComplianceData}>
              Refresh
            </Button>
          </Box>
        </Box>
        
        <Grid container spacing={2}>
          {complianceData.xbrl_filings.map((filing) => (
            <Grid item xs={12} md={6} key={filing.id}>
              <Card variant="outlined">
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <XBRLIcon color="primary" />
                    <Chip
                      label={filing.status}
                      color={getStatusColor(filing.status)}
                      size="small"
                    />
                  </Box>
                  <Typography variant="h6" gutterBottom>
                    {filing.filing_type} Filing
                  </Typography>
                  <Typography variant="body2" color="text.secondary" paragraph>
                    {filing.reporting_period}
                  </Typography>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2">
                      <strong>Due Date:</strong> {new Date(filing.due_date).toLocaleDateString()}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Taxonomy:</strong> {filing.taxonomy}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Prepared By:</strong> {filing.prepared_by}
                    </Typography>
                    {filing.filing_reference && (
                      <Typography variant="body2">
                        <strong>Reference:</strong> {filing.filing_reference}
                      </Typography>
                    )}
                  </Box>
                  
                  {filing.validation_score && (
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" gutterBottom>
                        Validation Score: {filing.validation_score}%
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={filing.validation_score}
                        color={filing.validation_score >= 90 ? 'success' : filing.validation_score >= 70 ? 'warning' : 'error'}
                        sx={{ height: 6, borderRadius: 3 }}
                      />
                    </Box>
                  )}
                  
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button variant="outlined" size="small" fullWidth>
                      View Details
                    </Button>
                    {filing.status === 'DRAFT' && (
                      <Button variant="contained" size="small" fullWidth>
                        Validate
                      </Button>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </CardContent>
    </Card>
  );

  const renderROCComplianceTab = () => (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>ROC Compliance Status</Typography>
        
        <Stepper orientation="vertical">
          {complianceData.roc_compliance.map((compliance, index) => (
            <Step key={compliance.id} active={true} completed={compliance.status === 'FILED'}>
              <StepLabel>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="subtitle1">
                    {compliance.compliance_type.replace('_', ' ')}
                  </Typography>
                  <Chip
                    label={compliance.status}
                    color={getStatusColor(compliance.status)}
                    size="small"
                  />
                </Box>
              </StepLabel>
              <StepContent>
                <Box sx={{ pl: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    <strong>Form:</strong> {compliance.form_number}
                  </Typography>
                  <Typography variant="body2" gutterBottom>
                    <strong>Due Date:</strong> {new Date(compliance.due_date).toLocaleDateString()}
                  </Typography>
                  {compliance.srn_number && (
                    <Typography variant="body2" gutterBottom>
                      <strong>SRN:</strong> {compliance.srn_number}
                    </Typography>
                  )}
                  <Typography variant="body2" gutterBottom>
                    <strong>Fees:</strong> {formatCurrency(compliance.fees_paid || compliance.estimated_fees)}
                    {compliance.estimated_fees && ' (Estimated)'}
                  </Typography>
                  
                  <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                    <Button variant="outlined" size="small">
                      View Details
                    </Button>
                    {compliance.status === 'PENDING' && (
                      <Button variant="contained" size="small">
                        File Now
                      </Button>
                    )}
                  </Box>
                </Box>
              </StepContent>
            </Step>
          ))}
        </Stepper>
      </CardContent>
    </Card>
  );

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Indian Compliance Dashboard
      </Typography>

      <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)} sx={{ mb: 3 }}>
        <Tab label="Overview" />
        <Tab label="MCA Filings" />
        <Tab label="XBRL Filings" />
        <Tab label="ROC Compliance" />
      </Tabs>

      {loading && <LinearProgress sx={{ mb: 2 }} />}

      {activeTab === 0 && renderOverviewTab()}
      {activeTab === 1 && renderMCAFilingsTab()}
      {activeTab === 2 && renderXBRLFilingsTab()}
      {activeTab === 3 && renderROCComplianceTab()}

      {/* Filing Details Dialog */}
      <Dialog open={filingDialog} onClose={() => setFilingDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Filing Details: {selectedFiling?.form_number}
        </DialogTitle>
        <DialogContent>
          {selectedFiling && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" gutterBottom>Form Information</Typography>
                <Typography variant="body2">
                  <strong>Form Number:</strong> {selectedFiling.form_number}
                </Typography>
                <Typography variant="body2">
                  <strong>Form Name:</strong> {selectedFiling.form_name}
                </Typography>
                <Typography variant="body2">
                  <strong>Due Date:</strong> {new Date(selectedFiling.due_date).toLocaleDateString()}
                </Typography>
                <Typography variant="body2">
                  <strong>Status:</strong> {selectedFiling.status}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" gutterBottom>Assignment & Progress</Typography>
                <Typography variant="body2">
                  <strong>Assigned To:</strong> {selectedFiling.assigned_to || 'Unassigned'}
                </Typography>
                <Typography variant="body2">
                  <strong>Estimated Hours:</strong> {selectedFiling.estimated_hours}
                </Typography>
                <Typography variant="body2">
                  <strong>Priority:</strong> {selectedFiling.priority}
                </Typography>
                {selectedFiling.penalty_amount > 0 && (
                  <Typography variant="body2" color="error">
                    <strong>Penalty:</strong> {formatCurrency(selectedFiling.penalty_amount)}
                  </Typography>
                )}
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setFilingDialog(false)}>Close</Button>
          <Button variant="contained">Edit Filing</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default IndianComplianceDashboard;
