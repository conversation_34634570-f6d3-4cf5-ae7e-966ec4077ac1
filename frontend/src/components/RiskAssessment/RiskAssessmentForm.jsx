import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Stepper,
  Step,
  StepLabel,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Slider,
  FormControlLabel,
  Switch
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Assessment as AssessmentIcon,
  TrendingUp as TrendingUpIcon,
  Security as SecurityIcon,
  SmartToy as AIIcon,
  Save as SaveIcon,
  Send as SendIcon
} from '@mui/icons-material';
import { riskAssessmentService } from '../../services/riskAssessmentService';

const RiskAssessmentForm = ({ engagementId, onComplete }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [riskAssessment, setRiskAssessment] = useState({
    engagement: engagementId,
    inherent_risk: 'MEDIUM',
    control_risk: 'MEDIUM',
    detection_risk: 'MEDIUM',
    overall_audit_risk: 'MEDIUM',
    overall_materiality: '',
    performance_materiality: '',
    trivial_threshold: '',
    materiality_basis: 'REVENUE',
    assessment_date: new Date().toISOString().split('T')[0],
    status: 'DRAFT'
  });
  const [riskFactors, setRiskFactors] = useState([]);
  const [materialityData, setMaterialityData] = useState({
    revenue: '',
    net_income: '',
    total_assets: '',
    auto_calculate: true
  });
  const [openFactorDialog, setOpenFactorDialog] = useState(false);
  const [currentFactor, setCurrentFactor] = useState({
    category: 'FINANCIAL',
    factor_name: '',
    description: '',
    likelihood: 3,
    impact: 3,
    risk_level: 'MEDIUM',
    existing_controls: '',
    control_effectiveness: 'MEDIUM',
    residual_risk: 'MEDIUM',
    audit_response: '',
    testing_approach: '',
    sample_size: ''
  });
  const [loading, setLoading] = useState(false);

  const steps = [
    'Basic Assessment',
    'Risk Factors',
    'Materiality Calculation',
    'Review & Submit'
  ];

  const riskLevels = [
    { value: 'LOW', label: 'Low', color: 'success' },
    { value: 'MEDIUM', label: 'Medium', color: 'warning' },
    { value: 'HIGH', label: 'High', color: 'error' }
  ];

  const riskCategories = [
    { value: 'FINANCIAL', label: 'Financial Reporting' },
    { value: 'OPERATIONAL', label: 'Operational' },
    { value: 'COMPLIANCE', label: 'Compliance' },
    { value: 'FRAUD', label: 'Fraud Risk' },
    { value: 'IT', label: 'Information Technology' },
    { value: 'ENVIRONMENTAL', label: 'Environmental' }
  ];

  useEffect(() => {
    if (materialityData.auto_calculate) {
      calculateMateriality();
    }
  }, [materialityData.revenue, materialityData.net_income, materialityData.total_assets, riskAssessment.overall_audit_risk]);

  const calculateMateriality = () => {
    const { revenue, net_income, total_assets } = materialityData;
    const riskLevel = riskAssessment.overall_audit_risk;
    
    let baseAmount = 0;
    let percentage = 0.05; // Default 5%

    // Determine base amount and percentage based on risk level
    if (revenue && parseFloat(revenue) > 0) {
      baseAmount = parseFloat(revenue);
      percentage = riskLevel === 'HIGH' ? 0.03 : riskLevel === 'MEDIUM' ? 0.05 : 0.07;
    } else if (net_income && parseFloat(net_income) > 0) {
      baseAmount = parseFloat(net_income);
      percentage = riskLevel === 'HIGH' ? 0.03 : riskLevel === 'MEDIUM' ? 0.05 : 0.10;
    } else if (total_assets && parseFloat(total_assets) > 0) {
      baseAmount = parseFloat(total_assets);
      percentage = riskLevel === 'HIGH' ? 0.005 : riskLevel === 'MEDIUM' ? 0.01 : 0.02;
    }

    if (baseAmount > 0) {
      const materiality = baseAmount * percentage;
      const performanceMateriality = materiality * 0.75; // 75% of overall materiality
      const trivialThreshold = materiality * 0.05; // 5% of overall materiality

      setRiskAssessment(prev => ({
        ...prev,
        overall_materiality: Math.round(materiality).toString(),
        performance_materiality: Math.round(performanceMateriality).toString(),
        trivial_threshold: Math.round(trivialThreshold).toString()
      }));
    }
  };

  const calculateOverallRisk = () => {
    const { inherent_risk, control_risk, detection_risk } = riskAssessment;
    const riskValues = { LOW: 1, MEDIUM: 2, HIGH: 3 };
    
    const inherentValue = riskValues[inherent_risk];
    const controlValue = riskValues[control_risk];
    const detectionValue = riskValues[detection_risk];
    
    const overallValue = (inherentValue + controlValue + detectionValue) / 3;
    
    let overallRisk = 'LOW';
    if (overallValue >= 2.5) overallRisk = 'HIGH';
    else if (overallValue >= 1.5) overallRisk = 'MEDIUM';
    
    setRiskAssessment(prev => ({ ...prev, overall_audit_risk: overallRisk }));
  };

  const addRiskFactor = () => {
    const riskScore = currentFactor.likelihood * currentFactor.impact;
    let riskLevel = 'LOW';
    if (riskScore >= 12) riskLevel = 'HIGH';
    else if (riskScore >= 6) riskLevel = 'MEDIUM';

    const newFactor = {
      ...currentFactor,
      id: Date.now(),
      risk_level: riskLevel
    };

    setRiskFactors(prev => [...prev, newFactor]);
    setCurrentFactor({
      category: 'FINANCIAL',
      factor_name: '',
      description: '',
      likelihood: 3,
      impact: 3,
      risk_level: 'MEDIUM',
      existing_controls: '',
      control_effectiveness: 'MEDIUM',
      residual_risk: 'MEDIUM',
      audit_response: '',
      testing_approach: '',
      sample_size: ''
    });
    setOpenFactorDialog(false);
  };

  const removeRiskFactor = (id) => {
    setRiskFactors(prev => prev.filter(factor => factor.id !== id));
  };

  const handleNext = () => {
    if (activeStep === 0) {
      calculateOverallRisk();
    }
    setActiveStep(prev => prev + 1);
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      
      const assessmentData = {
        ...riskAssessment,
        risk_factors: riskFactors.map(factor => ({
          category: factor.category,
          factor_name: factor.factor_name,
          description: factor.description,
          likelihood: factor.likelihood,
          impact: factor.impact,
          risk_level: factor.risk_level,
          existing_controls: factor.existing_controls,
          control_effectiveness: factor.control_effectiveness,
          residual_risk: factor.residual_risk,
          audit_response: factor.audit_response,
          testing_approach: factor.testing_approach,
          sample_size: factor.sample_size
        }))
      };

      const response = await riskAssessmentService.createAssessment(assessmentData);
      
      if (onComplete) {
        onComplete(response.data);
      }
    } catch (error) {
      console.error('Error submitting risk assessment:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderBasicAssessment = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom>
          <AssessmentIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Basic Risk Assessment
        </Typography>
      </Grid>
      
      <Grid item xs={12} md={4}>
        <FormControl fullWidth>
          <InputLabel>Inherent Risk</InputLabel>
          <Select
            value={riskAssessment.inherent_risk}
            onChange={(e) => setRiskAssessment(prev => ({ ...prev, inherent_risk: e.target.value }))}
          >
            {riskLevels.map(level => (
              <MenuItem key={level.value} value={level.value}>
                <Chip label={level.label} color={level.color} size="small" sx={{ mr: 1 }} />
                {level.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>

      <Grid item xs={12} md={4}>
        <FormControl fullWidth>
          <InputLabel>Control Risk</InputLabel>
          <Select
            value={riskAssessment.control_risk}
            onChange={(e) => setRiskAssessment(prev => ({ ...prev, control_risk: e.target.value }))}
          >
            {riskLevels.map(level => (
              <MenuItem key={level.value} value={level.value}>
                <Chip label={level.label} color={level.color} size="small" sx={{ mr: 1 }} />
                {level.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>

      <Grid item xs={12} md={4}>
        <FormControl fullWidth>
          <InputLabel>Detection Risk</InputLabel>
          <Select
            value={riskAssessment.detection_risk}
            onChange={(e) => setRiskAssessment(prev => ({ ...prev, detection_risk: e.target.value }))}
          >
            {riskLevels.map(level => (
              <MenuItem key={level.value} value={level.value}>
                <Chip label={level.label} color={level.color} size="small" sx={{ mr: 1 }} />
                {level.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>

      <Grid item xs={12}>
        <Alert severity="info">
          <Typography variant="subtitle2">Overall Audit Risk: </Typography>
          <Chip 
            label={riskAssessment.overall_audit_risk} 
            color={riskLevels.find(l => l.value === riskAssessment.overall_audit_risk)?.color} 
          />
        </Alert>
      </Grid>
    </Grid>
  );

  const renderRiskFactors = () => (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          <SecurityIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Risk Factors
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setOpenFactorDialog(true)}
        >
          Add Risk Factor
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Risk Factor</TableCell>
              <TableCell>Category</TableCell>
              <TableCell>Likelihood</TableCell>
              <TableCell>Impact</TableCell>
              <TableCell>Risk Level</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {riskFactors.map((factor) => (
              <TableRow key={factor.id}>
                <TableCell>
                  <Typography variant="subtitle2">{factor.factor_name}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    {factor.description}
                  </Typography>
                </TableCell>
                <TableCell>{factor.category}</TableCell>
                <TableCell>{factor.likelihood}/5</TableCell>
                <TableCell>{factor.impact}/5</TableCell>
                <TableCell>
                  <Chip 
                    label={factor.risk_level} 
                    color={riskLevels.find(l => l.value === factor.risk_level)?.color}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <IconButton onClick={() => removeRiskFactor(factor.id)} color="error">
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Risk Factor Dialog */}
      <Dialog open={openFactorDialog} onClose={() => setOpenFactorDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Add Risk Factor</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Category</InputLabel>
                <Select
                  value={currentFactor.category}
                  onChange={(e) => setCurrentFactor(prev => ({ ...prev, category: e.target.value }))}
                >
                  {riskCategories.map(cat => (
                    <MenuItem key={cat.value} value={cat.value}>{cat.label}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Risk Factor Name"
                value={currentFactor.factor_name}
                onChange={(e) => setCurrentFactor(prev => ({ ...prev, factor_name: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Description"
                value={currentFactor.description}
                onChange={(e) => setCurrentFactor(prev => ({ ...prev, description: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography gutterBottom>Likelihood (1-5)</Typography>
              <Slider
                value={currentFactor.likelihood}
                onChange={(e, value) => setCurrentFactor(prev => ({ ...prev, likelihood: value }))}
                min={1}
                max={5}
                marks
                valueLabelDisplay="auto"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography gutterBottom>Impact (1-5)</Typography>
              <Slider
                value={currentFactor.impact}
                onChange={(e, value) => setCurrentFactor(prev => ({ ...prev, impact: value }))}
                min={1}
                max={5}
                marks
                valueLabelDisplay="auto"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={2}
                label="Audit Response"
                value={currentFactor.audit_response}
                onChange={(e) => setCurrentFactor(prev => ({ ...prev, audit_response: e.target.value }))}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenFactorDialog(false)}>Cancel</Button>
          <Button onClick={addRiskFactor} variant="contained">Add Factor</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );

  const renderMaterialityCalculation = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom>
          <TrendingUpIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Materiality Calculation
        </Typography>
      </Grid>

      <Grid item xs={12}>
        <FormControlLabel
          control={
            <Switch
              checked={materialityData.auto_calculate}
              onChange={(e) => setMaterialityData(prev => ({ ...prev, auto_calculate: e.target.checked }))}
            />
          }
          label="Auto-calculate materiality based on financial data"
        />
      </Grid>

      {materialityData.auto_calculate && (
        <>
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="Revenue"
              type="number"
              value={materialityData.revenue}
              onChange={(e) => setMaterialityData(prev => ({ ...prev, revenue: e.target.value }))}
              InputProps={{ startAdornment: '₹' }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="Net Income"
              type="number"
              value={materialityData.net_income}
              onChange={(e) => setMaterialityData(prev => ({ ...prev, net_income: e.target.value }))}
              InputProps={{ startAdornment: '₹' }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              label="Total Assets"
              type="number"
              value={materialityData.total_assets}
              onChange={(e) => setMaterialityData(prev => ({ ...prev, total_assets: e.target.value }))}
              InputProps={{ startAdornment: '₹' }}
            />
          </Grid>
        </>
      )}

      <Grid item xs={12} md={4}>
        <TextField
          fullWidth
          label="Overall Materiality"
          type="number"
          value={riskAssessment.overall_materiality}
          onChange={(e) => setRiskAssessment(prev => ({ ...prev, overall_materiality: e.target.value }))}
          InputProps={{ startAdornment: '₹' }}
          disabled={materialityData.auto_calculate}
        />
      </Grid>
      <Grid item xs={12} md={4}>
        <TextField
          fullWidth
          label="Performance Materiality"
          type="number"
          value={riskAssessment.performance_materiality}
          onChange={(e) => setRiskAssessment(prev => ({ ...prev, performance_materiality: e.target.value }))}
          InputProps={{ startAdornment: '₹' }}
          disabled={materialityData.auto_calculate}
        />
      </Grid>
      <Grid item xs={12} md={4}>
        <TextField
          fullWidth
          label="Trivial Threshold"
          type="number"
          value={riskAssessment.trivial_threshold}
          onChange={(e) => setRiskAssessment(prev => ({ ...prev, trivial_threshold: e.target.value }))}
          InputProps={{ startAdornment: '₹' }}
          disabled={materialityData.auto_calculate}
        />
      </Grid>

      <Grid item xs={12}>
        <FormControl fullWidth>
          <InputLabel>Materiality Basis</InputLabel>
          <Select
            value={riskAssessment.materiality_basis}
            onChange={(e) => setRiskAssessment(prev => ({ ...prev, materiality_basis: e.target.value }))}
          >
            <MenuItem value="REVENUE">Revenue</MenuItem>
            <MenuItem value="NET_INCOME">Net Income</MenuItem>
            <MenuItem value="TOTAL_ASSETS">Total Assets</MenuItem>
            <MenuItem value="EQUITY">Equity</MenuItem>
          </Select>
        </FormControl>
      </Grid>
    </Grid>
  );

  const renderReview = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom>Review Risk Assessment</Typography>
      </Grid>
      
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="subtitle1" gutterBottom>Risk Levels</Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography>Inherent Risk:</Typography>
                <Chip label={riskAssessment.inherent_risk} color={riskLevels.find(l => l.value === riskAssessment.inherent_risk)?.color} size="small" />
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography>Control Risk:</Typography>
                <Chip label={riskAssessment.control_risk} color={riskLevels.find(l => l.value === riskAssessment.control_risk)?.color} size="small" />
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography>Detection Risk:</Typography>
                <Chip label={riskAssessment.detection_risk} color={riskLevels.find(l => l.value === riskAssessment.detection_risk)?.color} size="small" />
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography><strong>Overall Risk:</strong></Typography>
                <Chip label={riskAssessment.overall_audit_risk} color={riskLevels.find(l => l.value === riskAssessment.overall_audit_risk)?.color} />
              </Box>
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="subtitle1" gutterBottom>Materiality Amounts</Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography>Overall Materiality:</Typography>
                <Typography>₹{parseInt(riskAssessment.overall_materiality || 0).toLocaleString()}</Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography>Performance Materiality:</Typography>
                <Typography>₹{parseInt(riskAssessment.performance_materiality || 0).toLocaleString()}</Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography>Trivial Threshold:</Typography>
                <Typography>₹{parseInt(riskAssessment.trivial_threshold || 0).toLocaleString()}</Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12}>
        <Typography variant="subtitle1" gutterBottom>
          Risk Factors Summary ({riskFactors.length} factors identified)
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {riskFactors.map(factor => (
            <Chip
              key={factor.id}
              label={`${factor.factor_name} (${factor.risk_level})`}
              color={riskLevels.find(l => l.value === factor.risk_level)?.color}
              size="small"
            />
          ))}
        </Box>
      </Grid>
    </Grid>
  );

  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return renderBasicAssessment();
      case 1:
        return renderRiskFactors();
      case 2:
        return renderMaterialityCalculation();
      case 3:
        return renderReview();
      default:
        return 'Unknown step';
    }
  };

  return (
    <Box>
      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>

      <Card>
        <CardContent>
          {getStepContent(activeStep)}
        </CardContent>
      </Card>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
        <Button
          disabled={activeStep === 0}
          onClick={handleBack}
        >
          Back
        </Button>
        <Box>
          {activeStep === steps.length - 1 ? (
            <Button
              variant="contained"
              onClick={handleSubmit}
              disabled={loading}
              startIcon={<SendIcon />}
            >
              Submit Assessment
            </Button>
          ) : (
            <Button
              variant="contained"
              onClick={handleNext}
              startIcon={activeStep === steps.length - 2 ? <SaveIcon /> : undefined}
            >
              {activeStep === steps.length - 2 ? 'Save & Review' : 'Next'}
            </Button>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default RiskAssessmentForm;
