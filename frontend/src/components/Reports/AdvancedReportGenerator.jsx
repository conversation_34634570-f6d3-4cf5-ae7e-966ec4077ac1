import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  Alert,
  Divider,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Description as ReportIcon,
  Settings as SettingsIcon,
  Preview as PreviewIcon,
  Download as DownloadIcon,
  Send as SendIcon,
  ExpandMore as ExpandMoreIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  PictureAsPdf as PdfIcon,
  TableChart as ExcelIcon,
  Description as WordIcon,
  Email as EmailIcon,
  Schedule as ScheduleIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';

const AdvancedReportGenerator = ({ engagementId }) => {
  const [activeStep, setActiveStep] = useState(0);
  const [reportConfig, setReportConfig] = useState({
    type: '',
    format: 'PDF',
    template: '',
    sections: {},
    customizations: {},
    distribution: {
      email_recipients: [],
      auto_send: false,
      schedule: null
    }
  });
  const [availableReports, setAvailableReports] = useState([]);
  const [templates, setTemplates] = useState([]);
  const [generating, setGenerating] = useState(false);
  const [previewDialog, setPreviewDialog] = useState(false);
  const [generatedReport, setGeneratedReport] = useState(null);

  const steps = [
    'Select Report Type',
    'Configure Sections',
    'Customize Format',
    'Preview & Generate'
  ];

  const reportTypes = [
    {
      id: 'AUDIT_REPORT',
      name: 'Audit Report',
      description: 'Comprehensive audit report with opinion and findings',
      icon: <ReportIcon />,
      formats: ['PDF', 'WORD'],
      sections: [
        { id: 'executive_summary', name: 'Executive Summary', required: true },
        { id: 'audit_opinion', name: 'Audit Opinion', required: true },
        { id: 'key_audit_matters', name: 'Key Audit Matters', required: false },
        { id: 'financial_statements', name: 'Financial Statements', required: true },
        { id: 'notes', name: 'Notes to Financial Statements', required: true },
        { id: 'management_letter', name: 'Management Letter', required: false },
        { id: 'appendices', name: 'Appendices', required: false }
      ]
    },
    {
      id: 'FORM_3CD',
      name: 'Form 3CD',
      description: 'Tax audit report under Section 44AB',
      icon: <ReportIcon />,
      formats: ['PDF', 'EXCEL'],
      sections: [
        { id: 'part_a', name: 'Part A - General Information', required: true },
        { id: 'part_b', name: 'Part B - Profit & Loss Account', required: true },
        { id: 'part_c', name: 'Part C - Balance Sheet', required: true },
        { id: 'quantitative_details', name: 'Quantitative Details', required: true },
        { id: 'tax_audit_report', name: 'Tax Audit Report', required: true }
      ]
    },
    {
      id: 'CARO_2020',
      name: 'CARO 2020 Report',
      description: 'Companies (Auditor\'s Report) Order 2020',
      icon: <ReportIcon />,
      formats: ['PDF', 'WORD'],
      sections: [
        { id: 'fixed_assets', name: 'Fixed Assets', required: true },
        { id: 'inventory', name: 'Inventory', required: true },
        { id: 'investments', name: 'Investments', required: true },
        { id: 'loans_advances', name: 'Loans and Advances', required: true },
        { id: 'internal_controls', name: 'Internal Controls', required: true },
        { id: 'statutory_dues', name: 'Statutory Dues', required: true }
      ]
    },
    {
      id: 'MANAGEMENT_LETTER',
      name: 'Management Letter',
      description: 'Letter to management with recommendations',
      icon: <EmailIcon />,
      formats: ['PDF', 'WORD'],
      sections: [
        { id: 'introduction', name: 'Introduction', required: true },
        { id: 'observations', name: 'Observations', required: true },
        { id: 'recommendations', name: 'Recommendations', required: true },
        { id: 'management_responses', name: 'Management Responses', required: false }
      ]
    }
  ];

  useEffect(() => {
    setAvailableReports(reportTypes);
    // Initialize sections for selected report type
    if (reportConfig.type) {
      const selectedReport = reportTypes.find(r => r.id === reportConfig.type);
      if (selectedReport) {
        const sections = {};
        selectedReport.sections.forEach(section => {
          sections[section.id] = section.required;
        });
        setReportConfig(prev => ({ ...prev, sections }));
      }
    }
  }, [reportConfig.type]);

  const handleReportTypeSelect = (reportType) => {
    setReportConfig(prev => ({
      ...prev,
      type: reportType.id,
      format: reportType.formats[0]
    }));
  };

  const handleSectionToggle = (sectionId) => {
    setReportConfig(prev => ({
      ...prev,
      sections: {
        ...prev.sections,
        [sectionId]: !prev.sections[sectionId]
      }
    }));
  };

  const generateReport = async () => {
    setGenerating(true);
    try {
      // Mock API call - replace with actual implementation
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      setGeneratedReport({
        id: 'RPT-2024-001',
        name: `${reportConfig.type}_${new Date().toISOString().split('T')[0]}`,
        format: reportConfig.format,
        size: '2.4 MB',
        pages: 45,
        generated_at: new Date().toISOString(),
        download_url: '/api/reports/download/RPT-2024-001'
      });
      
      setActiveStep(3);
    } catch (error) {
      console.error('Error generating report:', error);
    } finally {
      setGenerating(false);
    }
  };

  const getFormatIcon = (format) => {
    const icons = {
      'PDF': <PdfIcon />,
      'WORD': <WordIcon />,
      'EXCEL': <ExcelIcon />
    };
    return icons[format] || <ReportIcon />;
  };

  const renderReportTypeSelection = () => {
    const selectedReport = reportTypes.find(r => r.id === reportConfig.type);
    
    return (
      <Grid container spacing={3}>
        {reportTypes.map((report) => (
          <Grid item xs={12} md={6} key={report.id}>
            <Card
              sx={{
                cursor: 'pointer',
                border: reportConfig.type === report.id ? 2 : 1,
                borderColor: reportConfig.type === report.id ? 'primary.main' : 'divider',
                '&:hover': { borderColor: 'primary.main' }
              }}
              onClick={() => handleReportTypeSelect(report)}
            >
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  {report.icon}
                  <Typography variant="h6" sx={{ ml: 1 }}>
                    {report.name}
                  </Typography>
                  {reportConfig.type === report.id && (
                    <CheckIcon color="primary" sx={{ ml: 'auto' }} />
                  )}
                </Box>
                <Typography variant="body2" color="text.secondary" paragraph>
                  {report.description}
                </Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  {report.formats.map(format => (
                    <Chip
                      key={format}
                      label={format}
                      size="small"
                      variant="outlined"
                      icon={getFormatIcon(format)}
                    />
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    );
  };

  const renderSectionConfiguration = () => {
    const selectedReport = reportTypes.find(r => r.id === reportConfig.type);
    if (!selectedReport) return null;

    return (
      <Box>
        <Typography variant="h6" gutterBottom>
          Configure Report Sections
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          Select which sections to include in your {selectedReport.name}
        </Typography>
        
        <List>
          {selectedReport.sections.map((section) => (
            <ListItem key={section.id}>
              <ListItemIcon>
                {section.required ? <CheckIcon color="primary" /> : <InfoIcon />}
              </ListItemIcon>
              <ListItemText
                primary={section.name}
                secondary={section.required ? 'Required section' : 'Optional section'}
              />
              <ListItemSecondaryAction>
                <Switch
                  checked={reportConfig.sections[section.id] || false}
                  onChange={() => handleSectionToggle(section.id)}
                  disabled={section.required}
                />
              </ListItemSecondaryAction>
            </ListItem>
          ))}
        </List>

        <Divider sx={{ my: 3 }} />

        <Typography variant="h6" gutterBottom>
          Additional Options
        </Typography>
        
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>Report Format</InputLabel>
              <Select
                value={reportConfig.format}
                onChange={(e) => setReportConfig(prev => ({ ...prev, format: e.target.value }))}
              >
                {selectedReport.formats.map(format => (
                  <MenuItem key={format} value={format}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {getFormatIcon(format)}
                      {format}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>Template</InputLabel>
              <Select
                value={reportConfig.template}
                onChange={(e) => setReportConfig(prev => ({ ...prev, template: e.target.value }))}
              >
                <MenuItem value="standard">Standard Template</MenuItem>
                <MenuItem value="detailed">Detailed Template</MenuItem>
                <MenuItem value="summary">Summary Template</MenuItem>
                <MenuItem value="custom">Custom Template</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Box>
    );
  };

  const renderCustomizations = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Report Customizations
      </Typography>
      
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography>Header & Footer</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Report Title"
                value={reportConfig.customizations.title || ''}
                onChange={(e) => setReportConfig(prev => ({
                  ...prev,
                  customizations: { ...prev.customizations, title: e.target.value }
                }))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Company Logo URL"
                value={reportConfig.customizations.logo || ''}
                onChange={(e) => setReportConfig(prev => ({
                  ...prev,
                  customizations: { ...prev.customizations, logo: e.target.value }
                }))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Footer Text"
                value={reportConfig.customizations.footer || ''}
                onChange={(e) => setReportConfig(prev => ({
                  ...prev,
                  customizations: { ...prev.customizations, footer: e.target.value }
                }))}
              />
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography>Distribution Settings</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Email Recipients"
                placeholder="Enter email addresses separated by commas"
                value={reportConfig.distribution.email_recipients.join(', ')}
                onChange={(e) => setReportConfig(prev => ({
                  ...prev,
                  distribution: {
                    ...prev.distribution,
                    email_recipients: e.target.value.split(',').map(email => email.trim())
                  }
                }))}
              />
            </Grid>
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Switch
                  checked={reportConfig.distribution.auto_send}
                  onChange={(e) => setReportConfig(prev => ({
                    ...prev,
                    distribution: { ...prev.distribution, auto_send: e.target.checked }
                  }))}
                />
                <Typography sx={{ ml: 1 }}>
                  Automatically send report after generation
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>
    </Box>
  );

  const renderPreviewAndGenerate = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Report Summary
      </Typography>
      
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>Report Type</Typography>
              <Typography variant="body1">
                {reportTypes.find(r => r.id === reportConfig.type)?.name}
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" gutterBottom>Format</Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {getFormatIcon(reportConfig.format)}
                <Typography variant="body1">{reportConfig.format}</Typography>
              </Box>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>Included Sections</Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {Object.entries(reportConfig.sections)
                  .filter(([_, included]) => included)
                  .map(([sectionId, _]) => {
                    const section = reportTypes
                      .find(r => r.id === reportConfig.type)
                      ?.sections.find(s => s.id === sectionId);
                    return (
                      <Chip
                        key={sectionId}
                        label={section?.name}
                        size="small"
                        color={section?.required ? 'primary' : 'default'}
                      />
                    );
                  })}
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {generating && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="body2" gutterBottom>
            Generating report... This may take a few minutes.
          </Typography>
          <LinearProgress />
        </Box>
      )}

      {generatedReport && (
        <Alert severity="success" sx={{ mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            Report Generated Successfully!
          </Typography>
          <Typography variant="body2">
            {generatedReport.name} ({generatedReport.format}) - {generatedReport.size}
          </Typography>
          <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
            <Button
              size="small"
              startIcon={<ViewIcon />}
              onClick={() => setPreviewDialog(true)}
            >
              Preview
            </Button>
            <Button
              size="small"
              startIcon={<DownloadIcon />}
              variant="contained"
            >
              Download
            </Button>
            <Button
              size="small"
              startIcon={<SendIcon />}
            >
              Send via Email
            </Button>
          </Box>
        </Alert>
      )}

      <Box sx={{ display: 'flex', gap: 2 }}>
        <Button
          variant="outlined"
          startIcon={<PreviewIcon />}
          onClick={() => setPreviewDialog(true)}
          disabled={generating}
        >
          Preview Report
        </Button>
        <Button
          variant="contained"
          startIcon={<ReportIcon />}
          onClick={generateReport}
          disabled={generating || !reportConfig.type}
        >
          {generating ? 'Generating...' : 'Generate Report'}
        </Button>
      </Box>
    </Box>
  );

  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return renderReportTypeSelection();
      case 1:
        return renderSectionConfiguration();
      case 2:
        return renderCustomizations();
      case 3:
        return renderPreviewAndGenerate();
      default:
        return 'Unknown step';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Advanced Report Generator
      </Typography>

      <Stepper activeStep={activeStep} orientation="vertical">
        {steps.map((label, index) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
            <StepContent>
              <Box sx={{ mb: 2 }}>
                {getStepContent(index)}
              </Box>
              <Box>
                <Button
                  disabled={index === 0}
                  onClick={() => setActiveStep(index - 1)}
                  sx={{ mr: 1 }}
                >
                  Back
                </Button>
                <Button
                  variant="contained"
                  onClick={() => setActiveStep(index + 1)}
                  disabled={index === steps.length - 1 || (index === 0 && !reportConfig.type)}
                >
                  {index === steps.length - 1 ? 'Finish' : 'Continue'}
                </Button>
              </Box>
            </StepContent>
          </Step>
        ))}
      </Stepper>

      {/* Preview Dialog */}
      <Dialog open={previewDialog} onClose={() => setPreviewDialog(false)} maxWidth="lg" fullWidth>
        <DialogTitle>Report Preview</DialogTitle>
        <DialogContent>
          <Box sx={{ height: 600, bgcolor: 'grey.100', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Typography variant="h6" color="text.secondary">
              Report preview will be displayed here
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewDialog(false)}>Close</Button>
          <Button variant="contained" startIcon={<DownloadIcon />}>
            Download
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdvancedReportGenerator;
