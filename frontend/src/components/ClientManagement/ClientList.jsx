import React, { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  TextField,
  Box,
  Typography,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Card,
  CardContent,
  Avatar,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Business as BusinessIcon,
  Search as SearchIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import { clientService } from '../../services/clientService';
import ClientForm from './ClientForm';
import ClientDetails from './ClientDetails';

const ClientList = () => {
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClient, setSelectedClient] = useState(null);
  const [openForm, setOpenForm] = useState(false);
  const [openDetails, setOpenDetails] = useState(false);
  const [filters, setFilters] = useState({
    entity_type: '',
    industry: '',
    size_category: ''
  });

  useEffect(() => {
    fetchClients();
  }, [searchTerm, filters]);

  const fetchClients = async () => {
    try {
      setLoading(true);
      const params = {
        search: searchTerm,
        ...filters
      };
      const response = await clientService.getClients(params);
      setClients(response.data.results || response.data);
    } catch (error) {
      console.error('Error fetching clients:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddClient = () => {
    setSelectedClient(null);
    setOpenForm(true);
  };

  const handleEditClient = (client) => {
    setSelectedClient(client);
    setOpenForm(true);
  };

  const handleViewClient = (client) => {
    setSelectedClient(client);
    setOpenDetails(true);
  };

  const handleFormClose = (shouldRefresh = false) => {
    setOpenForm(false);
    setSelectedClient(null);
    if (shouldRefresh) {
      fetchClients();
    }
  };

  const getEntityTypeColor = (entityType) => {
    const colors = {
      'PRIVATE_LIMITED': 'primary',
      'PUBLIC_LIMITED': 'secondary',
      'PARTNERSHIP': 'success',
      'LLP': 'warning',
      'PROPRIETORSHIP': 'info',
      'TRUST': 'error'
    };
    return colors[entityType] || 'default';
  };

  const getSizeCategoryColor = (size) => {
    const colors = {
      'MICRO': 'success',
      'SMALL': 'info',
      'MEDIUM': 'warning',
      'LARGE': 'error'
    };
    return colors[size] || 'default';
  };

  const filteredClients = clients.filter(client =>
    client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.pan_number.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <BusinessIcon fontSize="large" />
          Client Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddClient}
          size="large"
        >
          Add New Client
        </Button>
      </Box>

      {/* Search and Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search clients..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                select
                label="Entity Type"
                value={filters.entity_type}
                onChange={(e) => setFilters({ ...filters, entity_type: e.target.value })}
                SelectProps={{ native: true }}
              >
                <option value="">All Types</option>
                <option value="PRIVATE_LIMITED">Private Limited</option>
                <option value="PUBLIC_LIMITED">Public Limited</option>
                <option value="PARTNERSHIP">Partnership</option>
                <option value="LLP">LLP</option>
                <option value="PROPRIETORSHIP">Proprietorship</option>
              </TextField>
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                select
                label="Industry"
                value={filters.industry}
                onChange={(e) => setFilters({ ...filters, industry: e.target.value })}
                SelectProps={{ native: true }}
              >
                <option value="">All Industries</option>
                <option value="IT_SOFTWARE">IT & Software</option>
                <option value="MANUFACTURING">Manufacturing</option>
                <option value="FINANCE">Finance</option>
                <option value="RETAIL">Retail</option>
                <option value="HEALTHCARE">Healthcare</option>
              </TextField>
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                select
                label="Size"
                value={filters.size_category}
                onChange={(e) => setFilters({ ...filters, size_category: e.target.value })}
                SelectProps={{ native: true }}
              >
                <option value="">All Sizes</option>
                <option value="MICRO">Micro</option>
                <option value="SMALL">Small</option>
                <option value="MEDIUM">Medium</option>
                <option value="LARGE">Large</option>
              </TextField>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<FilterIcon />}
                onClick={() => setFilters({ entity_type: '', industry: '', size_category: '' })}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Client Statistics */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="primary">{clients.length}</Typography>
              <Typography variant="body2" color="text.secondary">Total Clients</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="success.main">
                {clients.filter(c => c.is_active).length}
              </Typography>
              <Typography variant="body2" color="text.secondary">Active Clients</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="warning.main">
                {clients.filter(c => c.size_category === 'LARGE').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">Large Enterprises</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="info.main">
                {clients.filter(c => c.industry === 'IT_SOFTWARE').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">IT Companies</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Client Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Client</TableCell>
              <TableCell>PAN Number</TableCell>
              <TableCell>Entity Type</TableCell>
              <TableCell>Industry</TableCell>
              <TableCell>Size</TableCell>
              <TableCell>Annual Turnover</TableCell>
              <TableCell>Status</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={8} align="center">Loading...</TableCell>
              </TableRow>
            ) : filteredClients.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} align="center">No clients found</TableCell>
              </TableRow>
            ) : (
              filteredClients.map((client) => (
                <TableRow key={client.id} hover>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        {client.name.charAt(0)}
                      </Avatar>
                      <Box>
                        <Typography variant="subtitle2">{client.name}</Typography>
                        <Typography variant="body2" color="text.secondary">
                          {client.city}, {client.state}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" fontFamily="monospace">
                      {client.pan_number}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={client.entity_type?.replace('_', ' ')}
                      color={getEntityTypeColor(client.entity_type)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {client.industry?.replace('_', ' ')}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={client.size_category}
                      color={getSizeCategoryColor(client.size_category)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      ₹{(client.annual_turnover / 10000000).toFixed(1)}Cr
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={client.is_active ? 'Active' : 'Inactive'}
                      color={client.is_active ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell align="center">
                    <Tooltip title="View Details">
                      <IconButton onClick={() => handleViewClient(client)} size="small">
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Edit Client">
                      <IconButton onClick={() => handleEditClient(client)} size="small">
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Client Form Dialog */}
      <Dialog open={openForm} onClose={() => handleFormClose()} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedClient ? 'Edit Client' : 'Add New Client'}
        </DialogTitle>
        <DialogContent>
          <ClientForm
            client={selectedClient}
            onClose={handleFormClose}
          />
        </DialogContent>
      </Dialog>

      {/* Client Details Dialog */}
      <Dialog open={openDetails} onClose={() => setOpenDetails(false)} maxWidth="lg" fullWidth>
        <DialogTitle>Client Details</DialogTitle>
        <DialogContent>
          {selectedClient && (
            <ClientDetails
              client={selectedClient}
              onClose={() => setOpenDetails(false)}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDetails(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ClientList;
