import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  <PERSON>per,
  Step,
  StepLabel,
  StepContent,
  Button,
  TextField,
  Grid,
  Chip,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  PlayArrow as StartIcon,
  Pause as PauseIcon,
  CheckCircle as CompleteIcon,
  Assignment as ProcedureIcon,
  Timer as TimerIcon,
  Person as AssignIcon,
  ExpandMore as ExpandMoreIcon,
  AttachFile as AttachIcon,
  Comment as CommentIcon,
  Warning as WarningIcon,
  TrendingUp as AnalyticsIcon
} from '@mui/icons-material';
import { riskAssessmentService } from '../../services/riskAssessmentService';

const AuditProcedureWorkflow = ({ auditPlanId, onProcedureUpdate }) => {
  const [procedures, setProcedures] = useState([]);
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(true);
  const [selectedProcedure, setSelectedProcedure] = useState(null);
  const [executionDialog, setExecutionDialog] = useState(false);
  const [executionData, setExecutionData] = useState({
    actual_hours: '',
    results: '',
    exceptions_noted: '',
    conclusion: '',
    evidence_files: []
  });
  const [timer, setTimer] = useState({ running: false, elapsed: 0 });

  useEffect(() => {
    fetchProcedures();
  }, [auditPlanId]);

  useEffect(() => {
    let interval;
    if (timer.running) {
      interval = setInterval(() => {
        setTimer(prev => ({ ...prev, elapsed: prev.elapsed + 1 }));
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [timer.running]);

  const fetchProcedures = async () => {
    try {
      setLoading(true);
      const response = await riskAssessmentService.getAuditProcedures({
        audit_plan: auditPlanId
      });
      setProcedures(response.data.results || response.data);
      
      // Find the first incomplete procedure
      const firstIncomplete = procedures.findIndex(proc => proc.status !== 'COMPLETED');
      setActiveStep(firstIncomplete >= 0 ? firstIncomplete : 0);
    } catch (error) {
      console.error('Error fetching procedures:', error);
    } finally {
      setLoading(false);
    }
  };

  const startProcedure = async (procedureId) => {
    try {
      await riskAssessmentService.startProcedure(procedureId);
      setTimer({ running: true, elapsed: 0 });
      fetchProcedures();
    } catch (error) {
      console.error('Error starting procedure:', error);
    }
  };

  const completeProcedure = async () => {
    try {
      const completionData = {
        ...executionData,
        actual_hours: parseFloat(executionData.actual_hours) || 0
      };
      
      await riskAssessmentService.completeProcedure(selectedProcedure.id, completionData);
      
      setExecutionDialog(false);
      setTimer({ running: false, elapsed: 0 });
      setExecutionData({
        actual_hours: '',
        results: '',
        exceptions_noted: '',
        conclusion: '',
        evidence_files: []
      });
      
      fetchProcedures();
      
      if (onProcedureUpdate) {
        onProcedureUpdate();
      }
    } catch (error) {
      console.error('Error completing procedure:', error);
    }
  };

  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusColor = (status) => {
    const colors = {
      'PLANNED': 'default',
      'IN_PROGRESS': 'primary',
      'COMPLETED': 'success',
      'NOT_APPLICABLE': 'secondary'
    };
    return colors[status] || 'default';
  };

  const getProcedureTypeIcon = (type) => {
    const icons = {
      'SUBSTANTIVE_DETAIL': <ProcedureIcon />,
      'SUBSTANTIVE_ANALYTICAL': <AnalyticsIcon />,
      'CONTROLS_TESTING': <CheckCircle />,
      'WALKTHROUGH': <Person />
    };
    return icons[type] || <ProcedureIcon />;
  };

  const calculateProgress = () => {
    if (procedures.length === 0) return 0;
    const completed = procedures.filter(proc => proc.status === 'COMPLETED').length;
    return (completed / procedures.length) * 100;
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>Loading Audit Procedures...</Typography>
        <LinearProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Audit Procedure Workflow
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {timer.running && (
            <Chip
              icon={<TimerIcon />}
              label={formatTime(timer.elapsed)}
              color="primary"
              variant="outlined"
            />
          )}
          <Typography variant="body2" color="text.secondary">
            Progress: {calculateProgress().toFixed(1)}%
          </Typography>
        </Box>
      </Box>

      {/* Progress Overview */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Overall Progress</Typography>
          <LinearProgress
            variant="determinate"
            value={calculateProgress()}
            sx={{ height: 10, borderRadius: 5, mb: 2 }}
          />
          <Grid container spacing={2}>
            <Grid item xs={3}>
              <Typography variant="body2" color="text.secondary">Total Procedures</Typography>
              <Typography variant="h6">{procedures.length}</Typography>
            </Grid>
            <Grid item xs={3}>
              <Typography variant="body2" color="text.secondary">Completed</Typography>
              <Typography variant="h6" color="success.main">
                {procedures.filter(p => p.status === 'COMPLETED').length}
              </Typography>
            </Grid>
            <Grid item xs={3}>
              <Typography variant="body2" color="text.secondary">In Progress</Typography>
              <Typography variant="h6" color="primary.main">
                {procedures.filter(p => p.status === 'IN_PROGRESS').length}
              </Typography>
            </Grid>
            <Grid item xs={3}>
              <Typography variant="body2" color="text.secondary">Planned Hours</Typography>
              <Typography variant="h6">
                {procedures.reduce((sum, p) => sum + (p.expected_hours || 0), 0)}
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Procedure Stepper */}
      <Card>
        <CardContent>
          <Stepper activeStep={activeStep} orientation="vertical">
            {procedures.map((procedure, index) => (
              <Step key={procedure.id}>
                <StepLabel
                  optional={
                    <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                      <Chip
                        label={procedure.status.replace('_', ' ')}
                        color={getStatusColor(procedure.status)}
                        size="small"
                      />
                      <Chip
                        label={`${procedure.expected_hours}h`}
                        size="small"
                        variant="outlined"
                      />
                      {procedure.assigned_to && (
                        <Chip
                          icon={<AssignIcon />}
                          label={procedure.assigned_to.first_name}
                          size="small"
                          variant="outlined"
                        />
                      )}
                    </Box>
                  }
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getProcedureTypeIcon(procedure.procedure_type)}
                    <Typography variant="subtitle1">
                      {procedure.procedure_code}: {procedure.procedure_name}
                    </Typography>
                  </Box>
                </StepLabel>
                <StepContent>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      {procedure.description}
                    </Typography>
                    
                    {procedure.addresses_risk && (
                      <Alert severity="info" sx={{ mb: 2 }}>
                        <Typography variant="body2">
                          <strong>Addresses Risk:</strong> {procedure.addresses_risk}
                        </Typography>
                      </Alert>
                    )}

                    <Accordion>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Typography variant="subtitle2">Procedure Details</Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Grid container spacing={2}>
                          <Grid item xs={12} md={6}>
                            <Typography variant="body2">
                              <strong>Assertion Tested:</strong> {procedure.assertion_tested || 'Not specified'}
                            </Typography>
                          </Grid>
                          <Grid item xs={12} md={6}>
                            <Typography variant="body2">
                              <strong>Sample Size:</strong> {procedure.sample_size || 'Not specified'}
                            </Typography>
                          </Grid>
                          <Grid item xs={12} md={6}>
                            <Typography variant="body2">
                              <strong>Sampling Method:</strong> {procedure.sampling_method || 'Not specified'}
                            </Typography>
                          </Grid>
                          <Grid item xs={12} md={6}>
                            <Typography variant="body2">
                              <strong>Expected Hours:</strong> {procedure.expected_hours}
                            </Typography>
                          </Grid>
                        </Grid>
                      </AccordionDetails>
                    </Accordion>

                    {procedure.status === 'COMPLETED' && (
                      <Box sx={{ mt: 2, p: 2, bgcolor: 'success.light', borderRadius: 1 }}>
                        <Typography variant="subtitle2" gutterBottom>Completion Summary</Typography>
                        <Typography variant="body2">
                          <strong>Actual Hours:</strong> {procedure.actual_hours}
                        </Typography>
                        <Typography variant="body2">
                          <strong>Conclusion:</strong> {procedure.conclusion}
                        </Typography>
                        {procedure.exceptions_noted && (
                          <Typography variant="body2" color="warning.main">
                            <strong>Exceptions:</strong> {procedure.exceptions_noted}
                          </Typography>
                        )}
                      </Box>
                    )}

                    <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                      {procedure.status === 'PLANNED' && (
                        <Button
                          variant="contained"
                          startIcon={<StartIcon />}
                          onClick={() => startProcedure(procedure.id)}
                        >
                          Start Procedure
                        </Button>
                      )}
                      
                      {procedure.status === 'IN_PROGRESS' && (
                        <Button
                          variant="contained"
                          color="success"
                          startIcon={<CompleteIcon />}
                          onClick={() => {
                            setSelectedProcedure(procedure);
                            setExecutionDialog(true);
                          }}
                        >
                          Complete Procedure
                        </Button>
                      )}

                      <Button
                        variant="outlined"
                        startIcon={<CommentIcon />}
                        size="small"
                      >
                        Add Comment
                      </Button>
                      
                      <Button
                        variant="outlined"
                        startIcon={<AttachIcon />}
                        size="small"
                      >
                        Attach Evidence
                      </Button>
                    </Box>
                  </Box>
                </StepContent>
              </Step>
            ))}
          </Stepper>
        </CardContent>
      </Card>

      {/* Procedure Completion Dialog */}
      <Dialog open={executionDialog} onClose={() => setExecutionDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Complete Procedure: {selectedProcedure?.procedure_name}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Actual Hours"
                type="number"
                value={executionData.actual_hours}
                onChange={(e) => setExecutionData(prev => ({ ...prev, actual_hours: e.target.value }))}
                inputProps={{ step: 0.5, min: 0 }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="body2" color="text.secondary">
                Expected: {selectedProcedure?.expected_hours} hours
              </Typography>
              {timer.running && (
                <Typography variant="body2" color="primary">
                  Timer: {formatTime(timer.elapsed)} ({(timer.elapsed / 3600).toFixed(2)}h)
                </Typography>
              )}
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={4}
                label="Results and Findings"
                value={executionData.results}
                onChange={(e) => setExecutionData(prev => ({ ...prev, results: e.target.value }))}
                placeholder="Describe the work performed and key findings..."
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Exceptions Noted"
                value={executionData.exceptions_noted}
                onChange={(e) => setExecutionData(prev => ({ ...prev, exceptions_noted: e.target.value }))}
                placeholder="Document any exceptions or unusual items identified..."
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={2}
                label="Conclusion"
                value={executionData.conclusion}
                onChange={(e) => setExecutionData(prev => ({ ...prev, conclusion: e.target.value }))}
                placeholder="Overall conclusion based on the work performed..."
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setExecutionDialog(false)}>Cancel</Button>
          <Button
            onClick={completeProcedure}
            variant="contained"
            color="success"
            disabled={!executionData.results || !executionData.conclusion}
          >
            Complete Procedure
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AuditProcedureWorkflow;
