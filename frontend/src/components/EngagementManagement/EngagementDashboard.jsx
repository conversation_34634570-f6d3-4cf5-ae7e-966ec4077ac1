import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Box,
  LinearProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Button,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Divider
} from '@mui/material';
import {
  Assignment as AssignmentIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  People as PeopleIcon,
  AttachMoney as MoneyIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material';
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import { engagementService } from '../../services/engagementService';

const EngagementDashboard = () => {
  const [dashboardData, setDashboardData] = useState({
    stats: {},
    recentEngagements: [],
    upcomingDeadlines: [],
    teamWorkload: [],
    riskDistribution: [],
    statusDistribution: []
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const [statsResponse, engagementsResponse] = await Promise.all([
        engagementService.getDashboardStats(),
        engagementService.getEngagements({ limit: 10 })
      ]);

      // Mock data for demonstration
      setDashboardData({
        stats: {
          total_engagements: 25,
          active_engagements: 18,
          completed_this_month: 7,
          overdue_engagements: 3,
          total_revenue: 2500000,
          avg_completion_time: 45
        },
        recentEngagements: [
          {
            id: 1,
            engagement_number: 'AUD-2024-001',
            client_name: 'TechCorp Solutions',
            engagement_type: 'STATUTORY_AUDIT',
            status: 'FIELDWORK',
            planned_end_date: '2024-03-15',
            progress: 65
          },
          {
            id: 2,
            engagement_number: 'AUD-2024-002',
            client_name: 'Manufacturing Ltd',
            engagement_type: 'TAX_AUDIT',
            status: 'PLANNING',
            planned_end_date: '2024-04-30',
            progress: 25
          },
          {
            id: 3,
            engagement_number: 'AUD-2024-003',
            client_name: 'Green Energy',
            engagement_type: 'INTERNAL_AUDIT',
            status: 'REVIEW',
            planned_end_date: '2024-02-28',
            progress: 85
          }
        ],
        upcomingDeadlines: [
          {
            engagement_number: 'AUD-2024-001',
            client_name: 'TechCorp Solutions',
            deadline: '2024-03-15',
            days_remaining: 5,
            type: 'Final Report'
          },
          {
            engagement_number: 'AUD-2024-003',
            client_name: 'Green Energy',
            deadline: '2024-02-28',
            days_remaining: 2,
            type: 'Management Letter'
          }
        ],
        statusDistribution: [
          { name: 'Planning', value: 5, color: '#8884d8' },
          { name: 'Fieldwork', value: 8, color: '#82ca9d' },
          { name: 'Review', value: 3, color: '#ffc658' },
          { name: 'Reporting', value: 2, color: '#ff7300' },
          { name: 'Completed', value: 7, color: '#00ff00' }
        ],
        riskDistribution: [
          { name: 'Low', value: 12, color: '#00ff00' },
          { name: 'Medium', value: 10, color: '#ffc658' },
          { name: 'High', value: 3, color: '#ff0000' }
        ]
      });
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      'PLANNING': 'info',
      'FIELDWORK': 'primary',
      'REVIEW': 'warning',
      'REPORTING': 'secondary',
      'COMPLETED': 'success',
      'ON_HOLD': 'error'
    };
    return colors[status] || 'default';
  };

  const getPriorityColor = (priority) => {
    const colors = {
      'LOW': 'success',
      'MEDIUM': 'warning',
      'HIGH': 'error'
    };
    return colors[priority] || 'default';
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>Loading Dashboard...</Typography>
        <LinearProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Typography variant="h4" component="h1" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <AssessmentIcon fontSize="large" />
        Engagement Dashboard
      </Typography>

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <AssignmentIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
              <Typography variant="h4" color="primary">
                {dashboardData.stats.total_engagements}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Engagements
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TrendingUpIcon sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" color="success.main">
                {dashboardData.stats.active_engagements}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active Engagements
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <CheckCircleIcon sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
              <Typography variant="h4" color="info.main">
                {dashboardData.stats.completed_this_month}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Completed This Month
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <WarningIcon sx={{ fontSize: 40, color: 'error.main', mb: 1 }} />
              <Typography variant="h4" color="error.main">
                {dashboardData.stats.overdue_engagements}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Overdue
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <MoneyIcon sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
              <Typography variant="h4" color="warning.main">
                ₹{(dashboardData.stats.total_revenue / 1000000).toFixed(1)}M
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Revenue
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <ScheduleIcon sx={{ fontSize: 40, color: 'secondary.main', mb: 1 }} />
              <Typography variant="h4" color="secondary.main">
                {dashboardData.stats.avg_completion_time}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Avg Days to Complete
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Status Distribution Chart */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Engagement Status Distribution
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={dashboardData.statusDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {dashboardData.statusDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <RechartsTooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Risk Distribution Chart */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Risk Level Distribution
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={dashboardData.riskDistribution}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <RechartsTooltip />
                  <Bar dataKey="value" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Engagements */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Engagements
              </Typography>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Engagement</TableCell>
                      <TableCell>Client</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Progress</TableCell>
                      <TableCell>Deadline</TableCell>
                      <TableCell align="center">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {dashboardData.recentEngagements.map((engagement) => (
                      <TableRow key={engagement.id} hover>
                        <TableCell>
                          <Typography variant="body2" fontWeight="bold">
                            {engagement.engagement_number}
                          </Typography>
                        </TableCell>
                        <TableCell>{engagement.client_name}</TableCell>
                        <TableCell>
                          <Chip
                            label={engagement.engagement_type.replace('_', ' ')}
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={engagement.status}
                            color={getStatusColor(engagement.status)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <LinearProgress
                              variant="determinate"
                              value={engagement.progress}
                              sx={{ flexGrow: 1, height: 8, borderRadius: 4 }}
                            />
                            <Typography variant="body2">
                              {engagement.progress}%
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {new Date(engagement.planned_end_date).toLocaleDateString()}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <Tooltip title="View Details">
                            <IconButton size="small">
                              <ViewIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Edit">
                            <IconButton size="small">
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Upcoming Deadlines */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Upcoming Deadlines
              </Typography>
              <List>
                {dashboardData.upcomingDeadlines.map((deadline, index) => (
                  <React.Fragment key={index}>
                    <ListItem>
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: deadline.days_remaining <= 3 ? 'error.main' : 'warning.main' }}>
                          <ScheduleIcon />
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={deadline.engagement_number}
                        secondary={
                          <Box>
                            <Typography variant="body2">{deadline.client_name}</Typography>
                            <Typography variant="body2" color="text.secondary">
                              {deadline.type} - {deadline.days_remaining} days remaining
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                    {index < dashboardData.upcomingDeadlines.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default EngagementDashboard;
