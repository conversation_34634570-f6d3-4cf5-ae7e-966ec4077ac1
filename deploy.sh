#!/bin/bash

# AuditSmartAI Production Deployment Script
# This script deploys the application to production environment

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-production}
COMPOSE_FILE="docker-compose.${ENVIRONMENT}.yml"
ENV_FILE=".env.${ENVIRONMENT}"

echo -e "${BLUE}🚀 AuditSmartAI Deployment Script${NC}"
echo -e "${BLUE}Environment: ${ENVIRONMENT}${NC}"
echo "=================================="

# Check if required files exist
if [ ! -f "$COMPOSE_FILE" ]; then
    echo -e "${RED}❌ Error: $COMPOSE_FILE not found${NC}"
    exit 1
fi

if [ ! -f "$ENV_FILE" ]; then
    echo -e "${RED}❌ Error: $ENV_FILE not found${NC}"
    exit 1
fi

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}❌ Error: Docker is not running${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Docker is running${NC}"
}

# Function to check if Docker Compose is available
check_docker_compose() {
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}❌ Error: docker-compose not found${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Docker Compose is available${NC}"
}

# Function to validate environment variables
validate_env() {
    echo -e "${YELLOW}🔍 Validating environment variables...${NC}"
    
    # Load environment variables
    source "$ENV_FILE"
    
    # Check required variables
    required_vars=(
        "SECRET_KEY"
        "DB_PASSWORD"
        "REDIS_PASSWORD"
        "ALLOWED_HOSTS"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            echo -e "${RED}❌ Error: $var is not set in $ENV_FILE${NC}"
            exit 1
        fi
    done
    
    echo -e "${GREEN}✅ Environment variables validated${NC}"
}

# Function to create necessary directories
create_directories() {
    echo -e "${YELLOW}📁 Creating necessary directories...${NC}"
    
    mkdir -p logs
    mkdir -p backend/logs
    mkdir -p backend/media
    mkdir -p backend/static
    mkdir -p backend/ml_models/saved_models
    mkdir -p database
    mkdir -p nginx/ssl
    
    echo -e "${GREEN}✅ Directories created${NC}"
}

# Function to generate SSL certificates (self-signed for development)
generate_ssl_certs() {
    if [ "$ENVIRONMENT" = "development" ] && [ ! -f "nginx/ssl/cert.pem" ]; then
        echo -e "${YELLOW}🔐 Generating self-signed SSL certificates...${NC}"
        
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout nginx/ssl/key.pem \
            -out nginx/ssl/cert.pem \
            -subj "/C=IN/ST=Delhi/L=Delhi/O=AuditSmartAI/CN=localhost"
        
        echo -e "${GREEN}✅ SSL certificates generated${NC}"
    fi
}

# Function to build and start services
deploy_services() {
    echo -e "${YELLOW}🏗️ Building and starting services...${NC}"
    
    # Pull latest images
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" pull
    
    # Build services
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" build --no-cache
    
    # Start services
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d
    
    echo -e "${GREEN}✅ Services started${NC}"
}

# Function to run database migrations
run_migrations() {
    echo -e "${YELLOW}🗄️ Running database migrations...${NC}"
    
    # Wait for database to be ready
    echo "Waiting for database to be ready..."
    sleep 10
    
    # Run migrations
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T backend python manage.py migrate
    
    echo -e "${GREEN}✅ Database migrations completed${NC}"
}

# Function to collect static files
collect_static() {
    echo -e "${YELLOW}📦 Collecting static files...${NC}"
    
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T backend python manage.py collectstatic --noinput
    
    echo -e "${GREEN}✅ Static files collected${NC}"
}

# Function to create superuser
create_superuser() {
    echo -e "${YELLOW}👤 Creating superuser...${NC}"
    
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T backend python manage.py shell << EOF
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('Superuser created: admin/admin123')
else:
    print('Superuser already exists')
EOF
    
    echo -e "${GREEN}✅ Superuser setup completed${NC}"
}

# Function to train ML models
train_ml_models() {
    echo -e "${YELLOW}🤖 Training ML models...${NC}"
    
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T backend python ml_models/model_trainer.py
    
    echo -e "${GREEN}✅ ML models trained${NC}"
}

# Function to load sample data
load_sample_data() {
    if [ "$ENVIRONMENT" = "development" ]; then
        echo -e "${YELLOW}📊 Loading sample data...${NC}"
        
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T backend python sample_data_generator.py
        
        echo -e "${GREEN}✅ Sample data loaded${NC}"
    fi
}

# Function to check service health
check_health() {
    echo -e "${YELLOW}🏥 Checking service health...${NC}"
    
    # Wait for services to be ready
    sleep 30
    
    # Check backend health
    if curl -f http://localhost:8000/health/ > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Backend is healthy${NC}"
    else
        echo -e "${RED}❌ Backend health check failed${NC}"
        return 1
    fi
    
    # Check frontend health
    if curl -f http://localhost/ > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Frontend is healthy${NC}"
    else
        echo -e "${RED}❌ Frontend health check failed${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ All services are healthy${NC}"
}

# Function to show deployment summary
show_summary() {
    echo ""
    echo -e "${BLUE}🎉 Deployment Summary${NC}"
    echo "=================================="
    echo -e "Environment: ${ENVIRONMENT}"
    echo -e "Frontend URL: ${GREEN}http://localhost${NC}"
    echo -e "Backend API: ${GREEN}http://localhost:8000${NC}"
    echo -e "Admin Panel: ${GREEN}http://localhost:8000/admin${NC}"
    echo -e "Flower (Celery): ${GREEN}http://localhost:5555${NC}"
    echo ""
    echo -e "${YELLOW}Default Credentials:${NC}"
    echo -e "Username: admin"
    echo -e "Password: admin123"
    echo ""
    echo -e "${YELLOW}Useful Commands:${NC}"
    echo -e "View logs: ${BLUE}docker-compose -f $COMPOSE_FILE logs -f${NC}"
    echo -e "Stop services: ${BLUE}docker-compose -f $COMPOSE_FILE down${NC}"
    echo -e "Restart services: ${BLUE}docker-compose -f $COMPOSE_FILE restart${NC}"
    echo ""
}

# Function to cleanup on failure
cleanup_on_failure() {
    echo -e "${RED}❌ Deployment failed. Cleaning up...${NC}"
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down
    exit 1
}

# Trap to cleanup on failure
trap cleanup_on_failure ERR

# Main deployment flow
main() {
    echo -e "${BLUE}Starting deployment process...${NC}"
    
    check_docker
    check_docker_compose
    validate_env
    create_directories
    generate_ssl_certs
    deploy_services
    run_migrations
    collect_static
    create_superuser
    train_ml_models
    load_sample_data
    check_health
    show_summary
    
    echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
}

# Run main function
main "$@"
