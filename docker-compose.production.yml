version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    container_name: auditsmartai_db
    environment:
      POSTGRES_DB: auditsmartai
      POSTGRES_USER: audituser
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_HOST_AUTH_METHOD: trust
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - auditsmartai_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U audituser -d auditsmartai"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: auditsmartai_redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - auditsmartai_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Django Backend
  backend:
    build:
      context: ./backend
      target: production
    container_name: auditsmartai_backend
    environment:
      - DJANGO_SETTINGS_MODULE=core.settings.production
      - DATABASE_URL=postgresql://audituser:${DB_PASSWORD}@db:5432/auditsmartai
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - DEBUG=False
      - ALLOWED_HOSTS=${ALLOWED_HOSTS}
      - CORS_ALLOWED_ORIGINS=${CORS_ALLOWED_ORIGINS}
      - EMAIL_HOST=${EMAIL_HOST}
      - EMAIL_PORT=${EMAIL_PORT}
      - EMAIL_HOST_USER=${EMAIL_HOST_USER}
      - EMAIL_HOST_PASSWORD=${EMAIL_HOST_PASSWORD}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_STORAGE_BUCKET_NAME=${AWS_STORAGE_BUCKET_NAME}
      - SENTRY_DSN=${SENTRY_DSN}
    volumes:
      - media_files:/app/media
      - static_files:/app/static
      - ./backend/logs:/app/logs
      - ./backend/ml_models/saved_models:/app/ml_models/saved_models
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - auditsmartai_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 5

  # React Frontend
  frontend:
    build:
      context: ./frontend
      target: production
    container_name: auditsmartai_frontend
    volumes:
      - ./frontend/nginx.conf:/etc/nginx/nginx.conf:ro
      - static_files:/usr/share/nginx/html/static
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    networks:
      - auditsmartai_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Celery Worker for Background Tasks
  celery_worker:
    build:
      context: ./backend
      target: production
    container_name: auditsmartai_celery_worker
    command: celery -A core worker -l info --concurrency=4
    environment:
      - DJANGO_SETTINGS_MODULE=core.settings.production
      - DATABASE_URL=postgresql://audituser:${DB_PASSWORD}@db:5432/auditsmartai
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
    volumes:
      - media_files:/app/media
      - ./backend/logs:/app/logs
      - ./backend/ml_models/saved_models:/app/ml_models/saved_models
    depends_on:
      - db
      - redis
    networks:
      - auditsmartai_network
    restart: unless-stopped

  # Celery Beat for Scheduled Tasks
  celery_beat:
    build:
      context: ./backend
      target: production
    container_name: auditsmartai_celery_beat
    command: celery -A core beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler
    environment:
      - DJANGO_SETTINGS_MODULE=core.settings.production
      - DATABASE_URL=postgresql://audituser:${DB_PASSWORD}@db:5432/auditsmartai
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
    volumes:
      - ./backend/logs:/app/logs
    depends_on:
      - db
      - redis
    networks:
      - auditsmartai_network
    restart: unless-stopped

  # Flower for Celery Monitoring
  flower:
    build:
      context: ./backend
      target: production
    container_name: auditsmartai_flower
    command: celery -A core flower --port=5555
    environment:
      - DJANGO_SETTINGS_MODULE=core.settings.production
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
      - FLOWER_BASIC_AUTH=${FLOWER_USER}:${FLOWER_PASSWORD}
    ports:
      - "5555:5555"
    depends_on:
      - redis
    networks:
      - auditsmartai_network
    restart: unless-stopped

  # Elasticsearch for Search
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: auditsmartai_elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - auditsmartai_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

volumes:
  postgres_data:
  redis_data:
  elasticsearch_data:
  media_files:
  static_files:

networks:
  auditsmartai_network:
    driver: bridge
