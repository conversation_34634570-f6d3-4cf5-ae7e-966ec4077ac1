# Production Environment Variables for AuditSmartAI

# Django Settings
SECRET_KEY=your-super-secret-key-here-change-this-in-production
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com,api.yourdomain.com
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Database Configuration
DB_PASSWORD=your-secure-database-password
DATABASE_URL=************************************************************/auditsmartai

# Redis Configuration
REDIS_PASSWORD=your-secure-redis-password
REDIS_URL=redis://:your-secure-redis-password@redis:6379/0

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# AWS S3 Configuration (Optional)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=auditsmartai-storage
AWS_S3_REGION_NAME=ap-south-1

# Sentry Configuration (Optional)
SENTRY_DSN=https://<EMAIL>/project-id

# Celery Flower Monitoring
FLOWER_USER=admin
FLOWER_PASSWORD=your-flower-password

# SSL Configuration
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# Backup Configuration
BACKUP_ENABLED=True
BACKUP_S3_BUCKET=auditsmartai-backups
BACKUP_RETENTION_DAYS=30

# Monitoring
MONITORING_ENABLED=True
ENABLE_SILK=False

# ML Configuration
ML_ENABLE_GPU=False
ML_BATCH_SIZE=32
ML_MAX_WORKERS=4

# Admin Configuration
ADMIN_URL=secure-admin/
ADMIN_EMAIL=<EMAIL>

# Release Version
RELEASE_VERSION=1.0.0

# Elasticsearch (Optional)
ELASTICSEARCH_URL=elasticsearch:9200
