# backend/core/urls.py

from django.contrib import admin
from django.urls import path, include
from django.http import HttpResponse

urlpatterns = [
    path('', lambda request: HttpResponse("✅ AuditSmartAI is running!")),

    # Django admin panel
    path('admin/', admin.site.urls),

    # API endpoints
    path('api/users/', include('users.urls')),
    path('api/audit/', include('audit.urls')),
    path('api/clients/', include('clients.urls')),
    path('api/engagements/', include('audit_engagements.urls')),
    path('api/working-papers/', include('working_papers.urls')),
    path('api/audit-reports/', include('audit_engagements.reports.urls')),
    path('api/peer-review/', include('peer_review.urls')),

    # (Optional) Catch-all or global APIs (if any exist)
    # path('api/', include('api.urls')),  # Uncomment if you have a global router
]
