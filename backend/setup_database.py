#!/usr/bin/env python
"""
Database setup script for AuditSmartAI
This script will create a fresh database with all required tables and sample data.
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings.development')
django.setup()

def setup_database():
    """Set up the database with all migrations and sample data"""
    
    print("🗄️  Setting up AuditSmartAI Database...")
    
    # Remove existing database
    db_path = 'db.sqlite3'
    if os.path.exists(db_path):
        os.remove(db_path)
        print("✅ Removed existing database")
    
    # Create migrations
    print("📝 Creating migrations...")
    execute_from_command_line(['manage.py', 'makemigrations'])
    
    # Run migrations
    print("🔄 Running migrations...")
    execute_from_command_line(['manage.py', 'migrate'])
    
    # Create superuser
    print("👤 Creating superuser...")
    from django.contrib.auth import get_user_model
    User = get_user_model()
    
    if not User.objects.filter(username='admin').exists():
        User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            first_name='Admin',
            last_name='User'
        )
        print("✅ Created superuser: admin/admin123")
    
    print("🎉 Database setup completed successfully!")
    print("\n📊 Next steps:")
    print("1. Run: python manage.py runserver")
    print("2. Visit: http://localhost:8000/admin")
    print("3. Login with: admin/admin123")

if __name__ == '__main__':
    setup_database()
