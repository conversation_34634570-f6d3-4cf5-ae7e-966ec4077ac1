"""email.py - Send audit notifications via email"""

from django.core.mail import send_mail
from django.conf import settings

def send_email_notification(to_email, subject, message):
    """
    Send a plain text email notification.

    Args:
        to_email (str): Recipient email
        subject (str): Subject line
        message (str): Body of the message
    """
    send_mail(
        subject,
        message,
        settings.DEFAULT_FROM_EMAIL,
        [to_email],
        fail_silently=False
    )
