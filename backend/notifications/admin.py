from django.contrib import admin
from .models import Notification

@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ['recipient', 'notification_type', 'subject', 'status', 'created_at']
    list_filter = ['notification_type', 'status', 'created_at']
    search_fields = ['recipient__username', 'subject', 'message']
    readonly_fields = ['created_at', 'sent_at']
