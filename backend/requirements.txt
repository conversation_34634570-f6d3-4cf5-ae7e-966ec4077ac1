# Django Framework
Django>=4.2

# API + Serialization
djangorestframework>=3.14.0
drf-yasg>=1.21.6        # Swagger for API docs

# CORS
django-cors-headers>=4.2.0

# OCR & PDF Processing
pytesseract>=0.3.10
Pillow>=10.0.0
PyMuPDF>=1.22.0         # PDF extraction

# NLP & AI
transformers>=4.40.0
torch>=2.2.0
scikit-learn>=1.3.0

# Database support
psycopg2-binary>=2.9.9  # Only if using PostgreSQL

# Environment variables (optional)
python-dotenv>=1.0.1

# WhatsApp & Push Simulation (actual APIs can be added later)
requests>=2.31.0

# Background tasks (optional)
celery>=5.3.6
redis>=5.0.1

# Linting (optional)
black>=24.3.0
isort>=5.13.0
