"""chatbot.py - AI chatbot interface for audit assistant"""

from ai_assistant.nlp_engine import extract_intent_and_entities
from ai_assistant.rule_engine import apply_audit_rules

def generate_chat_response(user_query: str) -> str:
    """
    Generate a response to the user's audit-related query.

    Args:
        user_query (str): Natural language query from user.

    Returns:
        str: AI-generated response based on audit rules.
    """
    intent, entities = extract_intent_and_entities(user_query)

    if not intent:
        return "I'm sorry, I couldn't understand your query. Could you please rephrase?"

    response = apply_audit_rules(intent, entities)
    return response
