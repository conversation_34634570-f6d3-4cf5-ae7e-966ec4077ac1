"""audit_risk_predictor.py - Predicts audit risk scores based on data inputs"""

def predict_risk_score(financials: dict) -> str:
    """
    Predicts an audit risk level based on financial metrics.

    Args:
        financials (dict): A dictionary containing financial figures.

    Returns:
        str: Risk level - 'Low', 'Medium', or 'High'
    """
    try:
        revenue = financials.get("revenue", 0)
        net_profit = financials.get("net_profit", 0)
        related_party_txns = financials.get("related_party_txns", 0)
        internal_control_flag = financials.get("internal_control_flag", True)

        # Simple scoring logic
        risk_score = 0

        if revenue > 10_00_00_000:  # 10 Cr+
            risk_score += 2
        if net_profit < 0:
            risk_score += 2
        if related_party_txns > 0:
            risk_score += 2
        if not internal_control_flag:
            risk_score += 3

        if risk_score >= 6:
            return "High"
        elif risk_score >= 3:
            return "Medium"
        else:
            return "Low"

    except Exception as e:
        return f"Risk prediction failed due to error: {str(e)}"
