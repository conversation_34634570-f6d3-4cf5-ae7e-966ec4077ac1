"""rule_engine.py - Contains audit domain logic and rule-based response engine"""

def apply_audit_rules(intent: str, entities: dict) -> str:
    """
    Returns an appropriate audit response based on the identified intent.

    Args:
        intent (str): Extracted user intent.
        entities (dict): Any audit-specific entities.

    Returns:
        str: Relevant AI-generated audit advice or insight.
    """
    if intent == "tax_audit":
        return "Ensure Clause 3CD details are correctly filled, including depreciation, disallowances, and TDS reconciliation."
    
    elif intent == "caro_compliance":
        return "Check CARO applicability. Verify loan defaults, related party transactions, and internal audit systems."

    elif intent == "tds_check":
        return "Match TDS entries with Form 26AS. Ensure deductions are deposited and reported correctly under relevant sections."

    elif intent == "gst_matching":
        return "Match GSTR-2B with purchase register. Flag mismatches and initiate vendor follow-up."

    elif intent == "financial_statement_analysis":
        return "Verify consistency across Balance Sheet and P&L. Apply ratio analysis for liquidity, solvency, and profitability."

    return "No audit rules matched the given query. Please consult the audit checklist manually."
