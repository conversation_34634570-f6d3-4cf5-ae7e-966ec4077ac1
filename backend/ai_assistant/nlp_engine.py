"""nlp_engine.py - NLP-based intent and entity extractor"""

def extract_intent_and_entities(text: str) -> tuple[str, dict]:
    """
    Extracts audit-related intent and entities from user query text.

    Args:
        text (str): Input query from user.

    Returns:
        tuple: (intent string, dictionary of entities)
    """
    text = text.lower()
    
    if "3cd" in text or "tax audit" in text:
        return "tax_audit", {"clause": "3CD"}
    elif "caro" in text:
        return "caro_compliance", {}
    elif "tds" in text or "26as" in text:
        return "tds_check", {}
    elif "gstr" in text or "gst" in text:
        return "gst_matching", {}
    elif "balance sheet" in text or "profit and loss" in text:
        return "financial_statement_analysis", {}
    
    return "", {}
