"""ml_services.py - Machine Learning services for document processing"""

import re
import json
import hashlib
from typing import Dict, List, Tuple, Any
from datetime import datetime
from decimal import Decimal, InvalidOperation
import pytesseract
from PIL import Image
import pandas as pd
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler
import io

class DocumentClassifier:
    """ML-based document classification"""
    
    def __init__(self):
        self.document_patterns = {
            'BANK_STATEMENT': [
                r'bank\s+statement', r'account\s+statement', r'transaction\s+history',
                r'opening\s+balance', r'closing\s+balance', r'debit', r'credit'
            ],
            'GST_REPORT': [
                r'gst', r'goods\s+and\s+services\s+tax', r'gstr', r'input\s+tax\s+credit',
                r'output\s+tax', r'igst', r'cgst', r'sgst'
            ],
            'FORM_26AS': [
                r'form\s+26as', r'tax\s+credit\s+statement', r'tds\s+certificate',
                r'deducted\s+at\s+source', r'challan'
            ],
            'INVOICE': [
                r'invoice', r'bill', r'tax\s+invoice', r'invoice\s+no', r'bill\s+no',
                r'amount\s+payable', r'total\s+amount'
            ],
            'BALANCE_SHEET': [
                r'balance\s+sheet', r'assets', r'liabilities', r'equity',
                r'current\s+assets', r'fixed\s+assets', r'current\s+liabilities'
            ],
            'PROFIT_LOSS': [
                r'profit\s+and\s+loss', r'income\s+statement', r'revenue',
                r'expenses', r'net\s+profit', r'gross\s+profit'
            ]
        }
    
    def classify_document(self, text: str) -> Tuple[str, float]:
        """
        Classify document based on text content.
        
        Args:
            text (str): Extracted text from document
            
        Returns:
            Tuple[str, float]: (document_type, confidence_score)
        """
        text_lower = text.lower()
        scores = {}
        
        for doc_type, patterns in self.document_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, text_lower))
                score += matches
            
            # Normalize score by text length
            scores[doc_type] = score / max(len(text.split()), 1)
        
        if not scores or max(scores.values()) == 0:
            return 'OTHERS', 0.0
        
        best_type = max(scores, key=scores.get)
        confidence = min(scores[best_type] * 10, 1.0)  # Scale to 0-1
        
        return best_type, confidence

class OCRProcessor:
    """Enhanced OCR processing with preprocessing"""
    
    def __init__(self):
        self.tesseract_config = '--oem 3 --psm 6'
    
    def extract_text_from_image(self, image_bytes: bytes) -> Dict[str, Any]:
        """
        Extract text from image with preprocessing.
        
        Args:
            image_bytes (bytes): Image file content
            
        Returns:
            Dict[str, Any]: Extraction results
        """
        try:
            image = Image.open(io.BytesIO(image_bytes))
            
            # Preprocess image
            processed_image = self._preprocess_image(image)
            
            # Extract text
            text = pytesseract.image_to_string(processed_image, config=self.tesseract_config)
            
            # Extract structured data
            data = pytesseract.image_to_data(processed_image, output_type=pytesseract.Output.DICT)
            
            # Calculate confidence
            confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0
            
            return {
                'text': text,
                'confidence': avg_confidence / 100,  # Convert to 0-1 scale
                'word_data': data,
                'processing_time': 0  # Would be calculated in real implementation
            }
            
        except Exception as e:
            return {
                'text': '',
                'confidence': 0.0,
                'error': str(e),
                'processing_time': 0
            }
    
    def _preprocess_image(self, image: Image.Image) -> Image.Image:
        """Preprocess image for better OCR results"""
        # Convert to grayscale
        if image.mode != 'L':
            image = image.convert('L')
        
        # Resize if too small
        width, height = image.size
        if width < 1000 or height < 1000:
            scale_factor = max(1000 / width, 1000 / height)
            new_size = (int(width * scale_factor), int(height * scale_factor))
            image = image.resize(new_size, Image.Resampling.LANCZOS)
        
        return image

class FinancialDataExtractor:
    """Extract financial data from documents"""
    
    def __init__(self):
        self.amount_patterns = [
            r'₹\s*([0-9,]+\.?[0-9]*)',
            r'rs\.?\s*([0-9,]+\.?[0-9]*)',
            r'inr\s*([0-9,]+\.?[0-9]*)',
            r'([0-9,]+\.?[0-9]*)\s*/-',
            r'\b([0-9,]+\.?[0-9]*)\b'
        ]
        
        self.date_patterns = [
            r'(\d{1,2}[-/]\d{1,2}[-/]\d{2,4})',
            r'(\d{1,2}\s+[a-zA-Z]{3,9}\s+\d{2,4})',
            r'(\d{2,4}[-/]\d{1,2}[-/]\d{1,2})'
        ]
        
        self.pan_pattern = r'\b[A-Z]{5}[0-9]{4}[A-Z]{1}\b'
        self.gst_pattern = r'\b[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}\b'
    
    def extract_financial_data(self, text: str, document_type: str) -> Dict[str, Any]:
        """
        Extract financial data based on document type.
        
        Args:
            text (str): Document text
            document_type (str): Type of document
            
        Returns:
            Dict[str, Any]: Extracted financial data
        """
        extracted_data = {
            'amounts': self._extract_amounts(text),
            'dates': self._extract_dates(text),
            'pan_numbers': self._extract_pan_numbers(text),
            'gst_numbers': self._extract_gst_numbers(text)
        }
        
        # Document-specific extraction
        if document_type == 'BANK_STATEMENT':
            extracted_data.update(self._extract_bank_statement_data(text))
        elif document_type == 'INVOICE':
            extracted_data.update(self._extract_invoice_data(text))
        elif document_type == 'GST_REPORT':
            extracted_data.update(self._extract_gst_data(text))
        elif document_type == 'BALANCE_SHEET':
            extracted_data.update(self._extract_balance_sheet_data(text))
        
        return extracted_data
    
    def _extract_amounts(self, text: str) -> List[Dict[str, Any]]:
        """Extract monetary amounts from text"""
        amounts = []
        for pattern in self.amount_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                amount_str = match.group(1) if match.groups() else match.group(0)
                try:
                    # Clean and convert amount
                    clean_amount = re.sub(r'[₹,\s]', '', amount_str)
                    amount_value = float(clean_amount)
                    amounts.append({
                        'value': amount_value,
                        'text': match.group(0),
                        'position': match.span()
                    })
                except (ValueError, InvalidOperation):
                    continue
        
        return amounts
    
    def _extract_dates(self, text: str) -> List[Dict[str, Any]]:
        """Extract dates from text"""
        dates = []
        for pattern in self.date_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                dates.append({
                    'text': match.group(0),
                    'position': match.span()
                })
        
        return dates
    
    def _extract_pan_numbers(self, text: str) -> List[str]:
        """Extract PAN numbers"""
        return re.findall(self.pan_pattern, text)
    
    def _extract_gst_numbers(self, text: str) -> List[str]:
        """Extract GST numbers"""
        return re.findall(self.gst_pattern, text)
    
    def _extract_bank_statement_data(self, text: str) -> Dict[str, Any]:
        """Extract bank statement specific data"""
        data = {}
        
        # Extract account number
        account_pattern = r'account\s+no\.?\s*:?\s*([0-9]+)'
        account_match = re.search(account_pattern, text, re.IGNORECASE)
        if account_match:
            data['account_number'] = account_match.group(1)
        
        # Extract opening/closing balance
        balance_patterns = [
            r'opening\s+balance\s*:?\s*₹?\s*([0-9,]+\.?[0-9]*)',
            r'closing\s+balance\s*:?\s*₹?\s*([0-9,]+\.?[0-9]*)'
        ]
        
        for i, pattern in enumerate(balance_patterns):
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                balance_type = 'opening_balance' if i == 0 else 'closing_balance'
                data[balance_type] = float(re.sub(r'[,]', '', match.group(1)))
        
        return data
    
    def _extract_invoice_data(self, text: str) -> Dict[str, Any]:
        """Extract invoice specific data"""
        data = {}
        
        # Extract invoice number
        invoice_patterns = [
            r'invoice\s+no\.?\s*:?\s*([A-Z0-9/-]+)',
            r'bill\s+no\.?\s*:?\s*([A-Z0-9/-]+)'
        ]
        
        for pattern in invoice_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                data['invoice_number'] = match.group(1)
                break
        
        # Extract tax amounts
        tax_patterns = [
            r'cgst\s*@?\s*[0-9.]+%?\s*:?\s*₹?\s*([0-9,]+\.?[0-9]*)',
            r'sgst\s*@?\s*[0-9.]+%?\s*:?\s*₹?\s*([0-9,]+\.?[0-9]*)',
            r'igst\s*@?\s*[0-9.]+%?\s*:?\s*₹?\s*([0-9,]+\.?[0-9]*)'
        ]
        
        taxes = {}
        for pattern in tax_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                tax_type = pattern.split('\\')[0]
                taxes[tax_type] = float(re.sub(r'[,]', '', match.group(1)))
        
        if taxes:
            data['taxes'] = taxes
        
        return data
    
    def _extract_gst_data(self, text: str) -> Dict[str, Any]:
        """Extract GST report specific data"""
        data = {}
        
        # Extract GSTIN
        gstin_pattern = r'gstin\s*:?\s*([0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1})'
        gstin_match = re.search(gstin_pattern, text, re.IGNORECASE)
        if gstin_match:
            data['gstin'] = gstin_match.group(1)
        
        # Extract tax period
        period_pattern = r'tax\s+period\s*:?\s*([0-9]{2}/[0-9]{4})'
        period_match = re.search(period_pattern, text, re.IGNORECASE)
        if period_match:
            data['tax_period'] = period_match.group(1)
        
        return data
    
    def _extract_balance_sheet_data(self, text: str) -> Dict[str, Any]:
        """Extract balance sheet specific data"""
        data = {}
        
        # Extract major line items
        line_items = [
            'total_assets', 'current_assets', 'fixed_assets',
            'total_liabilities', 'current_liabilities', 'equity'
        ]
        
        for item in line_items:
            pattern = item.replace('_', r'\s+') + r'\s*:?\s*₹?\s*([0-9,]+\.?[0-9]*)'
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                data[item] = float(re.sub(r'[,]', '', match.group(1)))
        
        return data

class AnomalyDetector:
    """Detect anomalies in financial documents"""
    
    def __init__(self):
        self.amount_threshold = 1000000  # 10 Lakh
        self.date_range_days = 365
    
    def detect_anomalies(self, extracted_data: Dict[str, Any], document_type: str) -> List[Dict[str, Any]]:
        """
        Detect anomalies in extracted data.
        
        Args:
            extracted_data (Dict[str, Any]): Extracted financial data
            document_type (str): Type of document
            
        Returns:
            List[Dict[str, Any]]: List of detected anomalies
        """
        anomalies = []
        
        # Check for unusual amounts
        anomalies.extend(self._detect_unusual_amounts(extracted_data.get('amounts', [])))
        
        # Check for date inconsistencies
        anomalies.extend(self._detect_date_anomalies(extracted_data.get('dates', [])))
        
        # Document-specific anomaly detection
        if document_type == 'BANK_STATEMENT':
            anomalies.extend(self._detect_bank_statement_anomalies(extracted_data))
        elif document_type == 'INVOICE':
            anomalies.extend(self._detect_invoice_anomalies(extracted_data))
        
        return anomalies
    
    def _detect_unusual_amounts(self, amounts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect unusually large amounts"""
        anomalies = []
        
        for amount in amounts:
            if amount['value'] > self.amount_threshold:
                anomalies.append({
                    'type': 'UNUSUAL_AMOUNT',
                    'severity': 'HIGH',
                    'description': f'Unusually large amount: ₹{amount["value"]:,.2f}',
                    'affected_field': 'amount',
                    'actual_value': amount['value'],
                    'confidence': 0.8
                })
        
        return anomalies
    
    def _detect_date_anomalies(self, dates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect date-related anomalies"""
        anomalies = []
        
        # Check for future dates
        current_date = datetime.now()
        
        for date_item in dates:
            # This is a simplified check - in practice, parse dates properly
            if 'future' in date_item['text'].lower():  # Placeholder logic
                anomalies.append({
                    'type': 'DATE_INCONSISTENCY',
                    'severity': 'MEDIUM',
                    'description': f'Future date detected: {date_item["text"]}',
                    'affected_field': 'date',
                    'actual_value': date_item['text'],
                    'confidence': 0.7
                })
        
        return anomalies
    
    def _detect_bank_statement_anomalies(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Detect bank statement specific anomalies"""
        anomalies = []
        
        # Check balance consistency
        opening = data.get('opening_balance')
        closing = data.get('closing_balance')
        
        if opening is not None and closing is not None:
            if abs(opening - closing) > self.amount_threshold:
                anomalies.append({
                    'type': 'UNUSUAL_AMOUNT',
                    'severity': 'HIGH',
                    'description': f'Large balance change: ₹{abs(opening - closing):,.2f}',
                    'affected_field': 'balance_change',
                    'expected_value': 'Gradual change',
                    'actual_value': f'₹{abs(opening - closing):,.2f}',
                    'confidence': 0.9
                })
        
        return anomalies
    
    def _detect_invoice_anomalies(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Detect invoice specific anomalies"""
        anomalies = []
        
        # Check tax calculations
        taxes = data.get('taxes', {})
        if taxes:
            total_tax = sum(taxes.values())
            amounts = data.get('amounts', [])
            
            if amounts:
                max_amount = max(amount['value'] for amount in amounts)
                tax_rate = total_tax / max_amount if max_amount > 0 else 0
                
                # Check if tax rate is reasonable (0-30%)
                if tax_rate > 0.3:
                    anomalies.append({
                        'type': 'TAX_MISMATCH',
                        'severity': 'MEDIUM',
                        'description': f'Unusually high tax rate: {tax_rate*100:.1f}%',
                        'affected_field': 'tax_calculation',
                        'expected_value': '0-30%',
                        'actual_value': f'{tax_rate*100:.1f}%',
                        'confidence': 0.8
                    })
        
        return anomalies
