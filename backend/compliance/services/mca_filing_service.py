"""
MCA Filing Service
Handles MCA form filings, due date calculations, and compliance tracking
"""

from datetime import datetime, timedelta, date
from typing import Dict, List, Any, Optional, Tuple
from django.db import transaction
from django.utils import timezone
from django.contrib.auth.models import User
from ..models import (
    MCAForm, ComplianceRequirement, ComplianceCalendar, 
    ComplianceNotification, ComplianceAuditTrail
)
from clients.models import Client
import logging
import calendar

logger = logging.getLogger(__name__)

class MCAFilingService:
    """Service for managing MCA filings and compliance"""
    
    def __init__(self):
        self.current_year = datetime.now().year
        self.current_date = timezone.now().date()
    
    def calculate_due_date(self, form_number: str, client: Client, event_date: Optional[date] = None) -> date:
        """Calculate due date for MCA forms based on form type and client details"""
        
        # Get form details
        try:
            mca_form = MCAForm.objects.get(form_number=form_number, is_active=True)
        except MCAForm.DoesNotExist:
            logger.error(f"MCA Form {form_number} not found")
            return self.current_date + timedelta(days=30)  # Default 30 days
        
        # Financial year end date
        fy_end = client.financial_year_end or date(self.current_year, 3, 31)
        
        # Calculate due dates based on form type
        if form_number == 'AOC-4':  # Financial Statements
            # Due within 30 days of AGM or 6 months from FY end, whichever is earlier
            agm_deadline = fy_end + timedelta(days=180)  # 6 months from FY end
            return min(agm_deadline, fy_end + timedelta(days=210))  # 7 months max
        
        elif form_number == 'MGT-7':  # Annual Return
            # Due within 60 days of AGM
            agm_date = event_date or (fy_end + timedelta(days=180))
            return agm_date + timedelta(days=60)
        
        elif form_number == 'ADT-1':  # Auditor Appointment
            # Due within 15 days of appointment
            appointment_date = event_date or self.current_date
            return appointment_date + timedelta(days=15)
        
        elif form_number == 'DIR-12':  # Change in Director
            # Due within 30 days of change
            change_date = event_date or self.current_date
            return change_date + timedelta(days=30)
        
        elif form_number == 'SH-7':  # Share Allotment
            # Due within 30 days of allotment
            allotment_date = event_date or self.current_date
            return allotment_date + timedelta(days=30)
        
        elif form_number == 'CHG-1':  # Charge Creation
            # Due within 30 days of charge creation
            charge_date = event_date or self.current_date
            return charge_date + timedelta(days=30)
        
        elif form_number == 'INC-22':  # Registered Office Change
            # Due within 15 days of change
            change_date = event_date or self.current_date
            return change_date + timedelta(days=15)
        
        elif form_number == 'MGT-14':  # Board Resolution
            # Due within 30 days of resolution
            resolution_date = event_date or self.current_date
            return resolution_date + timedelta(days=30)
        
        elif form_number == 'FORM-8':  # Commencement of Business
            # Due within 180 days of incorporation
            incorporation_date = client.incorporation_date or self.current_date
            return incorporation_date + timedelta(days=180)
        
        elif form_number == 'DPT-3':  # Deposit Return
            # Due by 30th June every year
            return date(self.current_year, 6, 30)
        
        elif form_number == 'MSC-1':  # Cash Flow Statement
            # Due within 30 days of AGM
            agm_date = event_date or (fy_end + timedelta(days=180))
            return agm_date + timedelta(days=30)
        
        else:
            # Default calculation based on form's due_date_calculation field
            if hasattr(mca_form, 'due_date_calculation') and mca_form.due_date_calculation:
                # Parse the calculation logic (simplified)
                if 'days' in mca_form.due_date_calculation:
                    days = int(mca_form.due_date_calculation.split()[0])
                    base_date = event_date or self.current_date
                    return base_date + timedelta(days=days)
            
            # Fallback to 30 days
            return self.current_date + timedelta(days=30)
    
    def calculate_penalty(self, form_number: str, due_date: date, filing_date: Optional[date] = None) -> Dict[str, Any]:
        """Calculate penalty for late filing"""
        
        if not filing_date:
            filing_date = self.current_date
        
        if filing_date <= due_date:
            return {'penalty_amount': 0, 'additional_fee': 0, 'total_penalty': 0}
        
        days_late = (filing_date - due_date).days
        
        # Get form details for penalty structure
        try:
            mca_form = MCAForm.objects.get(form_number=form_number, is_active=True)
            penalty_structure = mca_form.penalty_structure
        except MCAForm.DoesNotExist:
            penalty_structure = {}
        
        # Default penalty calculation
        base_penalty = 0
        additional_fee = 0
        
        if form_number in ['AOC-4', 'MGT-7']:  # Annual filings
            if days_late <= 30:
                base_penalty = 200
            elif days_late <= 90:
                base_penalty = 500
            elif days_late <= 180:
                base_penalty = 1000
            else:
                base_penalty = 5000
                additional_fee = min(days_late * 100, 100000)  # Max 1 lakh
        
        elif form_number in ['DIR-12', 'SH-7', 'CHG-1']:  # Event-based filings
            if days_late <= 30:
                base_penalty = 100
            elif days_late <= 90:
                base_penalty = 300
            else:
                base_penalty = 500
                additional_fee = min(days_late * 50, 50000)  # Max 50k
        
        else:  # Other forms
            base_penalty = min(days_late * 10, 10000)  # Rs. 10 per day, max 10k
        
        total_penalty = base_penalty + additional_fee
        
        return {
            'penalty_amount': base_penalty,
            'additional_fee': additional_fee,
            'total_penalty': total_penalty,
            'days_late': days_late
        }
    
    def get_applicable_forms(self, client: Client) -> List[Dict[str, Any]]:
        """Get list of applicable MCA forms for a client"""
        
        applicable_forms = []
        
        # Determine applicability based on client type
        if client.entity_type == 'PUBLIC_LIMITED':
            applicability_filter = ['ALL', 'PUBLIC', 'LISTED' if client.is_listed else 'UNLISTED']
        elif client.entity_type == 'PRIVATE_LIMITED':
            if client.is_small_company:
                applicability_filter = ['ALL', 'PRIVATE', 'SMALL']
            else:
                applicability_filter = ['ALL', 'PRIVATE']
        elif client.entity_type == 'OPC':
            applicability_filter = ['ALL', 'OPC']
        elif client.entity_type == 'SECTION_8':
            applicability_filter = ['ALL', 'SECTION_8']
        else:
            applicability_filter = ['ALL']
        
        # Get applicable forms
        forms = MCAForm.objects.filter(
            applicability__in=applicability_filter,
            is_active=True,
            effective_from__lte=self.current_date
        ).filter(
            models.Q(effective_to__isnull=True) | models.Q(effective_to__gte=self.current_date)
        )
        
        for form in forms:
            due_date = self.calculate_due_date(form.form_number, client)
            penalty_info = self.calculate_penalty(form.form_number, due_date)
            
            applicable_forms.append({
                'form': form,
                'due_date': due_date,
                'days_remaining': (due_date - self.current_date).days,
                'is_overdue': due_date < self.current_date,
                'penalty_info': penalty_info,
                'priority': self._calculate_priority(due_date, form.is_mandatory)
            })
        
        # Sort by due date and priority
        applicable_forms.sort(key=lambda x: (x['due_date'], -x['priority']))
        
        return applicable_forms
    
    def _calculate_priority(self, due_date: date, is_mandatory: bool) -> int:
        """Calculate priority score for compliance requirement"""
        days_remaining = (due_date - self.current_date).days
        
        if days_remaining < 0:  # Overdue
            return 5 if is_mandatory else 4
        elif days_remaining <= 7:  # Due within a week
            return 4 if is_mandatory else 3
        elif days_remaining <= 30:  # Due within a month
            return 3 if is_mandatory else 2
        else:
            return 2 if is_mandatory else 1
    
    @transaction.atomic
    def create_compliance_requirements(self, client: Client, financial_year: Optional[int] = None) -> List[ComplianceRequirement]:
        """Create compliance requirements for a client for a financial year"""
        
        if not financial_year:
            financial_year = self.current_year
        
        applicable_forms = self.get_applicable_forms(client)
        requirements = []
        
        for form_info in applicable_forms:
            form = form_info['form']
            
            # Check if requirement already exists
            existing = ComplianceRequirement.objects.filter(
                client=client,
                mca_form=form,
                due_date__year=financial_year
            ).first()
            
            if existing:
                continue
            
            # Create new requirement
            requirement = ComplianceRequirement.objects.create(
                client=client,
                framework_id='mca_framework_id',  # Assuming MCA framework exists
                mca_form=form,
                requirement_title=f"{form.form_number} - {form.form_name}",
                description=form.description,
                due_date=form_info['due_date'],
                priority='HIGH' if form_info['priority'] >= 4 else 'MEDIUM' if form_info['priority'] >= 2 else 'LOW',
                estimated_hours=self._estimate_hours(form.form_number),
                fees_amount=self._get_fees_amount(form.form_number, client)
            )
            
            requirements.append(requirement)
            
            # Create calendar entry
            self._create_calendar_entry(requirement)
            
            # Create audit trail
            ComplianceAuditTrail.objects.create(
                compliance_requirement=requirement,
                action_type='CREATED',
                action_description=f"Compliance requirement created for {form.form_number}",
                performed_by=None,  # System generated
                new_values={
                    'form_number': form.form_number,
                    'due_date': str(form_info['due_date']),
                    'priority': requirement.priority
                }
            )
        
        logger.info(f"Created {len(requirements)} compliance requirements for {client.name}")
        return requirements
    
    def _estimate_hours(self, form_number: str) -> float:
        """Estimate hours required for form completion"""
        hour_estimates = {
            'AOC-4': 8.0,
            'MGT-7': 4.0,
            'ADT-1': 1.0,
            'DIR-12': 2.0,
            'SH-7': 3.0,
            'CHG-1': 2.0,
            'INC-22': 1.5,
            'MGT-14': 1.0,
            'FORM-8': 2.0,
            'DPT-3': 3.0,
            'MSC-1': 2.0
        }
        return hour_estimates.get(form_number, 2.0)
    
    def _get_fees_amount(self, form_number: str, client: Client) -> float:
        """Get fees amount for form filing"""
        # Base fees structure
        base_fees = {
            'AOC-4': 500,
            'MGT-7': 300,
            'ADT-1': 100,
            'DIR-12': 100,
            'SH-7': 300,
            'CHG-1': 300,
            'INC-22': 200,
            'MGT-14': 100,
            'FORM-8': 200,
            'DPT-3': 100,
            'MSC-1': 100
        }
        
        base_fee = base_fees.get(form_number, 100)
        
        # Adjust based on company size
        if client.entity_type == 'PUBLIC_LIMITED':
            return base_fee * 2
        elif client.is_small_company:
            return base_fee * 0.5
        
        return base_fee
    
    def _create_calendar_entry(self, requirement: ComplianceRequirement):
        """Create calendar entry for compliance requirement"""
        
        # Calculate reminder dates
        due_date = requirement.due_date
        reminder_dates = [
            (due_date - timedelta(days=30)).isoformat(),  # 30 days before
            (due_date - timedelta(days=15)).isoformat(),  # 15 days before
            (due_date - timedelta(days=7)).isoformat(),   # 7 days before
            (due_date - timedelta(days=1)).isoformat()    # 1 day before
        ]
        
        # Filter out past dates
        reminder_dates = [d for d in reminder_dates if datetime.fromisoformat(d).date() >= self.current_date]
        
        ComplianceCalendar.objects.create(
            client=requirement.client,
            title=requirement.requirement_title,
            description=requirement.description,
            compliance_type='MCA_FILING',
            due_date=requirement.due_date,
            reminder_dates=reminder_dates,
            is_recurring=requirement.mca_form.filing_frequency == 'Annual',
            recurrence_pattern='YEARLY' if requirement.mca_form.filing_frequency == 'Annual' else '',
            assigned_to=requirement.assigned_to,
            status=requirement.status,
            priority=requirement.priority,
            estimated_cost=float(requirement.fees_amount)
        )
    
    def generate_compliance_calendar(self, client: Client, year: int) -> Dict[str, List[Dict]]:
        """Generate compliance calendar for a client for a specific year"""
        
        requirements = ComplianceRequirement.objects.filter(
            client=client,
            due_date__year=year
        ).select_related('mca_form').order_by('due_date')
        
        calendar_data = {}
        
        for requirement in requirements:
            month_key = requirement.due_date.strftime('%Y-%m')
            
            if month_key not in calendar_data:
                calendar_data[month_key] = []
            
            calendar_data[month_key].append({
                'id': str(requirement.id),
                'title': requirement.requirement_title,
                'due_date': requirement.due_date.isoformat(),
                'status': requirement.status,
                'priority': requirement.priority,
                'form_number': requirement.mca_form.form_number if requirement.mca_form else '',
                'days_remaining': (requirement.due_date - self.current_date).days,
                'is_overdue': requirement.is_overdue,
                'penalty_amount': self.calculate_penalty(
                    requirement.mca_form.form_number if requirement.mca_form else '',
                    requirement.due_date
                )['total_penalty']
            })
        
        return calendar_data
    
    def get_overdue_filings(self, client: Optional[Client] = None) -> List[Dict[str, Any]]:
        """Get all overdue filings"""
        
        query = ComplianceRequirement.objects.filter(
            due_date__lt=self.current_date,
            status__in=['PENDING', 'IN_PROGRESS']
        ).select_related('client', 'mca_form')
        
        if client:
            query = query.filter(client=client)
        
        overdue_filings = []
        
        for requirement in query:
            penalty_info = self.calculate_penalty(
                requirement.mca_form.form_number if requirement.mca_form else '',
                requirement.due_date
            )
            
            overdue_filings.append({
                'requirement': requirement,
                'days_overdue': (self.current_date - requirement.due_date).days,
                'penalty_info': penalty_info,
                'client_name': requirement.client.name,
                'form_number': requirement.mca_form.form_number if requirement.mca_form else '',
                'urgency_score': self._calculate_urgency_score(requirement, penalty_info)
            })
        
        # Sort by urgency score (highest first)
        overdue_filings.sort(key=lambda x: x['urgency_score'], reverse=True)
        
        return overdue_filings
    
    def _calculate_urgency_score(self, requirement: ComplianceRequirement, penalty_info: Dict) -> int:
        """Calculate urgency score for overdue filing"""
        score = 0
        
        # Days overdue (1 point per day, max 100)
        days_overdue = (self.current_date - requirement.due_date).days
        score += min(days_overdue, 100)
        
        # Penalty amount (1 point per 100 Rs, max 100)
        score += min(penalty_info['total_penalty'] // 100, 100)
        
        # Priority multiplier
        priority_multiplier = {'HIGH': 3, 'MEDIUM': 2, 'LOW': 1}
        score *= priority_multiplier.get(requirement.priority, 1)
        
        # Mandatory forms get higher score
        if requirement.mca_form and requirement.mca_form.is_mandatory:
            score *= 1.5
        
        return int(score)
    
    def send_compliance_reminders(self) -> int:
        """Send compliance reminders for upcoming deadlines"""
        
        # Get requirements due in next 30 days
        upcoming_deadline = self.current_date + timedelta(days=30)
        
        requirements = ComplianceRequirement.objects.filter(
            due_date__gte=self.current_date,
            due_date__lte=upcoming_deadline,
            status__in=['PENDING', 'IN_PROGRESS']
        ).select_related('client', 'assigned_to', 'mca_form')
        
        notifications_sent = 0
        
        for requirement in requirements:
            days_remaining = (requirement.due_date - self.current_date).days
            
            # Determine notification type and priority
            if days_remaining <= 1:
                notification_type = 'DEADLINE_REMINDER'
                priority = 'URGENT'
            elif days_remaining <= 7:
                notification_type = 'DEADLINE_REMINDER'
                priority = 'HIGH'
            elif days_remaining <= 15:
                notification_type = 'DEADLINE_REMINDER'
                priority = 'MEDIUM'
            else:
                notification_type = 'DEADLINE_REMINDER'
                priority = 'LOW'
            
            # Create notification
            notification = ComplianceNotification.objects.create(
                compliance_requirement=requirement,
                notification_type=notification_type,
                title=f"Compliance Deadline: {requirement.requirement_title}",
                message=f"The compliance requirement '{requirement.requirement_title}' for {requirement.client.name} is due on {requirement.due_date}. {days_remaining} days remaining.",
                priority=priority,
                scheduled_time=timezone.now()
            )
            
            # Add recipients
            recipients = []
            if requirement.assigned_to:
                recipients.append(requirement.assigned_to)
            
            # Add client's primary contact
            if hasattr(requirement.client, 'primary_contact') and requirement.client.primary_contact:
                recipients.append(requirement.client.primary_contact)
            
            if recipients:
                notification.recipients.set(recipients)
                notifications_sent += 1
        
        logger.info(f"Sent {notifications_sent} compliance reminder notifications")
        return notifications_sent
