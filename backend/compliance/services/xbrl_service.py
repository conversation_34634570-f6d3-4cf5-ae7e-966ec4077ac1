"""
XBRL Processing Service
Handles XBRL document creation, validation, and filing for Indian compliance
"""

import os
import xml.etree.ElementTree as ET
from datetime import datetime, date
from typing import Dict, List, Any, Optional, Tuple
from django.conf import settings
from django.core.files.base import ContentFile
from django.db import transaction
from ..models import XBRLTaxonomy, XBRLFiling, ComplianceRequirement
from clients.models import Client
import logging
import json
import requests
from decimal import Decimal

logger = logging.getLogger(__name__)

class XBRLService:
    """Service for XBRL document processing and validation"""
    
    def __init__(self):
        self.namespaces = {
            'xbrli': 'http://www.xbrl.org/2003/instance',
            'link': 'http://www.xbrl.org/2003/linkbase',
            'xlink': 'http://www.w3.org/1999/xlink',
            'xsi': 'http://www.w3.org/2001/XMLSchema-instance',
            'iso4217': 'http://www.xbrl.org/2003/iso4217',
            'in-gaap': 'http://www.mca.gov.in/xbrl/taxonomy/in-gaap',
            'in-gaap-ci': 'http://www.mca.gov.in/xbrl/taxonomy/in-gaap-ci'
        }
        
        # Common Indian GAAP concepts
        self.common_concepts = {
            'revenue': 'in-gaap:Revenue',
            'total_income': 'in-gaap:TotalIncome',
            'cost_of_goods_sold': 'in-gaap:CostOfGoodsSold',
            'gross_profit': 'in-gaap:GrossProfit',
            'operating_expenses': 'in-gaap:OperatingExpenses',
            'ebitda': 'in-gaap:EarningsBeforeInterestTaxDepreciationAmortisation',
            'depreciation': 'in-gaap:DepreciationAmortisationExpense',
            'finance_costs': 'in-gaap:FinanceCosts',
            'profit_before_tax': 'in-gaap:ProfitBeforeTax',
            'tax_expense': 'in-gaap:TaxExpense',
            'profit_after_tax': 'in-gaap:ProfitAfterTax',
            'total_assets': 'in-gaap:TotalAssets',
            'current_assets': 'in-gaap:CurrentAssets',
            'non_current_assets': 'in-gaap:NonCurrentAssets',
            'total_liabilities': 'in-gaap:TotalLiabilities',
            'current_liabilities': 'in-gaap:CurrentLiabilities',
            'non_current_liabilities': 'in-gaap:NonCurrentLiabilities',
            'equity': 'in-gaap:TotalEquity',
            'share_capital': 'in-gaap:ShareCapital',
            'reserves_surplus': 'in-gaap:ReservesAndSurplus'
        }
    
    def create_xbrl_instance(self, client: Client, financial_data: Dict[str, Any], 
                           reporting_period: Tuple[date, date], taxonomy: XBRLTaxonomy) -> str:
        """Create XBRL instance document"""
        
        period_start, period_end = reporting_period
        
        # Create root element
        root = ET.Element('xbrli:xbrl')
        
        # Add namespaces
        for prefix, uri in self.namespaces.items():
            root.set(f'xmlns:{prefix}', uri)
        
        # Add schema reference
        schema_ref = ET.SubElement(root, 'link:schemaRef')
        schema_ref.set('xlink:type', 'simple')
        schema_ref.set('xlink:href', taxonomy.schema_location)
        
        # Add context for the reporting period
        context = ET.SubElement(root, 'xbrli:context')
        context.set('id', f'FY{period_end.year}')
        
        # Entity information
        entity = ET.SubElement(context, 'xbrli:entity')
        identifier = ET.SubElement(entity, 'xbrli:identifier')
        identifier.set('scheme', 'http://www.mca.gov.in')
        identifier.text = client.cin_number or client.registration_number
        
        # Period information
        period = ET.SubElement(context, 'xbrli:period')
        start_date = ET.SubElement(period, 'xbrli:startDate')
        start_date.text = period_start.isoformat()
        end_date = ET.SubElement(period, 'xbrli:endDate')
        end_date.text = period_end.isoformat()
        
        # Unit for monetary values (INR)
        unit = ET.SubElement(root, 'xbrli:unit')
        unit.set('id', 'INR')
        measure = ET.SubElement(unit, 'xbrli:measure')
        measure.text = 'iso4217:INR'
        
        # Add financial facts
        self._add_financial_facts(root, financial_data, f'FY{period_end.year}', 'INR')
        
        # Add company information facts
        self._add_company_facts(root, client, f'FY{period_end.year}')
        
        # Convert to string
        ET.register_namespace('xbrli', self.namespaces['xbrli'])
        ET.register_namespace('link', self.namespaces['link'])
        ET.register_namespace('xlink', self.namespaces['xlink'])
        
        xml_string = ET.tostring(root, encoding='unicode', xml_declaration=True)
        
        # Pretty print
        return self._pretty_print_xml(xml_string)
    
    def _add_financial_facts(self, root: ET.Element, financial_data: Dict[str, Any], 
                           context_ref: str, unit_ref: str):
        """Add financial facts to XBRL instance"""
        
        for key, value in financial_data.items():
            if key in self.common_concepts and value is not None:
                concept_name = self.common_concepts[key]
                
                fact = ET.SubElement(root, concept_name)
                fact.set('contextRef', context_ref)
                fact.set('unitRef', unit_ref)
                fact.set('decimals', '0')
                
                # Convert to integer for monetary values
                if isinstance(value, (int, float, Decimal)):
                    fact.text = str(int(value))
                else:
                    fact.text = str(value)
    
    def _add_company_facts(self, root: ET.Element, client: Client, context_ref: str):
        """Add company information facts"""
        
        # Company name
        if client.name:
            name_fact = ET.SubElement(root, 'in-gaap:NameOfCompany')
            name_fact.set('contextRef', context_ref)
            name_fact.text = client.name
        
        # CIN number
        if client.cin_number:
            cin_fact = ET.SubElement(root, 'in-gaap:CorporateIdentificationNumber')
            cin_fact.set('contextRef', context_ref)
            cin_fact.text = client.cin_number
        
        # Date of incorporation
        if client.incorporation_date:
            inc_fact = ET.SubElement(root, 'in-gaap:DateOfIncorporation')
            inc_fact.set('contextRef', context_ref)
            inc_fact.text = client.incorporation_date.isoformat()
        
        # Registered office address
        if client.registered_address:
            address_fact = ET.SubElement(root, 'in-gaap:RegisteredOfficeAddress')
            address_fact.set('contextRef', context_ref)
            address_fact.text = client.registered_address
    
    def _pretty_print_xml(self, xml_string: str) -> str:
        """Pretty print XML string"""
        try:
            import xml.dom.minidom
            dom = xml.dom.minidom.parseString(xml_string)
            return dom.toprettyxml(indent="  ")
        except:
            return xml_string
    
    def validate_xbrl_instance(self, xbrl_content: str, taxonomy: XBRLTaxonomy) -> Dict[str, Any]:
        """Validate XBRL instance document"""
        
        validation_results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'business_rules_errors': []
        }
        
        try:
            # Parse XML
            root = ET.fromstring(xbrl_content)
            
            # Basic XML validation
            self._validate_xml_structure(root, validation_results)
            
            # Schema validation
            self._validate_against_schema(root, taxonomy, validation_results)
            
            # Business rules validation
            self._validate_business_rules(root, validation_results)
            
            # Calculate validation score
            error_count = len(validation_results['errors'])
            warning_count = len(validation_results['warnings'])
            business_error_count = len(validation_results['business_rules_errors'])
            
            validation_results['is_valid'] = error_count == 0 and business_error_count == 0
            validation_results['validation_score'] = max(0, 100 - (error_count * 10) - (warning_count * 2) - (business_error_count * 5))
            
        except ET.ParseError as e:
            validation_results['is_valid'] = False
            validation_results['errors'].append(f"XML Parse Error: {str(e)}")
            validation_results['validation_score'] = 0
        
        except Exception as e:
            validation_results['is_valid'] = False
            validation_results['errors'].append(f"Validation Error: {str(e)}")
            validation_results['validation_score'] = 0
        
        return validation_results
    
    def _validate_xml_structure(self, root: ET.Element, validation_results: Dict):
        """Validate basic XML structure"""
        
        # Check root element
        if root.tag != f"{{{self.namespaces['xbrli']}}}xbrl":
            validation_results['errors'].append("Root element must be xbrli:xbrl")
        
        # Check for required elements
        required_elements = ['context', 'unit']
        for element in required_elements:
            if not root.find(f".//{{{self.namespaces['xbrli']}}}{element}"):
                validation_results['errors'].append(f"Missing required element: {element}")
        
        # Check context references
        contexts = root.findall(f".//{{{self.namespaces['xbrli']}}}context")
        context_ids = [ctx.get('id') for ctx in contexts]
        
        # Find all facts with contextRef
        facts = root.findall(".//*[@contextRef]")
        for fact in facts:
            context_ref = fact.get('contextRef')
            if context_ref not in context_ids:
                validation_results['errors'].append(f"Invalid contextRef: {context_ref}")
    
    def _validate_against_schema(self, root: ET.Element, taxonomy: XBRLTaxonomy, validation_results: Dict):
        """Validate against taxonomy schema"""
        
        # This is a simplified validation
        # In production, you would use a proper XBRL processor like Arelle
        
        # Check schema reference
        schema_refs = root.findall(f".//{{{self.namespaces['link']}}}schemaRef")
        if not schema_refs:
            validation_results['errors'].append("Missing schema reference")
        else:
            schema_ref = schema_refs[0].get(f"{{{self.namespaces['xlink']}}}href")
            if schema_ref != taxonomy.schema_location:
                validation_results['warnings'].append(f"Schema reference mismatch: {schema_ref}")
    
    def _validate_business_rules(self, root: ET.Element, validation_results: Dict):
        """Validate business rules"""
        
        # Extract financial values for validation
        financial_values = {}
        
        for concept, xpath in self.common_concepts.items():
            elements = root.findall(f".//{xpath.replace(':', f':{{{self.namespaces[xpath.split(":")[0]]}}}')}")
            if elements:
                try:
                    financial_values[concept] = float(elements[0].text or 0)
                except ValueError:
                    validation_results['warnings'].append(f"Invalid numeric value for {concept}")
        
        # Business rule validations
        
        # 1. Total Assets = Total Liabilities + Equity
        if all(k in financial_values for k in ['total_assets', 'total_liabilities', 'equity']):
            assets = financial_values['total_assets']
            liabilities_equity = financial_values['total_liabilities'] + financial_values['equity']
            
            if abs(assets - liabilities_equity) > 1:  # Allow for rounding differences
                validation_results['business_rules_errors'].append(
                    f"Balance Sheet equation not balanced: Assets ({assets}) != Liabilities + Equity ({liabilities_equity})"
                )
        
        # 2. Gross Profit = Revenue - Cost of Goods Sold
        if all(k in financial_values for k in ['gross_profit', 'revenue', 'cost_of_goods_sold']):
            calculated_gross_profit = financial_values['revenue'] - financial_values['cost_of_goods_sold']
            reported_gross_profit = financial_values['gross_profit']
            
            if abs(calculated_gross_profit - reported_gross_profit) > 1:
                validation_results['business_rules_errors'].append(
                    f"Gross Profit calculation error: Expected {calculated_gross_profit}, Got {reported_gross_profit}"
                )
        
        # 3. Profit After Tax = Profit Before Tax - Tax Expense
        if all(k in financial_values for k in ['profit_after_tax', 'profit_before_tax', 'tax_expense']):
            calculated_pat = financial_values['profit_before_tax'] - financial_values['tax_expense']
            reported_pat = financial_values['profit_after_tax']
            
            if abs(calculated_pat - reported_pat) > 1:
                validation_results['business_rules_errors'].append(
                    f"Profit After Tax calculation error: Expected {calculated_pat}, Got {reported_pat}"
                )
        
        # 4. Check for negative values where not expected
        non_negative_items = ['total_assets', 'current_assets', 'non_current_assets', 'share_capital']
        for item in non_negative_items:
            if item in financial_values and financial_values[item] < 0:
                validation_results['business_rules_errors'].append(
                    f"{item} cannot be negative: {financial_values[item]}"
                )
    
    @transaction.atomic
    def create_xbrl_filing(self, client: Client, financial_data: Dict[str, Any], 
                          filing_type: str, reporting_period: Tuple[date, date],
                          taxonomy_id: str, prepared_by: Optional[Any] = None) -> XBRLFiling:
        """Create XBRL filing record"""
        
        try:
            taxonomy = XBRLTaxonomy.objects.get(id=taxonomy_id, is_active=True)
        except XBRLTaxonomy.DoesNotExist:
            raise ValueError(f"Taxonomy {taxonomy_id} not found")
        
        period_start, period_end = reporting_period
        
        # Create XBRL instance document
        xbrl_content = self.create_xbrl_instance(client, financial_data, reporting_period, taxonomy)
        
        # Validate the document
        validation_results = self.validate_xbrl_instance(xbrl_content, taxonomy)
        
        # Calculate due date based on filing type
        due_date = self._calculate_xbrl_due_date(filing_type, period_end)
        
        # Create filing record
        filing = XBRLFiling.objects.create(
            client=client,
            taxonomy=taxonomy,
            filing_type=filing_type,
            reporting_period_start=period_start,
            reporting_period_end=period_end,
            due_date=due_date,
            status='DRAFT',
            prepared_by=prepared_by,
            validation_errors=validation_results['errors'],
            business_rules_errors=validation_results['business_rules_errors']
        )
        
        # Save XBRL instance document
        filename = f"xbrl_instance_{client.cin_number}_{period_end.strftime('%Y%m%d')}.xml"
        filing.instance_document.save(
            filename,
            ContentFile(xbrl_content.encode('utf-8')),
            save=True
        )
        
        # Save validation report
        validation_report = self._generate_validation_report(validation_results)
        validation_filename = f"validation_report_{client.cin_number}_{period_end.strftime('%Y%m%d')}.json"
        filing.validation_report.save(
            validation_filename,
            ContentFile(validation_report.encode('utf-8')),
            save=True
        )
        
        # Update status based on validation
        if validation_results['is_valid']:
            filing.status = 'UNDER_REVIEW'
        else:
            filing.status = 'DRAFT'
        
        filing.save()
        
        logger.info(f"Created XBRL filing {filing.id} for {client.name}")
        return filing
    
    def _calculate_xbrl_due_date(self, filing_type: str, period_end: date) -> date:
        """Calculate due date for XBRL filing"""
        
        if filing_type == 'ANNUAL':
            # Annual filings due within 30 days of AGM or 6 months from year end
            return date(period_end.year + 1, 9, 30)  # Assuming Sept 30 as typical deadline
        
        elif filing_type == 'QUARTERLY':
            # Quarterly filings due within 45 days of quarter end
            if period_end.month == 3:  # Q4
                return date(period_end.year, 5, 15)
            elif period_end.month == 6:  # Q1
                return date(period_end.year, 8, 15)
            elif period_end.month == 9:  # Q2
                return date(period_end.year, 11, 15)
            else:  # Q3 (Dec)
                return date(period_end.year + 1, 2, 15)
        
        elif filing_type == 'HALF_YEARLY':
            # Half yearly filings due within 60 days
            if period_end.month == 9:  # H1
                return date(period_end.year, 11, 30)
            else:  # H2 (March)
                return date(period_end.year, 5, 30)
        
        else:
            # Event based filings - 30 days default
            return period_end + timedelta(days=30)
    
    def _generate_validation_report(self, validation_results: Dict[str, Any]) -> str:
        """Generate validation report in JSON format"""
        
        report = {
            'validation_timestamp': datetime.now().isoformat(),
            'validation_summary': {
                'is_valid': validation_results['is_valid'],
                'validation_score': validation_results.get('validation_score', 0),
                'total_errors': len(validation_results['errors']),
                'total_warnings': len(validation_results['warnings']),
                'total_business_rules_errors': len(validation_results['business_rules_errors'])
            },
            'validation_details': validation_results
        }
        
        return json.dumps(report, indent=2)
    
    def submit_xbrl_filing(self, filing_id: str, submitted_by: Optional[Any] = None) -> bool:
        """Submit XBRL filing to MCA portal (mock implementation)"""
        
        try:
            filing = XBRLFiling.objects.get(id=filing_id)
            
            # Validate filing is ready for submission
            if filing.status not in ['APPROVED']:
                raise ValueError(f"Filing status must be APPROVED for submission, current status: {filing.status}")
            
            if not filing.instance_document:
                raise ValueError("Instance document is required for submission")
            
            # Mock submission to MCA portal
            # In production, this would integrate with actual MCA API
            submission_result = self._mock_mca_submission(filing)
            
            if submission_result['success']:
                filing.status = 'FILED'
                filing.filing_date = date.today()
                filing.filing_reference = submission_result['filing_reference']
                filing.mca_acknowledgment = submission_result['acknowledgment_number']
                filing.save()
                
                logger.info(f"Successfully submitted XBRL filing {filing_id}")
                return True
            else:
                filing.status = 'REJECTED'
                filing.notes = f"Submission failed: {submission_result['error']}"
                filing.save()
                
                logger.error(f"XBRL filing submission failed: {submission_result['error']}")
                return False
        
        except XBRLFiling.DoesNotExist:
            logger.error(f"XBRL filing {filing_id} not found")
            return False
        
        except Exception as e:
            logger.error(f"Error submitting XBRL filing {filing_id}: {str(e)}")
            return False
    
    def _mock_mca_submission(self, filing: XBRLFiling) -> Dict[str, Any]:
        """Mock MCA submission (replace with actual API integration)"""
        
        # Simulate submission process
        import random
        import string
        
        # Generate mock reference numbers
        filing_reference = f"XBRL{datetime.now().strftime('%Y%m%d')}{''.join(random.choices(string.digits, k=6))}"
        acknowledgment_number = f"ACK{datetime.now().strftime('%Y%m%d')}{''.join(random.choices(string.digits, k=8))}"
        
        # Simulate success/failure (90% success rate)
        success = random.random() > 0.1
        
        if success:
            return {
                'success': True,
                'filing_reference': filing_reference,
                'acknowledgment_number': acknowledgment_number,
                'submission_timestamp': datetime.now().isoformat()
            }
        else:
            return {
                'success': False,
                'error': 'Technical error in MCA portal',
                'error_code': 'MCA_TECH_ERROR'
            }
    
    def get_filing_status(self, filing_id: str) -> Dict[str, Any]:
        """Get current status of XBRL filing"""
        
        try:
            filing = XBRLFiling.objects.get(id=filing_id)
            
            return {
                'filing_id': str(filing.id),
                'client_name': filing.client.name,
                'filing_type': filing.filing_type,
                'reporting_period': f"{filing.reporting_period_start} to {filing.reporting_period_end}",
                'status': filing.status,
                'due_date': filing.due_date.isoformat(),
                'filing_date': filing.filing_date.isoformat() if filing.filing_date else None,
                'filing_reference': filing.filing_reference,
                'validation_errors': len(filing.validation_errors),
                'business_rules_errors': len(filing.business_rules_errors),
                'is_overdue': filing.due_date < date.today() and filing.status != 'FILED'
            }
        
        except XBRLFiling.DoesNotExist:
            return {'error': f'Filing {filing_id} not found'}
    
    def get_available_taxonomies(self, effective_date: Optional[date] = None) -> List[Dict[str, Any]]:
        """Get list of available XBRL taxonomies"""
        
        if not effective_date:
            effective_date = date.today()
        
        taxonomies = XBRLTaxonomy.objects.filter(
            is_active=True,
            effective_date__lte=effective_date
        ).filter(
            models.Q(expiry_date__isnull=True) | models.Q(expiry_date__gte=effective_date)
        ).order_by('-effective_date')
        
        return [
            {
                'id': str(taxonomy.id),
                'name': taxonomy.name,
                'taxonomy_type': taxonomy.taxonomy_type,
                'version': taxonomy.version,
                'effective_date': taxonomy.effective_date.isoformat(),
                'expiry_date': taxonomy.expiry_date.isoformat() if taxonomy.expiry_date else None,
                'description': taxonomy.description
            }
            for taxonomy in taxonomies
        ]
