"""
Indian Compliance Models
Models for MCA filings, ROC compliance, XBRL reporting, and other Indian regulatory requirements
"""

from django.db import models
from django.contrib.auth.models import User
from django.core.validators import RegexValidator
from django.utils import timezone
from datetime import datetime, timedelta
import uuid

class ComplianceFramework(models.Model):
    """Master table for compliance frameworks"""
    FRAMEWORK_TYPES = [
        ('MCA', 'Ministry of Corporate Affairs'),
        ('ROC', 'Registrar of Companies'),
        ('SEBI', 'Securities and Exchange Board of India'),
        ('RBI', 'Reserve Bank of India'),
        ('ITAT', 'Income Tax Appellate Tribunal'),
        ('GST', 'Goods and Services Tax'),
        ('FEMA', 'Foreign Exchange Management Act'),
        ('LABOUR', 'Labour Laws'),
        ('ENVIRONMENT', 'Environmental Compliance'),
        ('OTHERS', 'Other Regulatory Bodies')
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.Char<PERSON>ield(max_length=200)
    framework_type = models.CharField(max_length=20, choices=FRAMEWORK_TYPES)
    description = models.TextField()
    regulatory_body = models.CharField(max_length=200)
    website_url = models.URLField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'compliance_frameworks'
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.framework_type})"

class MCAForm(models.Model):
    """MCA Forms and their details"""
    FORM_CATEGORIES = [
        ('INCORPORATION', 'Incorporation Forms'),
        ('ANNUAL', 'Annual Filing Forms'),
        ('EVENT_BASED', 'Event Based Forms'),
        ('COMPLIANCE', 'Compliance Forms'),
        ('CLOSURE', 'Closure/Winding Up Forms')
    ]
    
    APPLICABILITY = [
        ('ALL', 'All Companies'),
        ('PUBLIC', 'Public Companies Only'),
        ('PRIVATE', 'Private Companies Only'),
        ('LISTED', 'Listed Companies Only'),
        ('UNLISTED', 'Unlisted Companies Only'),
        ('SMALL', 'Small Companies'),
        ('OPC', 'One Person Companies'),
        ('SECTION_8', 'Section 8 Companies')
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    form_number = models.CharField(max_length=20, unique=True)
    form_name = models.CharField(max_length=300)
    category = models.CharField(max_length=20, choices=FORM_CATEGORIES)
    applicability = models.CharField(max_length=20, choices=APPLICABILITY)
    description = models.TextField()
    filing_frequency = models.CharField(max_length=50)  # Annual, Event-based, etc.
    due_date_calculation = models.TextField()  # Logic for calculating due dates
    penalty_structure = models.JSONField(default=dict)
    required_documents = models.JSONField(default=list)
    fees_structure = models.JSONField(default=dict)
    is_mandatory = models.BooleanField(default=True)
    is_active = models.BooleanField(default=True)
    effective_from = models.DateField()
    effective_to = models.DateField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'mca_forms'
        ordering = ['form_number']
    
    def __str__(self):
        return f"{self.form_number} - {self.form_name}"

class ComplianceRequirement(models.Model):
    """Compliance requirements for clients"""
    REQUIREMENT_STATUS = [
        ('PENDING', 'Pending'),
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('OVERDUE', 'Overdue'),
        ('NOT_APPLICABLE', 'Not Applicable'),
        ('EXEMPTED', 'Exempted')
    ]
    
    PRIORITY_LEVELS = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical')
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    client = models.ForeignKey('clients.Client', on_delete=models.CASCADE, related_name='compliance_requirements')
    framework = models.ForeignKey(ComplianceFramework, on_delete=models.CASCADE)
    mca_form = models.ForeignKey(MCAForm, on_delete=models.CASCADE, null=True, blank=True)
    requirement_title = models.CharField(max_length=300)
    description = models.TextField()
    due_date = models.DateField()
    status = models.CharField(max_length=20, choices=REQUIREMENT_STATUS, default='PENDING')
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS, default='MEDIUM')
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    estimated_hours = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    actual_hours = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    fees_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    penalty_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    completion_date = models.DateField(null=True, blank=True)
    filing_reference = models.CharField(max_length=100, blank=True)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'compliance_requirements'
        ordering = ['due_date', 'priority']
    
    def __str__(self):
        return f"{self.client.name} - {self.requirement_title}"
    
    @property
    def days_remaining(self):
        if self.status == 'COMPLETED':
            return 0
        today = timezone.now().date()
        return (self.due_date - today).days
    
    @property
    def is_overdue(self):
        return self.days_remaining < 0 and self.status != 'COMPLETED'

class XBRLTaxonomy(models.Model):
    """XBRL Taxonomies for different reporting requirements"""
    TAXONOMY_TYPES = [
        ('IFRS', 'IFRS Taxonomy'),
        ('IND_AS', 'Ind AS Taxonomy'),
        ('IGAAP', 'Indian GAAP Taxonomy'),
        ('BANKING', 'Banking Taxonomy'),
        ('INSURANCE', 'Insurance Taxonomy'),
        ('NBFC', 'NBFC Taxonomy')
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    taxonomy_type = models.CharField(max_length=20, choices=TAXONOMY_TYPES)
    version = models.CharField(max_length=20)
    effective_date = models.DateField()
    expiry_date = models.DateField(null=True, blank=True)
    description = models.TextField()
    taxonomy_url = models.URLField()
    schema_location = models.TextField()
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'xbrl_taxonomies'
        unique_together = ['name', 'version']
        ordering = ['-effective_date']
    
    def __str__(self):
        return f"{self.name} v{self.version}"

class XBRLFiling(models.Model):
    """XBRL Filing records"""
    FILING_STATUS = [
        ('DRAFT', 'Draft'),
        ('UNDER_REVIEW', 'Under Review'),
        ('APPROVED', 'Approved'),
        ('FILED', 'Filed'),
        ('REJECTED', 'Rejected'),
        ('RESUBMITTED', 'Resubmitted')
    ]
    
    FILING_TYPES = [
        ('ANNUAL', 'Annual Filing'),
        ('QUARTERLY', 'Quarterly Filing'),
        ('HALF_YEARLY', 'Half Yearly Filing'),
        ('EVENT_BASED', 'Event Based Filing')
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    client = models.ForeignKey('clients.Client', on_delete=models.CASCADE, related_name='xbrl_filings')
    taxonomy = models.ForeignKey(XBRLTaxonomy, on_delete=models.CASCADE)
    filing_type = models.CharField(max_length=20, choices=FILING_TYPES)
    reporting_period_start = models.DateField()
    reporting_period_end = models.DateField()
    filing_date = models.DateField(null=True, blank=True)
    due_date = models.DateField()
    status = models.CharField(max_length=20, choices=FILING_STATUS, default='DRAFT')
    instance_document = models.FileField(upload_to='xbrl/instances/', null=True, blank=True)
    validation_report = models.FileField(upload_to='xbrl/validations/', null=True, blank=True)
    filing_reference = models.CharField(max_length=100, blank=True)
    mca_acknowledgment = models.CharField(max_length=100, blank=True)
    prepared_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='prepared_xbrl_filings')
    reviewed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='reviewed_xbrl_filings')
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_xbrl_filings')
    validation_errors = models.JSONField(default=list)
    business_rules_errors = models.JSONField(default=list)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'xbrl_filings'
        ordering = ['-reporting_period_end']
    
    def __str__(self):
        return f"{self.client.name} - {self.filing_type} ({self.reporting_period_end})"

class ROCCompliance(models.Model):
    """ROC Compliance tracking"""
    COMPLIANCE_TYPES = [
        ('ANNUAL_RETURN', 'Annual Return (MGT-7)'),
        ('FINANCIAL_STATEMENTS', 'Financial Statements (AOC-4)'),
        ('BOARD_RESOLUTION', 'Board Resolution Filing'),
        ('CHANGE_IN_DIRECTORS', 'Change in Directors'),
        ('CHANGE_IN_SHAREHOLDING', 'Change in Shareholding'),
        ('CHARGE_CREATION', 'Charge Creation/Modification'),
        ('REGISTERED_OFFICE', 'Registered Office Change'),
        ('SHARE_ALLOTMENT', 'Share Allotment'),
        ('OTHERS', 'Other Compliance')
    ]
    
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('FILED', 'Filed'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
        ('UNDER_SCRUTINY', 'Under Scrutiny')
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    client = models.ForeignKey('clients.Client', on_delete=models.CASCADE, related_name='roc_compliances')
    compliance_type = models.CharField(max_length=30, choices=COMPLIANCE_TYPES)
    form_number = models.CharField(max_length=20)
    description = models.TextField()
    due_date = models.DateField()
    filing_date = models.DateField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')
    srn_number = models.CharField(max_length=50, blank=True)  # Service Request Number
    acknowledgment_number = models.CharField(max_length=50, blank=True)
    fees_paid = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    additional_fees = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    penalty_paid = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    documents_attached = models.JSONField(default=list)
    roc_queries = models.JSONField(default=list)
    compliance_officer = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'roc_compliances'
        ordering = ['-due_date']
    
    def __str__(self):
        return f"{self.client.name} - {self.compliance_type}"

class ComplianceCalendar(models.Model):
    """Compliance calendar for tracking all due dates"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    client = models.ForeignKey('clients.Client', on_delete=models.CASCADE, related_name='compliance_calendar')
    title = models.CharField(max_length=300)
    description = models.TextField()
    compliance_type = models.CharField(max_length=50)
    due_date = models.DateField()
    reminder_dates = models.JSONField(default=list)  # List of reminder dates
    is_recurring = models.BooleanField(default=False)
    recurrence_pattern = models.CharField(max_length=50, blank=True)  # YEARLY, MONTHLY, etc.
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    status = models.CharField(max_length=20, default='PENDING')
    priority = models.CharField(max_length=10, default='MEDIUM')
    estimated_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    actual_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    completion_date = models.DateField(null=True, blank=True)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'compliance_calendar'
        ordering = ['due_date']
    
    def __str__(self):
        return f"{self.client.name} - {self.title} ({self.due_date})"

class ComplianceDocument(models.Model):
    """Documents related to compliance filings"""
    DOCUMENT_TYPES = [
        ('FORM', 'Compliance Form'),
        ('ATTACHMENT', 'Supporting Document'),
        ('CERTIFICATE', 'Certificate'),
        ('ACKNOWLEDGMENT', 'Acknowledgment'),
        ('CORRESPONDENCE', 'Official Correspondence'),
        ('PENALTY_RECEIPT', 'Penalty Payment Receipt'),
        ('FEES_RECEIPT', 'Fees Payment Receipt')
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    compliance_requirement = models.ForeignKey(ComplianceRequirement, on_delete=models.CASCADE, related_name='documents')
    document_type = models.CharField(max_length=20, choices=DOCUMENT_TYPES)
    document_name = models.CharField(max_length=300)
    file_path = models.FileField(upload_to='compliance/documents/')
    file_size = models.BigIntegerField()
    mime_type = models.CharField(max_length=100)
    uploaded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    upload_date = models.DateTimeField(auto_now_add=True)
    is_signed = models.BooleanField(default=False)
    digital_signature_info = models.JSONField(default=dict)
    version = models.PositiveIntegerField(default=1)
    is_current_version = models.BooleanField(default=True)
    notes = models.TextField(blank=True)
    
    class Meta:
        db_table = 'compliance_documents'
        ordering = ['-upload_date']
    
    def __str__(self):
        return f"{self.document_name} - {self.compliance_requirement.client.name}"

class ComplianceAuditTrail(models.Model):
    """Audit trail for compliance activities"""
    ACTION_TYPES = [
        ('CREATED', 'Created'),
        ('UPDATED', 'Updated'),
        ('FILED', 'Filed'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
        ('DELETED', 'Deleted'),
        ('DOCUMENT_UPLOADED', 'Document Uploaded'),
        ('REMINDER_SENT', 'Reminder Sent'),
        ('STATUS_CHANGED', 'Status Changed')
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    compliance_requirement = models.ForeignKey(ComplianceRequirement, on_delete=models.CASCADE, related_name='audit_trail')
    action_type = models.CharField(max_length=20, choices=ACTION_TYPES)
    action_description = models.TextField()
    performed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    old_values = models.JSONField(default=dict)
    new_values = models.JSONField(default=dict)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    
    class Meta:
        db_table = 'compliance_audit_trail'
        ordering = ['-timestamp']
    
    def __str__(self):
        return f"{self.action_type} - {self.compliance_requirement.requirement_title}"

class ComplianceNotification(models.Model):
    """Notifications for compliance deadlines and updates"""
    NOTIFICATION_TYPES = [
        ('DEADLINE_REMINDER', 'Deadline Reminder'),
        ('OVERDUE_ALERT', 'Overdue Alert'),
        ('STATUS_UPDATE', 'Status Update'),
        ('DOCUMENT_REQUIRED', 'Document Required'),
        ('APPROVAL_REQUIRED', 'Approval Required'),
        ('FILING_COMPLETED', 'Filing Completed'),
        ('PENALTY_ALERT', 'Penalty Alert')
    ]
    
    PRIORITY_LEVELS = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('URGENT', 'Urgent')
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    compliance_requirement = models.ForeignKey(ComplianceRequirement, on_delete=models.CASCADE, related_name='notifications')
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    title = models.CharField(max_length=300)
    message = models.TextField()
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS, default='MEDIUM')
    recipients = models.ManyToManyField(User, related_name='compliance_notifications')
    is_read = models.BooleanField(default=False)
    is_email_sent = models.BooleanField(default=False)
    is_sms_sent = models.BooleanField(default=False)
    scheduled_time = models.DateTimeField(null=True, blank=True)
    sent_time = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'compliance_notifications'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.notification_type} - {self.title}"
