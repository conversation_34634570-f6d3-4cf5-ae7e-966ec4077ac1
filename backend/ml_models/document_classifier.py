"""
Document Classification Model
Classifies uploaded documents into different types using ML
"""

import os
import pickle
import numpy as np
import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB
from sklearn.pipeline import Pipeline
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import classification_report, confusion_matrix
import joblib
import re
from typing import List, Tuple, Dict

class DocumentClassifier:
    """ML model for document classification"""
    
    def __init__(self, model_path: str = None):
        self.model_path = model_path or 'ml_models/saved_models/document_classifier.pkl'
        self.pipeline = None
        self.classes = [
            'BANK_STATEMENT',
            'GST_REPORT', 
            'FORM_26AS',
            'INVOICE',
            'BALANCE_SHEET',
            'PROFIT_LOSS',
            'CASH_FLOW',
            'TRIAL_BALANCE',
            'LEDGER',
            'OTHERS'
        ]
        
    def preprocess_text(self, text: str) -> str:
        """Preprocess text for classification"""
        if not text:
            return ""
            
        # Convert to lowercase
        text = text.lower()
        
        # Remove special characters but keep important financial terms
        text = re.sub(r'[^\w\s\-\.]', ' ', text)
        
        # Remove extra whitespace
        text = ' '.join(text.split())
        
        return text
    
    def extract_features(self, text: str) -> Dict[str, bool]:
        """Extract features from document text"""
        text_lower = text.lower()
        
        features = {
            # Bank statement indicators
            'has_account_number': bool(re.search(r'account\s*number|a/c\s*no', text_lower)),
            'has_opening_balance': bool(re.search(r'opening\s*balance|o/b', text_lower)),
            'has_closing_balance': bool(re.search(r'closing\s*balance|c/b', text_lower)),
            'has_transaction_date': bool(re.search(r'transaction\s*date|txn\s*date', text_lower)),
            'has_debit_credit': bool(re.search(r'debit|credit|dr|cr', text_lower)),
            
            # GST report indicators
            'has_gstin': bool(re.search(r'gstin|gst\s*number', text_lower)),
            'has_gstr': bool(re.search(r'gstr[-\s]*[0-9]', text_lower)),
            'has_tax_period': bool(re.search(r'tax\s*period|return\s*period', text_lower)),
            'has_taxable_value': bool(re.search(r'taxable\s*value|taxable\s*amount', text_lower)),
            'has_igst_cgst_sgst': bool(re.search(r'igst|cgst|sgst', text_lower)),
            
            # Form 26AS indicators
            'has_26as': bool(re.search(r'26as|form\s*26as', text_lower)),
            'has_tds': bool(re.search(r'tds|tax\s*deducted\s*at\s*source', text_lower)),
            'has_pan': bool(re.search(r'pan\s*number|permanent\s*account', text_lower)),
            'has_assessment_year': bool(re.search(r'assessment\s*year|a\.?y\.?', text_lower)),
            
            # Invoice indicators
            'has_invoice_number': bool(re.search(r'invoice\s*number|invoice\s*no|bill\s*no', text_lower)),
            'has_invoice_date': bool(re.search(r'invoice\s*date|bill\s*date', text_lower)),
            'has_buyer_seller': bool(re.search(r'buyer|seller|vendor|customer', text_lower)),
            'has_quantity_rate': bool(re.search(r'quantity|rate|qty|amount', text_lower)),
            
            # Balance sheet indicators
            'has_balance_sheet': bool(re.search(r'balance\s*sheet', text_lower)),
            'has_assets_liabilities': bool(re.search(r'assets|liabilities|equity', text_lower)),
            'has_current_non_current': bool(re.search(r'current|non[-\s]*current', text_lower)),
            'has_share_capital': bool(re.search(r'share\s*capital|paid\s*up', text_lower)),
            
            # P&L indicators
            'has_profit_loss': bool(re.search(r'profit\s*and\s*loss|p\s*&\s*l|income\s*statement', text_lower)),
            'has_revenue_income': bool(re.search(r'revenue|income|sales|turnover', text_lower)),
            'has_expenses': bool(re.search(r'expenses|expenditure|cost', text_lower)),
            'has_net_profit': bool(re.search(r'net\s*profit|net\s*income', text_lower)),
            
            # General financial indicators
            'has_financial_year': bool(re.search(r'financial\s*year|f\.?y\.?', text_lower)),
            'has_rupees': bool(re.search(r'rupees|rs\.?|₹', text_lower)),
            'has_amounts': bool(re.search(r'\d+[,\d]*\.?\d*', text)),
        }
        
        return features
    
    def create_training_data(self) -> Tuple[List[str], List[str]]:
        """Create synthetic training data for document classification"""
        training_data = []
        labels = []
        
        # Bank statement samples
        bank_samples = [
            "Account Number: ********** Opening Balance: Rs. 50000 Closing Balance: Rs. 75000 Transaction Date Debit Credit",
            "HDFC Bank Statement Account No ********** Opening Bal 25000 Closing Bal 30000 Txn Date Dr Cr",
            "State Bank of India A/c No ********** O/B 100000 C/B 95000 Transaction Details Debit Credit Amount"
        ]
        
        # GST report samples  
        gst_samples = [
            "GSTIN: 07**********1ZM GSTR-1 Tax Period: March 2024 Taxable Value IGST CGST SGST",
            "GST Number 27**********1ZN GSTR-3B Return Period 03/2024 Taxable Amount Tax Liability",
            "GSTR-9 Annual Return GSTIN 09AABCG9012E1ZO Tax Period 2023-24 Total Taxable Value"
        ]
        
        # Form 26AS samples
        form26as_samples = [
            "Form 26AS PAN Number ********** Assessment Year 2024-25 TDS Tax Deducted at Source",
            "26AS Statement PAN ********** A.Y. 2023-24 TDS Certificate Advance Tax Self Assessment Tax",
            "Tax Credit Statement Form 26AS Permanent Account Number Assessment Year TDS Details"
        ]
        
        # Invoice samples
        invoice_samples = [
            "Invoice Number INV-2024-001 Invoice Date 15/03/2024 Buyer Seller Quantity Rate Amount GST",
            "Bill No B-12345 Bill Date 20/02/2024 Vendor Customer Qty Rate Total Amount Tax",
            "Tax Invoice No TI-2024-100 Date 10/01/2024 Buyer Details Seller Details Item Qty Rate"
        ]
        
        # Balance sheet samples
        balance_sheet_samples = [
            "Balance Sheet as at 31st March 2024 Assets Liabilities Equity Share Capital Current Non-Current",
            "Statement of Financial Position Assets Current Assets Non-Current Assets Liabilities Equity",
            "Balance Sheet Assets Fixed Assets Current Assets Liabilities Current Liabilities Share Capital"
        ]
        
        # Add samples to training data
        for sample in bank_samples:
            training_data.append(sample)
            labels.append('BANK_STATEMENT')
            
        for sample in gst_samples:
            training_data.append(sample)
            labels.append('GST_REPORT')
            
        for sample in form26as_samples:
            training_data.append(sample)
            labels.append('FORM_26AS')
            
        for sample in invoice_samples:
            training_data.append(sample)
            labels.append('INVOICE')
            
        for sample in balance_sheet_samples:
            training_data.append(sample)
            labels.append('BALANCE_SHEET')
        
        return training_data, labels
    
    def train_model(self, texts: List[str] = None, labels: List[str] = None):
        """Train the document classification model"""
        if texts is None or labels is None:
            texts, labels = self.create_training_data()
        
        # Preprocess texts
        processed_texts = [self.preprocess_text(text) for text in texts]
        
        # Create pipeline
        self.pipeline = Pipeline([
            ('tfidf', TfidfVectorizer(
                max_features=5000,
                ngram_range=(1, 3),
                stop_words='english',
                lowercase=True
            )),
            ('classifier', MultinomialNB(alpha=0.1))
        ])
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            processed_texts, labels, test_size=0.2, random_state=42, stratify=labels
        )
        
        # Train model
        self.pipeline.fit(X_train, y_train)
        
        # Evaluate model
        train_score = self.pipeline.score(X_train, y_train)
        test_score = self.pipeline.score(X_test, y_test)
        
        print(f"Training Accuracy: {train_score:.3f}")
        print(f"Testing Accuracy: {test_score:.3f}")
        
        # Cross validation
        cv_scores = cross_val_score(self.pipeline, processed_texts, labels, cv=5)
        print(f"Cross-validation Score: {cv_scores.mean():.3f} (+/- {cv_scores.std() * 2:.3f})")
        
        # Classification report
        y_pred = self.pipeline.predict(X_test)
        print("\nClassification Report:")
        print(classification_report(y_test, y_pred))
        
        return self.pipeline
    
    def predict(self, text: str) -> Tuple[str, float]:
        """Predict document type and confidence"""
        if not self.pipeline:
            self.load_model()
        
        if not self.pipeline:
            return 'OTHERS', 0.0
        
        processed_text = self.preprocess_text(text)
        
        # Get prediction and probability
        prediction = self.pipeline.predict([processed_text])[0]
        probabilities = self.pipeline.predict_proba([processed_text])[0]
        confidence = max(probabilities)
        
        return prediction, confidence
    
    def predict_batch(self, texts: List[str]) -> List[Tuple[str, float]]:
        """Predict document types for multiple texts"""
        results = []
        for text in texts:
            prediction, confidence = self.predict(text)
            results.append((prediction, confidence))
        return results
    
    def save_model(self):
        """Save trained model to disk"""
        if self.pipeline:
            os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
            joblib.dump(self.pipeline, self.model_path)
            print(f"Model saved to {self.model_path}")
    
    def load_model(self):
        """Load trained model from disk"""
        if os.path.exists(self.model_path):
            self.pipeline = joblib.load(self.model_path)
            print(f"Model loaded from {self.model_path}")
            return True
        return False
    
    def get_feature_importance(self, top_n: int = 20) -> Dict[str, float]:
        """Get most important features for classification"""
        if not self.pipeline:
            return {}
        
        # Get feature names and importance
        feature_names = self.pipeline.named_steps['tfidf'].get_feature_names_out()
        feature_importance = self.pipeline.named_steps['classifier'].feature_log_prob_
        
        # Calculate average importance across all classes
        avg_importance = np.mean(feature_importance, axis=0)
        
        # Get top features
        top_indices = np.argsort(avg_importance)[-top_n:]
        top_features = {
            feature_names[i]: avg_importance[i] 
            for i in top_indices
        }
        
        return dict(sorted(top_features.items(), key=lambda x: x[1], reverse=True))

# Example usage and training
if __name__ == "__main__":
    classifier = DocumentClassifier()
    
    # Train the model
    print("Training document classifier...")
    classifier.train_model()
    
    # Save the model
    classifier.save_model()
    
    # Test predictions
    test_texts = [
        "Account Number ********** Opening Balance Rs 50000 Transaction Date Debit Credit",
        "GSTIN 07**********1ZM GSTR-1 March 2024 Taxable Value IGST CGST",
        "Form 26AS PAN ********** Assessment Year 2024-25 TDS"
    ]
    
    print("\nTest Predictions:")
    for text in test_texts:
        prediction, confidence = classifier.predict(text)
        print(f"Text: {text[:50]}...")
        print(f"Prediction: {prediction} (Confidence: {confidence:.3f})")
        print()
    
    # Show feature importance
    print("Top Features:")
    features = classifier.get_feature_importance(10)
    for feature, importance in features.items():
        print(f"{feature}: {importance:.3f}")
