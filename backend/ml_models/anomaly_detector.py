"""
Anomaly Detection Model
Detects unusual patterns in financial documents and transactions
"""

import os
import numpy as np
import pandas as pd
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.decomposition import PCA
from sklearn.cluster import DBSCAN
from sklearn.metrics import classification_report
import joblib
from typing import Dict, List, Tuple, Any
import re
from datetime import datetime, timedelta

class AnomalyDetector:
    """ML model for detecting anomalies in financial data"""
    
    def __init__(self, model_path: str = None):
        self.model_path = model_path or 'ml_models/saved_models/anomaly_detector.pkl'
        self.isolation_forest = None
        self.scaler = RobustScaler()
        self.pca = PCA(n_components=0.95)  # Keep 95% of variance
        self.feature_columns = []
        
    def extract_financial_features(self, data: Dict[str, Any]) -> Dict[str, float]:
        """Extract features from financial data for anomaly detection"""
        features = {}
        
        # Amount-based features
        if 'amounts' in data:
            amounts = [float(amt.get('value', 0)) for amt in data['amounts'] if amt.get('value')]
            if amounts:
                features['amount_mean'] = np.mean(amounts)
                features['amount_std'] = np.std(amounts)
                features['amount_max'] = np.max(amounts)
                features['amount_min'] = np.min(amounts)
                features['amount_range'] = features['amount_max'] - features['amount_min']
                features['amount_count'] = len(amounts)
                features['amount_sum'] = np.sum(amounts)
                features['amount_median'] = np.median(amounts)
                features['amount_q75'] = np.percentile(amounts, 75)
                features['amount_q25'] = np.percentile(amounts, 25)
                features['amount_iqr'] = features['amount_q75'] - features['amount_q25']
                
                # Detect round numbers (potential manipulation)
                round_amounts = [amt for amt in amounts if amt % 1000 == 0 and amt > 1000]
                features['round_amount_ratio'] = len(round_amounts) / len(amounts)
                
                # Detect duplicate amounts
                unique_amounts = len(set(amounts))
                features['duplicate_amount_ratio'] = 1 - (unique_amounts / len(amounts))
            else:
                # Default values when no amounts found
                for key in ['amount_mean', 'amount_std', 'amount_max', 'amount_min', 
                           'amount_range', 'amount_count', 'amount_sum', 'amount_median',
                           'amount_q75', 'amount_q25', 'amount_iqr', 'round_amount_ratio',
                           'duplicate_amount_ratio']:
                    features[key] = 0.0
        
        # Date-based features
        if 'dates' in data:
            dates = []
            for date_item in data['dates']:
                try:
                    # Try to parse different date formats
                    date_str = date_item.get('text', '')
                    if date_str:
                        # Common Indian date formats
                        for fmt in ['%d/%m/%Y', '%d-%m-%Y', '%Y-%m-%d', '%d.%m.%Y']:
                            try:
                                date_obj = datetime.strptime(date_str, fmt)
                                dates.append(date_obj)
                                break
                            except ValueError:
                                continue
                except:
                    continue
            
            if dates:
                # Date range analysis
                date_range = (max(dates) - min(dates)).days
                features['date_range_days'] = date_range
                features['date_count'] = len(dates)
                
                # Weekend transactions (potentially suspicious)
                weekend_dates = [d for d in dates if d.weekday() >= 5]
                features['weekend_transaction_ratio'] = len(weekend_dates) / len(dates)
                
                # Future dates (data entry errors)
                future_dates = [d for d in dates if d > datetime.now()]
                features['future_date_ratio'] = len(future_dates) / len(dates)
                
                # Old dates (potentially backdated)
                old_dates = [d for d in dates if d < datetime.now() - timedelta(days=365*2)]
                features['old_date_ratio'] = len(old_dates) / len(dates)
            else:
                for key in ['date_range_days', 'date_count', 'weekend_transaction_ratio',
                           'future_date_ratio', 'old_date_ratio']:
                    features[key] = 0.0
        
        # Text-based features
        if 'extracted_text' in data:
            text = data['extracted_text'].lower()
            
            # Suspicious keywords
            suspicious_keywords = [
                'cash', 'adjustment', 'reversal', 'correction', 'error',
                'manual', 'override', 'urgent', 'rush', 'backdated'
            ]
            
            features['suspicious_keyword_count'] = sum(
                text.count(keyword) for keyword in suspicious_keywords
            )
            
            # Text quality indicators
            features['text_length'] = len(text)
            features['word_count'] = len(text.split())
            features['avg_word_length'] = np.mean([len(word) for word in text.split()]) if text.split() else 0
            
            # Special character ratio (OCR quality indicator)
            special_chars = sum(1 for char in text if not char.isalnum() and not char.isspace())
            features['special_char_ratio'] = special_chars / len(text) if text else 0
            
            # Number density
            numbers = re.findall(r'\d+', text)
            features['number_density'] = len(numbers) / len(text.split()) if text.split() else 0
        
        # Entity-based features
        if 'entities' in data:
            entities = data['entities']
            features['entity_count'] = len(entities)
            
            # Check for duplicate entities (potential data quality issues)
            unique_entities = len(set(entities))
            features['duplicate_entity_ratio'] = 1 - (unique_entities / len(entities)) if entities else 0
        
        # Confidence-based features
        if 'confidence_scores' in data:
            confidences = [float(score) for score in data['confidence_scores'] if score]
            if confidences:
                features['confidence_mean'] = np.mean(confidences)
                features['confidence_min'] = np.min(confidences)
                features['low_confidence_ratio'] = sum(1 for c in confidences if c < 0.7) / len(confidences)
            else:
                features['confidence_mean'] = 0.0
                features['confidence_min'] = 0.0
                features['low_confidence_ratio'] = 1.0
        
        return features
    
    def create_training_data(self) -> pd.DataFrame:
        """Create synthetic training data for anomaly detection"""
        np.random.seed(42)
        n_normal = 800
        n_anomaly = 200
        
        # Normal transactions
        normal_data = []
        for _ in range(n_normal):
            # Generate normal financial data
            amounts = np.random.lognormal(8, 1.5, np.random.randint(5, 50))
            
            data = {
                'amounts': [{'value': amt} for amt in amounts],
                'dates': [{'text': f"{np.random.randint(1,29):02d}/{np.random.randint(1,13):02d}/2024"}
                         for _ in range(len(amounts))],
                'extracted_text': f"Normal transaction data with {len(amounts)} entries",
                'entities': [f"Entity_{i}" for i in range(np.random.randint(1, 5))],
                'confidence_scores': np.random.uniform(0.8, 0.95, len(amounts))
            }
            
            features = self.extract_financial_features(data)
            features['is_anomaly'] = 0
            normal_data.append(features)
        
        # Anomalous transactions
        anomaly_data = []
        for _ in range(n_anomaly):
            # Generate anomalous patterns
            anomaly_type = np.random.choice(['round_amounts', 'duplicate_amounts', 'suspicious_dates', 'low_confidence'])
            
            if anomaly_type == 'round_amounts':
                # Many round amounts (potential manipulation)
                amounts = [float(x * 1000) for x in np.random.randint(1, 100, np.random.randint(10, 30))]
            elif anomaly_type == 'duplicate_amounts':
                # Many duplicate amounts
                base_amount = np.random.uniform(1000, 100000)
                amounts = [base_amount] * np.random.randint(10, 20)
            elif anomaly_type == 'suspicious_dates':
                # Weekend or future dates
                amounts = np.random.lognormal(8, 1.5, np.random.randint(5, 20))
                # Generate weekend dates
                dates = [{'text': f"{np.random.randint(1,29):02d}/{np.random.randint(1,13):02d}/2024"}
                        for _ in range(len(amounts))]
            else:  # low_confidence
                amounts = np.random.lognormal(8, 1.5, np.random.randint(5, 20))
                dates = [{'text': f"{np.random.randint(1,29):02d}/{np.random.randint(1,13):02d}/2024"}
                        for _ in range(len(amounts))]
            
            data = {
                'amounts': [{'value': amt} for amt in amounts],
                'dates': [{'text': f"{np.random.randint(1,29):02d}/{np.random.randint(1,13):02d}/2024"}
                         for _ in range(len(amounts))],
                'extracted_text': f"Suspicious transaction data with {len(amounts)} entries cash adjustment",
                'entities': [f"Entity_{i}" for i in range(np.random.randint(1, 3))],
                'confidence_scores': np.random.uniform(0.3, 0.6, len(amounts)) if anomaly_type == 'low_confidence' 
                                   else np.random.uniform(0.8, 0.95, len(amounts))
            }
            
            features = self.extract_financial_features(data)
            features['is_anomaly'] = 1
            anomaly_data.append(features)
        
        # Combine data
        all_data = normal_data + anomaly_data
        df = pd.DataFrame(all_data)
        
        # Fill missing values
        df = df.fillna(0)
        
        return df
    
    def train_model(self, df: pd.DataFrame = None):
        """Train the anomaly detection model"""
        if df is None:
            df = self.create_training_data()
        
        # Separate features and labels
        feature_cols = [col for col in df.columns if col != 'is_anomaly']
        self.feature_columns = feature_cols
        
        X = df[feature_cols]
        y = df['is_anomaly']
        
        # Handle infinite values
        X = X.replace([np.inf, -np.inf], np.nan).fillna(0)
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        
        # Apply PCA for dimensionality reduction
        X_pca = self.pca.fit_transform(X_scaled)
        
        # Train Isolation Forest
        self.isolation_forest = IsolationForest(
            contamination=0.2,  # Expected proportion of anomalies
            random_state=42,
            n_estimators=100
        )
        
        self.isolation_forest.fit(X_pca)
        
        # Evaluate model
        predictions = self.isolation_forest.predict(X_pca)
        predictions_binary = (predictions == -1).astype(int)  # -1 for anomalies, 1 for normal
        
        print("Anomaly Detection Model Performance:")
        print(classification_report(y, predictions_binary, target_names=['Normal', 'Anomaly']))
        
        # Calculate anomaly scores
        anomaly_scores = self.isolation_forest.decision_function(X_pca)
        
        print(f"\nAnomaly Score Statistics:")
        print(f"Mean: {np.mean(anomaly_scores):.3f}")
        print(f"Std: {np.std(anomaly_scores):.3f}")
        print(f"Min: {np.min(anomaly_scores):.3f}")
        print(f"Max: {np.max(anomaly_scores):.3f}")
        
        return self.isolation_forest
    
    def detect_anomalies(self, data: Dict[str, Any]) -> Tuple[bool, float, List[str]]:
        """Detect anomalies in financial data"""
        if not self.isolation_forest:
            self.load_model()
        
        if not self.isolation_forest:
            return False, 0.0, []
        
        # Extract features
        features = self.extract_financial_features(data)
        
        # Ensure all required features exist
        for col in self.feature_columns:
            if col not in features:
                features[col] = 0.0
        
        # Convert to DataFrame
        df = pd.DataFrame([features])
        X = df[self.feature_columns]
        
        # Handle infinite values
        X = X.replace([np.inf, -np.inf], np.nan).fillna(0)
        
        # Scale and transform
        X_scaled = self.scaler.transform(X)
        X_pca = self.pca.transform(X_scaled)
        
        # Predict
        prediction = self.isolation_forest.predict(X_pca)[0]
        anomaly_score = self.isolation_forest.decision_function(X_pca)[0]
        
        is_anomaly = prediction == -1
        confidence = abs(anomaly_score)  # Higher absolute value = more confident
        
        # Identify specific anomaly reasons
        anomaly_reasons = []
        
        if features.get('round_amount_ratio', 0) > 0.5:
            anomaly_reasons.append("High proportion of round amounts detected")
        
        if features.get('duplicate_amount_ratio', 0) > 0.3:
            anomaly_reasons.append("Many duplicate amounts found")
        
        if features.get('weekend_transaction_ratio', 0) > 0.2:
            anomaly_reasons.append("Unusual number of weekend transactions")
        
        if features.get('future_date_ratio', 0) > 0:
            anomaly_reasons.append("Future dates detected")
        
        if features.get('low_confidence_ratio', 0) > 0.3:
            anomaly_reasons.append("Low OCR confidence scores")
        
        if features.get('suspicious_keyword_count', 0) > 2:
            anomaly_reasons.append("Suspicious keywords found in text")
        
        return is_anomaly, confidence, anomaly_reasons
    
    def batch_detect_anomalies(self, data_list: List[Dict[str, Any]]) -> List[Tuple[bool, float, List[str]]]:
        """Detect anomalies in batch"""
        results = []
        for data in data_list:
            result = self.detect_anomalies(data)
            results.append(result)
        return results
    
    def save_model(self):
        """Save trained model and preprocessors"""
        if self.isolation_forest:
            model_data = {
                'isolation_forest': self.isolation_forest,
                'scaler': self.scaler,
                'pca': self.pca,
                'feature_columns': self.feature_columns
            }
            
            os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
            joblib.dump(model_data, self.model_path)
            print(f"Anomaly detection model saved to {self.model_path}")
    
    def load_model(self):
        """Load trained model and preprocessors"""
        if os.path.exists(self.model_path):
            model_data = joblib.load(self.model_path)
            self.isolation_forest = model_data['isolation_forest']
            self.scaler = model_data['scaler']
            self.pca = model_data['pca']
            self.feature_columns = model_data['feature_columns']
            print(f"Anomaly detection model loaded from {self.model_path}")
            return True
        return False

# Example usage and training
if __name__ == "__main__":
    detector = AnomalyDetector()
    
    # Train the model
    print("Training anomaly detection model...")
    detector.train_model()
    
    # Save the model
    detector.save_model()
    
    # Test anomaly detection
    test_data = {
        'amounts': [{'value': 10000}, {'value': 10000}, {'value': 10000}, {'value': 10000}],
        'dates': [{'text': '15/03/2024'}, {'text': '16/03/2024'}, {'text': '17/03/2024'}, {'text': '18/03/2024'}],
        'extracted_text': 'cash adjustment manual override urgent transaction',
        'entities': ['Entity_1', 'Entity_1', 'Entity_1'],
        'confidence_scores': [0.5, 0.4, 0.6, 0.5]
    }
    
    print("\nTest Anomaly Detection:")
    is_anomaly, confidence, reasons = detector.detect_anomalies(test_data)
    print(f"Is Anomaly: {is_anomaly}")
    print(f"Confidence: {confidence:.3f}")
    print(f"Reasons: {reasons}")
    
    # Test normal data
    normal_data = {
        'amounts': [{'value': 1250.50}, {'value': 2340.75}, {'value': 890.25}],
        'dates': [{'text': '15/03/2024'}, {'text': '16/03/2024'}, {'text': '17/03/2024'}],
        'extracted_text': 'normal business transaction invoice payment',
        'entities': ['Vendor_A', 'Customer_B', 'Supplier_C'],
        'confidence_scores': [0.92, 0.88, 0.95]
    }
    
    print("\nNormal Data Test:")
    is_anomaly, confidence, reasons = detector.detect_anomalies(normal_data)
    print(f"Is Anomaly: {is_anomaly}")
    print(f"Confidence: {confidence:.3f}")
    print(f"Reasons: {reasons}")
