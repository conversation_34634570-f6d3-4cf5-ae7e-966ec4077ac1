#!/usr/bin/env python
"""
ML Model Training Pipeline
Trains and manages all ML models for the audit system
"""

import os
import sys
import logging
from datetime import datetime
from typing import Dict, Any, List

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ml_models.document_classifier import DocumentClassifier
from ml_models.risk_predictor import RiskPredictor
from ml_models.anomaly_detector import AnomalyDetector

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ml_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MLModelTrainer:
    """Manages training of all ML models"""
    
    def __init__(self):
        self.models = {
            'document_classifier': DocumentClassifier(),
            'risk_predictor': RiskPredictor(),
            'anomaly_detector': AnomalyDetector()
        }
        self.training_results = {}
    
    def train_all_models(self):
        """Train all ML models"""
        logger.info("Starting ML model training pipeline...")
        start_time = datetime.now()
        
        for model_name, model in self.models.items():
            try:
                logger.info(f"Training {model_name}...")
                model_start_time = datetime.now()
                
                # Train the model
                trained_model = model.train_model()
                
                # Save the model
                model.save_model()
                
                training_time = (datetime.now() - model_start_time).total_seconds()
                
                self.training_results[model_name] = {
                    'status': 'success',
                    'training_time': training_time,
                    'model_path': model.model_path
                }
                
                logger.info(f"✅ {model_name} trained successfully in {training_time:.2f} seconds")
                
            except Exception as e:
                logger.error(f"❌ Failed to train {model_name}: {str(e)}")
                self.training_results[model_name] = {
                    'status': 'failed',
                    'error': str(e)
                }
        
        total_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"Training pipeline completed in {total_time:.2f} seconds")
        
        return self.training_results
    
    def validate_models(self):
        """Validate all trained models"""
        logger.info("Validating trained models...")
        validation_results = {}
        
        # Test document classifier
        try:
            classifier = DocumentClassifier()
            if classifier.load_model():
                test_text = "Account Number ********** Opening Balance Rs 50000 Transaction Date Debit Credit"
                prediction, confidence = classifier.predict(test_text)
                validation_results['document_classifier'] = {
                    'status': 'valid',
                    'test_prediction': prediction,
                    'test_confidence': confidence
                }
                logger.info(f"✅ Document classifier validation passed")
            else:
                validation_results['document_classifier'] = {'status': 'model_not_found'}
        except Exception as e:
            validation_results['document_classifier'] = {'status': 'error', 'error': str(e)}
            logger.error(f"❌ Document classifier validation failed: {str(e)}")
        
        # Test risk predictor
        try:
            predictor = RiskPredictor()
            if predictor.load_model():
                test_client = {
                    'annual_turnover': ********,
                    'number_of_employees': 150,
                    'entity_type': 'PRIVATE_LIMITED',
                    'industry': 'IT_SOFTWARE',
                    'size_category': 'MEDIUM',
                    'previous_audit_issues': 1,
                    'management_changes': 0,
                    'system_changes': 1,
                    'regulatory_changes': 1,
                    'financial_distress_indicators': 0,
                    'related_party_transactions': 1,
                    'complex_transactions': 1,
                    'geographic_spread': 3,
                    'audit_committee_effectiveness': 4
                }
                risk_level, confidence = predictor.predict_risk(test_client)
                validation_results['risk_predictor'] = {
                    'status': 'valid',
                    'test_prediction': risk_level,
                    'test_confidence': confidence
                }
                logger.info(f"✅ Risk predictor validation passed")
            else:
                validation_results['risk_predictor'] = {'status': 'model_not_found'}
        except Exception as e:
            validation_results['risk_predictor'] = {'status': 'error', 'error': str(e)}
            logger.error(f"❌ Risk predictor validation failed: {str(e)}")
        
        # Test anomaly detector
        try:
            detector = AnomalyDetector()
            if detector.load_model():
                test_data = {
                    'amounts': [{'value': 1250.50}, {'value': 2340.75}],
                    'dates': [{'text': '15/03/2024'}, {'text': '16/03/2024'}],
                    'extracted_text': 'normal business transaction',
                    'entities': ['Vendor_A', 'Customer_B'],
                    'confidence_scores': [0.92, 0.88]
                }
                is_anomaly, confidence, reasons = detector.detect_anomalies(test_data)
                validation_results['anomaly_detector'] = {
                    'status': 'valid',
                    'test_prediction': is_anomaly,
                    'test_confidence': confidence
                }
                logger.info(f"✅ Anomaly detector validation passed")
            else:
                validation_results['anomaly_detector'] = {'status': 'model_not_found'}
        except Exception as e:
            validation_results['anomaly_detector'] = {'status': 'error', 'error': str(e)}
            logger.error(f"❌ Anomaly detector validation failed: {str(e)}")
        
        return validation_results
    
    def create_model_info(self):
        """Create model information file"""
        model_info = {
            'training_date': datetime.now().isoformat(),
            'models': {},
            'training_results': self.training_results
        }
        
        for model_name, model in self.models.items():
            model_info['models'][model_name] = {
                'model_path': model.model_path,
                'description': self._get_model_description(model_name),
                'input_format': self._get_model_input_format(model_name),
                'output_format': self._get_model_output_format(model_name)
            }
        
        # Save model info
        import json
        info_path = 'ml_models/model_info.json'
        os.makedirs(os.path.dirname(info_path), exist_ok=True)
        
        with open(info_path, 'w') as f:
            json.dump(model_info, f, indent=2)
        
        logger.info(f"Model information saved to {info_path}")
        return model_info
    
    def _get_model_description(self, model_name: str) -> str:
        """Get model description"""
        descriptions = {
            'document_classifier': 'Classifies uploaded documents into types (bank statements, GST reports, etc.)',
            'risk_predictor': 'Predicts audit risk levels based on client characteristics',
            'anomaly_detector': 'Detects unusual patterns in financial documents and transactions'
        }
        return descriptions.get(model_name, 'No description available')
    
    def _get_model_input_format(self, model_name: str) -> Dict[str, Any]:
        """Get model input format"""
        formats = {
            'document_classifier': {
                'type': 'string',
                'description': 'Extracted text from document'
            },
            'risk_predictor': {
                'type': 'object',
                'properties': {
                    'annual_turnover': 'float',
                    'number_of_employees': 'int',
                    'entity_type': 'string',
                    'industry': 'string',
                    'size_category': 'string',
                    'previous_audit_issues': 'int',
                    'management_changes': 'int',
                    'system_changes': 'int',
                    'regulatory_changes': 'int',
                    'financial_distress_indicators': 'int',
                    'related_party_transactions': 'int',
                    'complex_transactions': 'int',
                    'geographic_spread': 'int',
                    'audit_committee_effectiveness': 'int'
                }
            },
            'anomaly_detector': {
                'type': 'object',
                'properties': {
                    'amounts': 'array of objects with value field',
                    'dates': 'array of objects with text field',
                    'extracted_text': 'string',
                    'entities': 'array of strings',
                    'confidence_scores': 'array of floats'
                }
            }
        }
        return formats.get(model_name, {})
    
    def _get_model_output_format(self, model_name: str) -> Dict[str, Any]:
        """Get model output format"""
        formats = {
            'document_classifier': {
                'prediction': 'string (document type)',
                'confidence': 'float (0-1)'
            },
            'risk_predictor': {
                'risk_level': 'string (LOW/MEDIUM/HIGH)',
                'confidence': 'float (0-1)'
            },
            'anomaly_detector': {
                'is_anomaly': 'boolean',
                'confidence': 'float',
                'reasons': 'array of strings'
            }
        }
        return formats.get(model_name, {})
    
    def generate_training_report(self):
        """Generate comprehensive training report"""
        report = {
            'training_summary': self.training_results,
            'validation_results': self.validate_models(),
            'model_info': self.create_model_info(),
            'recommendations': self._generate_recommendations()
        }
        
        # Save report
        report_path = f'ml_models/training_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        import json
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Training report saved to {report_path}")
        return report
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on training results"""
        recommendations = []
        
        # Check training results
        failed_models = [name for name, result in self.training_results.items() 
                        if result.get('status') != 'success']
        
        if failed_models:
            recommendations.append(f"Retrain failed models: {', '.join(failed_models)}")
        
        # General recommendations
        recommendations.extend([
            "Regularly retrain models with new data to maintain accuracy",
            "Monitor model performance in production and retrain if accuracy drops",
            "Collect feedback from users to improve model predictions",
            "Consider ensemble methods for better performance",
            "Implement A/B testing for model updates"
        ])
        
        return recommendations

def main():
    """Main training function"""
    print("🤖 AuditSmartAI ML Model Training Pipeline")
    print("=" * 50)
    
    # Create directories
    os.makedirs('ml_models/saved_models', exist_ok=True)
    
    # Initialize trainer
    trainer = MLModelTrainer()
    
    # Train all models
    training_results = trainer.train_all_models()
    
    # Generate report
    report = trainer.generate_training_report()
    
    # Print summary
    print("\n📊 TRAINING SUMMARY")
    print("=" * 50)
    
    successful_models = [name for name, result in training_results.items() 
                        if result.get('status') == 'success']
    failed_models = [name for name, result in training_results.items() 
                    if result.get('status') != 'success']
    
    print(f"✅ Successfully trained: {len(successful_models)} models")
    for model in successful_models:
        time_taken = training_results[model].get('training_time', 0)
        print(f"   - {model}: {time_taken:.2f}s")
    
    if failed_models:
        print(f"❌ Failed to train: {len(failed_models)} models")
        for model in failed_models:
            error = training_results[model].get('error', 'Unknown error')
            print(f"   - {model}: {error}")
    
    print(f"\n🎯 All models ready for production use!")
    print(f"📁 Models saved in: ml_models/saved_models/")
    print(f"📋 Training report: {report}")

if __name__ == "__main__":
    main()
