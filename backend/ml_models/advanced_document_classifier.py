"""
Advanced Document Classification with Deep Learning
Uses transformer models and ensemble methods for superior accuracy
"""

import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from transformers import AutoTokenizer, AutoModel, AutoConfig
from sklearn.ensemble import VotingClassifier, RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.metrics import classification_report, confusion_matrix
import joblib
import re
from typing import List, Tuple, Dict, Any
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DocumentDataset(Dataset):
    """Dataset class for document classification"""
    
    def __init__(self, texts, labels, tokenizer, max_length=512):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]
        
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'label': torch.tensor(label, dtype=torch.long)
        }

class TransformerDocumentClassifier(nn.Module):
    """Transformer-based document classifier"""
    
    def __init__(self, model_name, num_classes, dropout_rate=0.3):
        super().__init__()
        self.config = AutoConfig.from_pretrained(model_name)
        self.transformer = AutoModel.from_pretrained(model_name)
        self.dropout = nn.Dropout(dropout_rate)
        self.classifier = nn.Linear(self.config.hidden_size, num_classes)
        
    def forward(self, input_ids, attention_mask):
        outputs = self.transformer(input_ids=input_ids, attention_mask=attention_mask)
        pooled_output = outputs.pooler_output
        output = self.dropout(pooled_output)
        return self.classifier(output)

class AdvancedDocumentClassifier:
    """Advanced document classifier with ensemble methods and deep learning"""
    
    def __init__(self, model_path: str = None):
        self.model_path = model_path or 'ml_models/saved_models/advanced_document_classifier.pkl'
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Model components
        self.transformer_model = None
        self.tokenizer = None
        self.ensemble_model = None
        self.tfidf_vectorizer = None
        self.label_encoder = None
        
        # Configuration
        self.model_name = 'distilbert-base-uncased'  # Lightweight transformer
        self.max_length = 512
        self.num_classes = 10
        
        self.classes = [
            'BANK_STATEMENT',
            'GST_REPORT', 
            'FORM_26AS',
            'INVOICE',
            'BALANCE_SHEET',
            'PROFIT_LOSS',
            'CASH_FLOW',
            'TRIAL_BALANCE',
            'LEDGER',
            'OTHERS'
        ]
        
        logger.info(f"Using device: {self.device}")
    
    def create_enhanced_training_data(self) -> Tuple[List[str], List[str]]:
        """Create enhanced training data with more sophisticated patterns"""
        training_data = []
        labels = []
        
        # Enhanced bank statement samples with more variations
        bank_samples = [
            "HDFC Bank Limited Account Statement Account Number: ************** Statement Period: 01-Apr-2024 to 30-Apr-2024 Opening Balance: Rs. 2,50,000.00 Date Description Debit Credit Balance 01-Apr NEFT Transfer to ABC Corp 50,000.00 2,00,000.00 02-Apr Salary Credit COMPANY XYZ 75,000.00 2,75,000.00 03-Apr ATM Withdrawal 5,000.00 2,70,000.00 Closing Balance: Rs. 2,70,000.00",
            "State Bank of India Current Account Statement A/c No: **********1 Customer Name: TECH SOLUTIONS PVT LTD Statement from 01/03/2024 to 31/03/2024 Opening Bal: 1,25,000.00 Dr Transaction Details: Date Particulars Chq/Ref No Debit Credit Balance 01-Mar Opening Balance 1,25,000.00 05-Mar RTGS-VENDOR PAYMENT 25,000.00 1,00,000.00 10-Mar NEFT-CLIENT PAYMENT 50,000.00 1,50,000.00 Closing Bal: 1,50,000.00 Cr",
            "ICICI Bank Savings Account Statement Account: ************ Period: January 2024 Opening: Rs 80,000 Transactions: Date Description Withdrawal Deposit Balance 15-Jan UPI Payment to Merchant 2,500 77,500 20-Jan Interest Credit 150 77,650 25-Jan Online Transfer 10,000 67,650 Closing: Rs 67,650"
        ]
        
        # Enhanced GST report samples
        gst_samples = [
            "Goods and Services Tax Return GSTR-1 GSTIN: 07**********1ZM Legal Name: TECH CORP SOLUTIONS PVT LTD Period: March 2024 B2B Supplies: Sr No Recipient GSTIN Invoice No Invoice Date Taxable Value IGST CGST SGST 1 27**********1ZN INV-001 15-Mar-24 1,00,000 18,000 0 0 2 09**********1ZO INV-002 20-Mar-24 50,000 0 4,500 4,500 Total Taxable Value: 1,50,000 Total Tax: 27,000",
            "GST Annual Return GSTR-9 Financial Year: 2023-24 GSTIN: 27**********1ZN Trade Name: MANUFACTURING INDUSTRIES Summary of Outward Supplies: Nature of Supply Taxable Value Tax Amount Intra-State 50,00,000 9,00,000 Inter-State 25,00,000 4,50,000 Total 75,00,000 13,50,000 Input Tax Credit Summary: ITC Available 12,00,000 ITC Utilized 11,50,000 ITC Carried Forward 50,000",
            "GSTR-3B Monthly Return GSTIN: 09**********1ZO Month: February 2024 Outward Supplies: Taxable Supplies 10,00,000 Zero Rated Supplies 2,00,000 Exempt Supplies 1,00,000 Total 13,00,000 Tax Liability: CGST 90,000 SGST 90,000 IGST 36,000 Total 2,16,000"
        ]
        
        # Enhanced Form 26AS samples
        form26as_samples = [
            "Tax Credit Statement Form 26AS Assessment Year: 2024-25 PAN: ********** Name: RAJESH KUMAR SHARMA TDS Details: Deductor TAN Deductor Name Amount Credited Date Tax Deducted MUMM12345D TECH SOLUTIONS 50,000 15-Mar-24 5,000 DELH67890E CONSULTING FIRM 75,000 20-Mar-24 7,500 Total TDS: 12,500 Advance Tax: First Installment 25,000 Second Installment 30,000 Total Advance Tax: 55,000",
            "Form 26AS Annual Statement PAN: ********** Assessment Year: 2023-24 Part A - Tax Deducted at Source: Sr No TAN of Deductor Name of Deductor Amount Paid/Credited Tax Deducted 1 PUNJ12345F ABC COMPANY 2,00,000 20,000 2 DELH67890G XYZ CORPORATION 1,50,000 15,000 Total TDS: 35,000 Part B - Tax Collected at Source: Nature of Payment Amount Tax Collected Sale of Goods 5,00,000 5,000 Total TCS: 5,000",
            "26AS Statement PAN: ********** A.Y. 2024-25 Tax Deducted at Source Section 192 - Salary 8,00,000 80,000 Section 194A - Interest 25,000 2,500 Section 194C - Contractor 1,00,000 10,000 Total TDS: 92,500 Self Assessment Tax: Payment Date Amount 15-Mar-24 50,000 Total SAT: 50,000"
        ]
        
        # Enhanced invoice samples
        invoice_samples = [
            "Tax Invoice Invoice No: INV/2024/001 Date: 15-March-2024 From: TECH SOLUTIONS PVT LTD GSTIN: 07**********1ZM Address: 123 Tech Park, Noida To: ABC MANUFACTURING LTD GSTIN: 27**********1ZN Address: Industrial Area, Mumbai Description: Software Development Services Quantity: 1 Rate: 1,00,000 Amount: 1,00,000 CGST @ 9%: 9,000 SGST @ 9%: 9,000 Total: 1,18,000 Amount in Words: One Lakh Eighteen Thousand Only",
            "Commercial Invoice Bill No: B-2024-0025 Bill Date: 20-Feb-2024 Supplier: GREEN ENERGY SOLUTIONS GST No: 09**********1ZO Buyer: RETAIL CHAIN ENTERPRISES GST No: 19AABCR3456F1ZP Item Details: Solar Panel 320W Qty: 100 Rate: 15,000 Amount: 15,00,000 Discount: 50,000 Taxable Value: 14,50,000 IGST @ 12%: 1,74,000 Total Invoice Value: 16,24,000",
            "Service Invoice Invoice Number: SI-2024-003 Invoice Date: 10-Jan-2024 Service Provider: FINANCIAL SERVICES CORP GSTIN: 07AABCF7890G1ZQ Service Recipient: TECH CORP SOLUTIONS GSTIN: 07**********1ZM Service Description: Financial Consulting SAC Code: 998314 Service Amount: 2,00,000 CGST @ 9%: 18,000 SGST @ 9%: 18,000 Total Amount: 2,36,000"
        ]
        
        # Enhanced balance sheet samples
        balance_sheet_samples = [
            "Balance Sheet as at 31st March 2024 TECH CORP SOLUTIONS PVT LTD ASSETS Non-Current Assets: Property Plant Equipment 50,00,000 Intangible Assets 10,00,000 Investments 15,00,000 Total Non-Current Assets 75,00,000 Current Assets: Inventories 20,00,000 Trade Receivables 25,00,000 Cash and Bank 10,00,000 Other Current Assets 5,00,000 Total Current Assets 60,00,000 TOTAL ASSETS 1,35,00,000 EQUITY AND LIABILITIES Equity: Share Capital 50,00,000 Reserves and Surplus 45,00,000 Total Equity 95,00,000 Liabilities: Non-Current Liabilities 20,00,000 Current Liabilities 20,00,000 Total Liabilities 40,00,000 TOTAL EQUITY AND LIABILITIES 1,35,00,000",
            "Statement of Financial Position As at March 31, 2024 MANUFACTURING INDUSTRIES LTD ASSETS Fixed Assets: Land and Buildings 1,00,00,000 Plant and Machinery 75,00,000 Furniture and Fixtures 5,00,000 Total Fixed Assets 1,80,00,000 Current Assets: Stock in Trade 30,00,000 Sundry Debtors 40,00,000 Cash in Hand 2,00,000 Bank Balance 18,00,000 Total Current Assets 90,00,000 TOTAL 2,70,00,000 LIABILITIES Capital: Authorized Capital 2,00,00,000 Issued and Paid up 1,50,00,000 Reserves 50,00,000 Total Capital 2,00,00,000 Current Liabilities: Sundry Creditors 35,00,000 Provisions 10,00,000 Bank Overdraft 25,00,000 Total Current Liabilities 70,00,000 TOTAL 2,70,00,000"
        ]
        
        # Add all samples with labels
        for sample in bank_samples:
            training_data.append(sample)
            labels.append('BANK_STATEMENT')
            
        for sample in gst_samples:
            training_data.append(sample)
            labels.append('GST_REPORT')
            
        for sample in form26as_samples:
            training_data.append(sample)
            labels.append('FORM_26AS')
            
        for sample in invoice_samples:
            training_data.append(sample)
            labels.append('INVOICE')
            
        for sample in balance_sheet_samples:
            training_data.append(sample)
            labels.append('BALANCE_SHEET')
        
        # Add more samples for other classes
        profit_loss_samples = [
            "Profit and Loss Account for the year ended 31st March 2024 REVENUE: Sales 1,00,00,000 Other Income 5,00,000 Total Revenue 1,05,00,000 EXPENSES: Cost of Goods Sold 60,00,000 Employee Benefits 20,00,000 Administrative Expenses 10,00,000 Finance Costs 3,00,000 Depreciation 5,00,000 Total Expenses 98,00,000 Profit Before Tax 7,00,000 Tax Expense 2,00,000 Profit After Tax 5,00,000",
            "Statement of Profit and Loss Year: 2023-24 Income from Operations 2,50,00,000 Other Income 10,00,000 Total Income 2,60,00,000 Expenses: Purchase of Stock 1,50,00,000 Changes in Inventory (5,00,000) Employee Benefit Expenses 40,00,000 Finance Costs 8,00,000 Depreciation 12,00,000 Other Expenses 25,00,000 Total Expenses 2,30,00,000 Profit Before Tax 30,00,000 Current Tax 8,00,000 Deferred Tax 1,00,000 Profit for the Year 21,00,000"
        ]
        
        for sample in profit_loss_samples:
            training_data.append(sample)
            labels.append('PROFIT_LOSS')
        
        # Generate synthetic data for remaining classes
        cash_flow_samples = [
            "Cash Flow Statement for the year ended 31st March 2024 Cash Flow from Operating Activities: Net Profit 21,00,000 Adjustments: Depreciation 12,00,000 Operating Cash Flow 33,00,000 Cash Flow from Investing Activities: Purchase of Assets (15,00,000) Sale of Investments 5,00,000 Investing Cash Flow (10,00,000) Cash Flow from Financing Activities: Issue of Shares 10,00,000 Repayment of Loans (8,00,000) Financing Cash Flow 2,00,000 Net Increase in Cash 25,00,000"
        ]
        
        for sample in cash_flow_samples:
            training_data.append(sample)
            labels.append('CASH_FLOW')
        
        return training_data, labels
    
    def train_ensemble_model(self, texts: List[str], labels: List[str]):
        """Train ensemble model with multiple algorithms"""
        logger.info("Training ensemble model...")
        
        # Prepare data
        from sklearn.preprocessing import LabelEncoder
        self.label_encoder = LabelEncoder()
        encoded_labels = self.label_encoder.fit_transform(labels)
        
        # TF-IDF features for traditional ML models
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=10000,
            ngram_range=(1, 3),
            stop_words='english',
            lowercase=True
        )
        tfidf_features = self.tfidf_vectorizer.fit_transform(texts)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            tfidf_features, encoded_labels, test_size=0.2, random_state=42, stratify=encoded_labels
        )
        
        # Create ensemble model
        rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
        lr_model = LogisticRegression(random_state=42, max_iter=1000)
        
        self.ensemble_model = VotingClassifier(
            estimators=[
                ('rf', rf_model),
                ('lr', lr_model)
            ],
            voting='soft'
        )
        
        # Train ensemble
        self.ensemble_model.fit(X_train, y_train)
        
        # Evaluate
        train_score = self.ensemble_model.score(X_train, y_train)
        test_score = self.ensemble_model.score(X_test, y_test)
        
        logger.info(f"Ensemble Model - Train Score: {train_score:.3f}, Test Score: {test_score:.3f}")
        
        return self.ensemble_model
    
    def train_transformer_model(self, texts: List[str], labels: List[str]):
        """Train transformer-based model"""
        logger.info("Training transformer model...")
        
        # Initialize tokenizer
        self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
        
        # Prepare labels
        from sklearn.preprocessing import LabelEncoder
        if not self.label_encoder:
            self.label_encoder = LabelEncoder()
            encoded_labels = self.label_encoder.fit_transform(labels)
        else:
            encoded_labels = self.label_encoder.transform(labels)
        
        # Create dataset
        dataset = DocumentDataset(texts, encoded_labels, self.tokenizer, self.max_length)
        
        # Split data
        train_size = int(0.8 * len(dataset))
        test_size = len(dataset) - train_size
        train_dataset, test_dataset = torch.utils.data.random_split(dataset, [train_size, test_size])
        
        # Create data loaders
        train_loader = DataLoader(train_dataset, batch_size=8, shuffle=True)
        test_loader = DataLoader(test_dataset, batch_size=8, shuffle=False)
        
        # Initialize model
        self.transformer_model = TransformerDocumentClassifier(
            self.model_name, 
            len(self.label_encoder.classes_)
        ).to(self.device)
        
        # Training setup
        optimizer = torch.optim.AdamW(self.transformer_model.parameters(), lr=2e-5)
        criterion = nn.CrossEntropyLoss()
        
        # Training loop
        self.transformer_model.train()
        for epoch in range(3):  # Limited epochs for demo
            total_loss = 0
            for batch in train_loader:
                optimizer.zero_grad()
                
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                labels_batch = batch['label'].to(self.device)
                
                outputs = self.transformer_model(input_ids, attention_mask)
                loss = criterion(outputs, labels_batch)
                
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
            
            logger.info(f"Epoch {epoch+1}, Average Loss: {total_loss/len(train_loader):.4f}")
        
        # Evaluation
        self.transformer_model.eval()
        correct = 0
        total = 0
        
        with torch.no_grad():
            for batch in test_loader:
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                labels_batch = batch['label'].to(self.device)
                
                outputs = self.transformer_model(input_ids, attention_mask)
                _, predicted = torch.max(outputs.data, 1)
                
                total += labels_batch.size(0)
                correct += (predicted == labels_batch).sum().item()
        
        accuracy = correct / total
        logger.info(f"Transformer Model Accuracy: {accuracy:.3f}")
        
        return self.transformer_model
    
    def train_model(self, texts: List[str] = None, labels: List[str] = None):
        """Train both ensemble and transformer models"""
        if texts is None or labels is None:
            texts, labels = self.create_enhanced_training_data()
        
        logger.info(f"Training with {len(texts)} samples")
        
        # Train ensemble model
        self.train_ensemble_model(texts, labels)
        
        # Train transformer model (if GPU available or small dataset)
        if torch.cuda.is_available() or len(texts) < 1000:
            self.train_transformer_model(texts, labels)
        else:
            logger.info("Skipping transformer training (no GPU and large dataset)")
        
        return True
    
    def predict(self, text: str) -> Tuple[str, float]:
        """Predict using ensemble of models"""
        if not self.ensemble_model:
            self.load_model()
        
        if not self.ensemble_model:
            return 'OTHERS', 0.0
        
        # Ensemble prediction
        tfidf_features = self.tfidf_vectorizer.transform([text])
        ensemble_proba = self.ensemble_model.predict_proba(tfidf_features)[0]
        ensemble_pred = self.ensemble_model.predict(tfidf_features)[0]
        
        # Transformer prediction (if available)
        if self.transformer_model and self.tokenizer:
            try:
                self.transformer_model.eval()
                encoding = self.tokenizer(
                    text,
                    truncation=True,
                    padding='max_length',
                    max_length=self.max_length,
                    return_tensors='pt'
                )
                
                with torch.no_grad():
                    input_ids = encoding['input_ids'].to(self.device)
                    attention_mask = encoding['attention_mask'].to(self.device)
                    outputs = self.transformer_model(input_ids, attention_mask)
                    transformer_proba = torch.softmax(outputs, dim=1).cpu().numpy()[0]
                
                # Combine predictions (weighted average)
                combined_proba = 0.6 * ensemble_proba + 0.4 * transformer_proba
                final_pred = np.argmax(combined_proba)
                confidence = combined_proba[final_pred]
                
            except Exception as e:
                logger.warning(f"Transformer prediction failed: {e}")
                final_pred = ensemble_pred
                confidence = ensemble_proba[final_pred]
        else:
            final_pred = ensemble_pred
            confidence = ensemble_proba[final_pred]
        
        predicted_class = self.label_encoder.inverse_transform([final_pred])[0]
        return predicted_class, float(confidence)
    
    def save_model(self):
        """Save all model components"""
        model_data = {
            'ensemble_model': self.ensemble_model,
            'tfidf_vectorizer': self.tfidf_vectorizer,
            'label_encoder': self.label_encoder,
            'classes': self.classes
        }
        
        # Save transformer model separately if it exists
        if self.transformer_model:
            torch.save(self.transformer_model.state_dict(), 
                      self.model_path.replace('.pkl', '_transformer.pth'))
            model_data['has_transformer'] = True
        
        os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
        joblib.dump(model_data, self.model_path)
        logger.info(f"Advanced model saved to {self.model_path}")
    
    def load_model(self):
        """Load all model components"""
        if os.path.exists(self.model_path):
            model_data = joblib.load(self.model_path)
            self.ensemble_model = model_data['ensemble_model']
            self.tfidf_vectorizer = model_data['tfidf_vectorizer']
            self.label_encoder = model_data['label_encoder']
            self.classes = model_data['classes']
            
            # Load transformer model if it exists
            transformer_path = self.model_path.replace('.pkl', '_transformer.pth')
            if model_data.get('has_transformer') and os.path.exists(transformer_path):
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
                self.transformer_model = TransformerDocumentClassifier(
                    self.model_name, 
                    len(self.label_encoder.classes_)
                ).to(self.device)
                self.transformer_model.load_state_dict(torch.load(transformer_path, map_location=self.device))
            
            logger.info(f"Advanced model loaded from {self.model_path}")
            return True
        return False

# Example usage
if __name__ == "__main__":
    classifier = AdvancedDocumentClassifier()
    
    # Train the model
    logger.info("Training advanced document classifier...")
    classifier.train_model()
    
    # Save the model
    classifier.save_model()
    
    # Test prediction
    test_text = "HDFC Bank Statement Account Number ********** Opening Balance Rs 50000 Transaction Date Debit Credit"
    prediction, confidence = classifier.predict(test_text)
    logger.info(f"Prediction: {prediction} (Confidence: {confidence:.3f})")
