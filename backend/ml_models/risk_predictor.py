"""
Risk Prediction Model
Predicts audit risk levels based on client and engagement characteristics
"""

import os
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.metrics import classification_report, confusion_matrix
import joblib
from typing import Dict, List, Tuple, Any

class RiskPredictor:
    """ML model for audit risk prediction"""
    
    def __init__(self, model_path: str = None):
        self.model_path = model_path or 'ml_models/saved_models/risk_predictor.pkl'
        self.model = None
        self.scaler = StandardScaler()
        self.label_encoders = {}
        self.feature_columns = [
            'annual_turnover', 'number_of_employees', 'entity_type_encoded',
            'industry_encoded', 'size_category_encoded', 'previous_audit_issues',
            'management_changes', 'system_changes', 'regulatory_changes',
            'financial_distress_indicators', 'related_party_transactions',
            'complex_transactions', 'geographic_spread', 'audit_committee_effectiveness'
        ]
        
    def create_training_data(self) -> pd.DataFrame:
        """Create synthetic training data for risk prediction"""
        np.random.seed(42)
        n_samples = 1000
        
        # Generate synthetic data
        data = {
            'annual_turnover': np.random.lognormal(15, 2, n_samples),  # Log-normal distribution
            'number_of_employees': np.random.randint(10, 5000, n_samples),
            'entity_type': np.random.choice(['PRIVATE_LIMITED', 'PUBLIC_LIMITED', 'PARTNERSHIP', 'LLP'], n_samples),
            'industry': np.random.choice(['IT_SOFTWARE', 'MANUFACTURING', 'FINANCE', 'RETAIL', 'HEALTHCARE'], n_samples),
            'size_category': np.random.choice(['MICRO', 'SMALL', 'MEDIUM', 'LARGE'], n_samples),
            'previous_audit_issues': np.random.randint(0, 5, n_samples),
            'management_changes': np.random.randint(0, 3, n_samples),
            'system_changes': np.random.randint(0, 2, n_samples),
            'regulatory_changes': np.random.randint(0, 3, n_samples),
            'financial_distress_indicators': np.random.randint(0, 4, n_samples),
            'related_party_transactions': np.random.randint(0, 3, n_samples),
            'complex_transactions': np.random.randint(0, 4, n_samples),
            'geographic_spread': np.random.randint(1, 10, n_samples),
            'audit_committee_effectiveness': np.random.randint(1, 5, n_samples)
        }
        
        df = pd.DataFrame(data)
        
        # Create risk labels based on business logic
        risk_scores = (
            (df['previous_audit_issues'] * 0.2) +
            (df['management_changes'] * 0.15) +
            (df['system_changes'] * 0.1) +
            (df['regulatory_changes'] * 0.1) +
            (df['financial_distress_indicators'] * 0.25) +
            (df['related_party_transactions'] * 0.1) +
            (df['complex_transactions'] * 0.15) +
            ((df['geographic_spread'] - 1) * 0.05) +
            ((5 - df['audit_committee_effectiveness']) * 0.1)
        )
        
        # Add industry-specific risk adjustments
        industry_risk = {
            'FINANCE': 0.3,
            'IT_SOFTWARE': 0.1,
            'MANUFACTURING': 0.2,
            'RETAIL': 0.15,
            'HEALTHCARE': 0.25
        }
        
        for industry, risk_adj in industry_risk.items():
            risk_scores += (df['industry'] == industry) * risk_adj
        
        # Add size-based risk adjustments
        size_risk = {
            'LARGE': 0.2,
            'MEDIUM': 0.1,
            'SMALL': 0.05,
            'MICRO': 0.0
        }
        
        for size, risk_adj in size_risk.items():
            risk_scores += (df['size_category'] == size) * risk_adj
        
        # Convert to risk categories
        df['risk_level'] = pd.cut(
            risk_scores,
            bins=[-np.inf, 0.5, 1.0, np.inf],
            labels=['LOW', 'MEDIUM', 'HIGH']
        )
        
        return df
    
    def preprocess_data(self, df: pd.DataFrame, fit_encoders: bool = False) -> pd.DataFrame:
        """Preprocess data for model training/prediction"""
        df_processed = df.copy()
        
        # Encode categorical variables
        categorical_columns = ['entity_type', 'industry', 'size_category']
        
        for col in categorical_columns:
            if col in df_processed.columns:
                if fit_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    df_processed[f'{col}_encoded'] = self.label_encoders[col].fit_transform(df_processed[col])
                else:
                    if col in self.label_encoders:
                        # Handle unseen categories
                        unique_values = set(df_processed[col].unique())
                        known_values = set(self.label_encoders[col].classes_)
                        
                        # Replace unseen values with most common known value
                        if unique_values - known_values:
                            most_common = self.label_encoders[col].classes_[0]
                            df_processed[col] = df_processed[col].apply(
                                lambda x: x if x in known_values else most_common
                            )
                        
                        df_processed[f'{col}_encoded'] = self.label_encoders[col].transform(df_processed[col])
                    else:
                        # If encoder doesn't exist, create dummy encoding
                        df_processed[f'{col}_encoded'] = 0
        
        # Handle missing values
        numeric_columns = ['annual_turnover', 'number_of_employees', 'previous_audit_issues',
                          'management_changes', 'system_changes', 'regulatory_changes',
                          'financial_distress_indicators', 'related_party_transactions',
                          'complex_transactions', 'geographic_spread', 'audit_committee_effectiveness']
        
        for col in numeric_columns:
            if col in df_processed.columns:
                df_processed[col] = df_processed[col].fillna(df_processed[col].median())
        
        # Log transform skewed features
        if 'annual_turnover' in df_processed.columns:
            df_processed['annual_turnover'] = np.log1p(df_processed['annual_turnover'])
        
        return df_processed
    
    def train_model(self, df: pd.DataFrame = None):
        """Train the risk prediction model"""
        if df is None:
            df = self.create_training_data()
        
        # Preprocess data
        df_processed = self.preprocess_data(df, fit_encoders=True)
        
        # Prepare features and target
        X = df_processed[self.feature_columns]
        y = df_processed['risk_level']
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Train multiple models and select best
        models = {
            'RandomForest': RandomForestClassifier(n_estimators=100, random_state=42),
            'GradientBoosting': GradientBoostingClassifier(n_estimators=100, random_state=42)
        }
        
        best_score = 0
        best_model = None
        best_name = None
        
        for name, model in models.items():
            # Cross-validation
            cv_scores = cross_val_score(model, X_train_scaled, y_train, cv=5)
            mean_score = cv_scores.mean()
            
            print(f"{name} CV Score: {mean_score:.3f} (+/- {cv_scores.std() * 2:.3f})")
            
            if mean_score > best_score:
                best_score = mean_score
                best_model = model
                best_name = name
        
        # Train best model
        self.model = best_model
        self.model.fit(X_train_scaled, y_train)
        
        # Evaluate model
        train_score = self.model.score(X_train_scaled, y_train)
        test_score = self.model.score(X_test_scaled, y_test)
        
        print(f"\nBest Model: {best_name}")
        print(f"Training Accuracy: {train_score:.3f}")
        print(f"Testing Accuracy: {test_score:.3f}")
        
        # Classification report
        y_pred = self.model.predict(X_test_scaled)
        print("\nClassification Report:")
        print(classification_report(y_test, y_pred))
        
        return self.model
    
    def predict_risk(self, client_data: Dict[str, Any]) -> Tuple[str, float]:
        """Predict risk level for a client"""
        if not self.model:
            self.load_model()
        
        if not self.model:
            return 'MEDIUM', 0.5
        
        # Convert to DataFrame
        df = pd.DataFrame([client_data])
        
        # Preprocess
        df_processed = self.preprocess_data(df, fit_encoders=False)
        
        # Ensure all required columns exist
        for col in self.feature_columns:
            if col not in df_processed.columns:
                df_processed[col] = 0
        
        # Select features and scale
        X = df_processed[self.feature_columns]
        X_scaled = self.scaler.transform(X)
        
        # Predict
        prediction = self.model.predict(X_scaled)[0]
        probabilities = self.model.predict_proba(X_scaled)[0]
        confidence = max(probabilities)
        
        return prediction, confidence
    
    def get_risk_factors(self, client_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get specific risk factors for a client"""
        risk_factors = []
        
        # Check various risk indicators
        if client_data.get('previous_audit_issues', 0) > 2:
            risk_factors.append({
                'factor': 'Previous Audit Issues',
                'level': 'HIGH',
                'description': 'Client has history of significant audit issues'
            })
        
        if client_data.get('financial_distress_indicators', 0) > 2:
            risk_factors.append({
                'factor': 'Financial Distress',
                'level': 'HIGH',
                'description': 'Indicators of financial difficulties detected'
            })
        
        if client_data.get('management_changes', 0) > 1:
            risk_factors.append({
                'factor': 'Management Changes',
                'level': 'MEDIUM',
                'description': 'Recent changes in key management positions'
            })
        
        if client_data.get('complex_transactions', 0) > 2:
            risk_factors.append({
                'factor': 'Complex Transactions',
                'level': 'MEDIUM',
                'description': 'Presence of complex or unusual transactions'
            })
        
        if client_data.get('industry') == 'FINANCE':
            risk_factors.append({
                'factor': 'Industry Risk',
                'level': 'MEDIUM',
                'description': 'Financial services industry has inherent regulatory risks'
            })
        
        return risk_factors
    
    def calculate_materiality(self, financial_data: Dict[str, float], risk_level: str) -> Dict[str, float]:
        """Calculate materiality amounts based on financial data and risk"""
        # Base percentages by risk level
        risk_percentages = {
            'LOW': {'revenue': 0.07, 'net_income': 0.10, 'assets': 0.02},
            'MEDIUM': {'revenue': 0.05, 'net_income': 0.05, 'assets': 0.01},
            'HIGH': {'revenue': 0.03, 'net_income': 0.03, 'assets': 0.005}
        }
        
        percentages = risk_percentages.get(risk_level, risk_percentages['MEDIUM'])
        
        # Determine base amount
        revenue = financial_data.get('revenue', 0)
        net_income = financial_data.get('net_income', 0)
        total_assets = financial_data.get('total_assets', 0)
        
        base_amount = 0
        base_type = 'revenue'
        
        if revenue > 0:
            base_amount = revenue * percentages['revenue']
            base_type = 'revenue'
        elif abs(net_income) > 0:
            base_amount = abs(net_income) * percentages['net_income']
            base_type = 'net_income'
        elif total_assets > 0:
            base_amount = total_assets * percentages['assets']
            base_type = 'assets'
        
        # Calculate related amounts
        performance_materiality = base_amount * 0.75
        trivial_threshold = base_amount * 0.05
        
        return {
            'overall_materiality': base_amount,
            'performance_materiality': performance_materiality,
            'trivial_threshold': trivial_threshold,
            'base_type': base_type,
            'base_amount': financial_data.get(base_type, 0),
            'percentage_applied': percentages[base_type]
        }
    
    def save_model(self):
        """Save trained model and preprocessors"""
        if self.model:
            model_data = {
                'model': self.model,
                'scaler': self.scaler,
                'label_encoders': self.label_encoders,
                'feature_columns': self.feature_columns
            }
            
            os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
            joblib.dump(model_data, self.model_path)
            print(f"Model saved to {self.model_path}")
    
    def load_model(self):
        """Load trained model and preprocessors"""
        if os.path.exists(self.model_path):
            model_data = joblib.load(self.model_path)
            self.model = model_data['model']
            self.scaler = model_data['scaler']
            self.label_encoders = model_data['label_encoders']
            self.feature_columns = model_data['feature_columns']
            print(f"Model loaded from {self.model_path}")
            return True
        return False
    
    def get_feature_importance(self) -> Dict[str, float]:
        """Get feature importance from the trained model"""
        if not self.model or not hasattr(self.model, 'feature_importances_'):
            return {}
        
        importance_dict = dict(zip(self.feature_columns, self.model.feature_importances_))
        return dict(sorted(importance_dict.items(), key=lambda x: x[1], reverse=True))

# Example usage and training
if __name__ == "__main__":
    predictor = RiskPredictor()
    
    # Train the model
    print("Training risk prediction model...")
    predictor.train_model()
    
    # Save the model
    predictor.save_model()
    
    # Test prediction
    test_client = {
        'annual_turnover': 50000000,
        'number_of_employees': 150,
        'entity_type': 'PRIVATE_LIMITED',
        'industry': 'IT_SOFTWARE',
        'size_category': 'MEDIUM',
        'previous_audit_issues': 1,
        'management_changes': 0,
        'system_changes': 1,
        'regulatory_changes': 1,
        'financial_distress_indicators': 0,
        'related_party_transactions': 1,
        'complex_transactions': 1,
        'geographic_spread': 3,
        'audit_committee_effectiveness': 4
    }
    
    print("\nTest Prediction:")
    risk_level, confidence = predictor.predict_risk(test_client)
    print(f"Predicted Risk Level: {risk_level} (Confidence: {confidence:.3f})")
    
    # Get risk factors
    risk_factors = predictor.get_risk_factors(test_client)
    print(f"\nIdentified Risk Factors: {len(risk_factors)}")
    for factor in risk_factors:
        print(f"- {factor['factor']}: {factor['level']} - {factor['description']}")
    
    # Calculate materiality
    financial_data = {
        'revenue': 50000000,
        'net_income': 5000000,
        'total_assets': 25000000
    }
    
    materiality = predictor.calculate_materiality(financial_data, risk_level)
    print(f"\nMateriality Calculation:")
    print(f"Overall Materiality: ₹{materiality['overall_materiality']:,.0f}")
    print(f"Performance Materiality: ₹{materiality['performance_materiality']:,.0f}")
    print(f"Trivial Threshold: ₹{materiality['trivial_threshold']:,.0f}")
    
    # Show feature importance
    print("\nFeature Importance:")
    importance = predictor.get_feature_importance()
    for feature, imp in list(importance.items())[:10]:
        print(f"{feature}: {imp:.3f}")
