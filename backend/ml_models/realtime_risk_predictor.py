"""
Real-time Learning Risk Predictor
Implements online learning and adaptive risk assessment with continuous model updates
"""

import os
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import SGDClassifier, PassiveAggressiveClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, accuracy_score
from sklearn.model_selection import cross_val_score
import joblib
from typing import Dict, List, Tuple, Any
import logging
from datetime import datetime, timedelta
import threading
import queue
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OnlineLearningBuffer:
    """Buffer for storing new training samples for online learning"""
    
    def __init__(self, max_size=1000):
        self.max_size = max_size
        self.buffer = queue.Queue(maxsize=max_size)
        self.lock = threading.Lock()
    
    def add_sample(self, features: Dict[str, Any], true_label: str, prediction: str, confidence: float):
        """Add a new sample to the buffer"""
        sample = {
            'features': features,
            'true_label': true_label,
            'prediction': prediction,
            'confidence': confidence,
            'timestamp': datetime.now(),
            'is_correct': true_label == prediction
        }
        
        with self.lock:
            if self.buffer.full():
                # Remove oldest sample
                try:
                    self.buffer.get_nowait()
                except queue.Empty:
                    pass
            
            self.buffer.put(sample)
    
    def get_samples(self, min_samples=10) -> List[Dict]:
        """Get samples from buffer for retraining"""
        samples = []
        with self.lock:
            while not self.buffer.empty() and len(samples) < self.buffer.qsize():
                try:
                    samples.append(self.buffer.get_nowait())
                except queue.Empty:
                    break
        
        return samples if len(samples) >= min_samples else []
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """Get recent performance metrics"""
        samples = []
        temp_queue = queue.Queue()
        
        with self.lock:
            # Temporarily extract all samples
            while not self.buffer.empty():
                try:
                    sample = self.buffer.get_nowait()
                    samples.append(sample)
                    temp_queue.put(sample)
                except queue.Empty:
                    break
            
            # Put samples back
            while not temp_queue.empty():
                try:
                    self.buffer.put(temp_queue.get_nowait())
                except queue.Full:
                    break
        
        if not samples:
            return {'accuracy': 0.0, 'avg_confidence': 0.0, 'sample_count': 0}
        
        accuracy = sum(1 for s in samples if s['is_correct']) / len(samples)
        avg_confidence = sum(s['confidence'] for s in samples) / len(samples)
        
        return {
            'accuracy': accuracy,
            'avg_confidence': avg_confidence,
            'sample_count': len(samples)
        }

class AdaptiveRiskPredictor:
    """Advanced risk predictor with online learning and model adaptation"""
    
    def __init__(self, model_path: str = None):
        self.model_path = model_path or 'ml_models/saved_models/adaptive_risk_predictor.pkl'
        
        # Model ensemble
        self.base_model = None
        self.online_model = None
        self.meta_model = None
        
        # Preprocessing
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        
        # Online learning components
        self.learning_buffer = OnlineLearningBuffer()
        self.last_retrain_time = datetime.now()
        self.retrain_interval = timedelta(hours=24)  # Retrain daily
        self.min_samples_for_retrain = 50
        
        # Model performance tracking
        self.performance_history = []
        self.drift_threshold = 0.1  # Accuracy drop threshold for drift detection
        
        # Feature importance tracking
        self.feature_importance_history = []
        
        # Configuration
        self.feature_columns = [
            'annual_turnover', 'number_of_employees', 'entity_type_encoded',
            'industry_encoded', 'size_category_encoded', 'previous_audit_issues',
            'management_changes', 'system_changes', 'regulatory_changes',
            'financial_distress_indicators', 'related_party_transactions',
            'complex_transactions', 'geographic_spread', 'audit_committee_effectiveness',
            'days_since_last_audit', 'audit_history_score', 'financial_ratio_score',
            'compliance_score', 'market_volatility_index'
        ]
        
        logger.info("Adaptive Risk Predictor initialized")
    
    def extract_enhanced_features(self, client_data: Dict[str, Any]) -> Dict[str, float]:
        """Extract enhanced features including temporal and contextual information"""
        features = {}
        
        # Basic features
        features['annual_turnover'] = float(client_data.get('annual_turnover', 0))
        features['number_of_employees'] = float(client_data.get('number_of_employees', 0))
        features['previous_audit_issues'] = float(client_data.get('previous_audit_issues', 0))
        features['management_changes'] = float(client_data.get('management_changes', 0))
        features['system_changes'] = float(client_data.get('system_changes', 0))
        features['regulatory_changes'] = float(client_data.get('regulatory_changes', 0))
        features['financial_distress_indicators'] = float(client_data.get('financial_distress_indicators', 0))
        features['related_party_transactions'] = float(client_data.get('related_party_transactions', 0))
        features['complex_transactions'] = float(client_data.get('complex_transactions', 0))
        features['geographic_spread'] = float(client_data.get('geographic_spread', 1))
        features['audit_committee_effectiveness'] = float(client_data.get('audit_committee_effectiveness', 3))
        
        # Enhanced temporal features
        last_audit_date = client_data.get('last_audit_date')
        if last_audit_date:
            if isinstance(last_audit_date, str):
                last_audit_date = datetime.strptime(last_audit_date, '%Y-%m-%d')
            features['days_since_last_audit'] = (datetime.now() - last_audit_date).days
        else:
            features['days_since_last_audit'] = 365  # Default to 1 year
        
        # Audit history score (based on past audit results)
        audit_history = client_data.get('audit_history', [])
        if audit_history:
            # Calculate weighted score based on recency and severity
            total_score = 0
            total_weight = 0
            for i, audit in enumerate(audit_history[-5:]):  # Last 5 audits
                weight = 1 / (i + 1)  # More recent audits have higher weight
                severity = audit.get('issues_severity', 0)  # 0-5 scale
                total_score += severity * weight
                total_weight += weight
            features['audit_history_score'] = total_score / total_weight if total_weight > 0 else 0
        else:
            features['audit_history_score'] = 0
        
        # Financial ratio score
        financial_ratios = client_data.get('financial_ratios', {})
        ratio_score = 0
        if financial_ratios:
            # Current ratio
            current_ratio = financial_ratios.get('current_ratio', 1.0)
            if current_ratio < 1.0:
                ratio_score += 2
            elif current_ratio < 1.5:
                ratio_score += 1
            
            # Debt to equity ratio
            debt_equity = financial_ratios.get('debt_to_equity', 0.5)
            if debt_equity > 2.0:
                ratio_score += 2
            elif debt_equity > 1.0:
                ratio_score += 1
            
            # ROA
            roa = financial_ratios.get('return_on_assets', 0.05)
            if roa < 0:
                ratio_score += 2
            elif roa < 0.02:
                ratio_score += 1
        
        features['financial_ratio_score'] = ratio_score
        
        # Compliance score
        compliance_data = client_data.get('compliance_history', {})
        compliance_score = 0
        if compliance_data:
            late_filings = compliance_data.get('late_filings', 0)
            penalties = compliance_data.get('penalties', 0)
            compliance_score = late_filings + penalties * 2
        features['compliance_score'] = compliance_score
        
        # Market volatility index (industry-specific)
        industry = client_data.get('industry', 'OTHERS')
        volatility_map = {
            'IT_SOFTWARE': 0.3,
            'FINANCE': 0.8,
            'MANUFACTURING': 0.5,
            'RETAIL': 0.6,
            'HEALTHCARE': 0.4,
            'ENERGY': 0.7,
            'OTHERS': 0.5
        }
        features['market_volatility_index'] = volatility_map.get(industry, 0.5)
        
        # Encode categorical features
        entity_type_map = {'PRIVATE_LIMITED': 1, 'PUBLIC_LIMITED': 2, 'PARTNERSHIP': 3, 'LLP': 4, 'PROPRIETORSHIP': 5}
        features['entity_type_encoded'] = entity_type_map.get(client_data.get('entity_type', 'PRIVATE_LIMITED'), 1)
        
        industry_map = {'IT_SOFTWARE': 1, 'MANUFACTURING': 2, 'FINANCE': 3, 'RETAIL': 4, 'HEALTHCARE': 5, 'ENERGY': 6, 'OTHERS': 7}
        features['industry_encoded'] = industry_map.get(industry, 7)
        
        size_map = {'MICRO': 1, 'SMALL': 2, 'MEDIUM': 3, 'LARGE': 4}
        features['size_category_encoded'] = size_map.get(client_data.get('size_category', 'MEDIUM'), 3)
        
        return features
    
    def create_enhanced_training_data(self) -> pd.DataFrame:
        """Create enhanced training data with more sophisticated features"""
        np.random.seed(42)
        n_samples = 2000
        
        # Generate base data
        data = {}
        
        # Basic features
        data['annual_turnover'] = np.random.lognormal(15, 2, n_samples)
        data['number_of_employees'] = np.random.randint(10, 5000, n_samples)
        data['entity_type_encoded'] = np.random.randint(1, 6, n_samples)
        data['industry_encoded'] = np.random.randint(1, 8, n_samples)
        data['size_category_encoded'] = np.random.randint(1, 5, n_samples)
        data['previous_audit_issues'] = np.random.randint(0, 6, n_samples)
        data['management_changes'] = np.random.randint(0, 4, n_samples)
        data['system_changes'] = np.random.randint(0, 3, n_samples)
        data['regulatory_changes'] = np.random.randint(0, 4, n_samples)
        data['financial_distress_indicators'] = np.random.randint(0, 5, n_samples)
        data['related_party_transactions'] = np.random.randint(0, 4, n_samples)
        data['complex_transactions'] = np.random.randint(0, 5, n_samples)
        data['geographic_spread'] = np.random.randint(1, 11, n_samples)
        data['audit_committee_effectiveness'] = np.random.randint(1, 6, n_samples)
        
        # Enhanced features
        data['days_since_last_audit'] = np.random.randint(30, 730, n_samples)
        data['audit_history_score'] = np.random.uniform(0, 5, n_samples)
        data['financial_ratio_score'] = np.random.randint(0, 7, n_samples)
        data['compliance_score'] = np.random.randint(0, 10, n_samples)
        data['market_volatility_index'] = np.random.uniform(0.2, 0.9, n_samples)
        
        df = pd.DataFrame(data)
        
        # Create sophisticated risk calculation
        risk_scores = (
            (df['previous_audit_issues'] * 0.15) +
            (df['management_changes'] * 0.10) +
            (df['system_changes'] * 0.08) +
            (df['regulatory_changes'] * 0.08) +
            (df['financial_distress_indicators'] * 0.20) +
            (df['related_party_transactions'] * 0.08) +
            (df['complex_transactions'] * 0.12) +
            ((df['geographic_spread'] - 1) * 0.03) +
            ((6 - df['audit_committee_effectiveness']) * 0.08) +
            (df['audit_history_score'] * 0.15) +
            (df['financial_ratio_score'] * 0.10) +
            (df['compliance_score'] * 0.12) +
            (df['market_volatility_index'] * 0.08) +
            (np.log(df['days_since_last_audit'] / 365) * 0.05)
        )
        
        # Add industry-specific adjustments
        industry_risk = {1: 0.1, 2: 0.2, 3: 0.3, 4: 0.15, 5: 0.25, 6: 0.2, 7: 0.1}
        for industry, risk_adj in industry_risk.items():
            risk_scores += (df['industry_encoded'] == industry) * risk_adj
        
        # Add size-based adjustments
        size_risk = {1: 0.0, 2: 0.05, 3: 0.1, 4: 0.2}
        for size, risk_adj in size_risk.items():
            risk_scores += (df['size_category_encoded'] == size) * risk_adj
        
        # Convert to risk categories with more nuanced thresholds
        df['risk_level'] = pd.cut(
            risk_scores,
            bins=[-np.inf, 0.6, 1.2, np.inf],
            labels=['LOW', 'MEDIUM', 'HIGH']
        )
        
        return df
    
    def train_ensemble_models(self, df: pd.DataFrame):
        """Train ensemble of models including online learning components"""
        logger.info("Training ensemble models...")
        
        # Prepare data
        X = df[self.feature_columns]
        y = df['risk_level']
        
        # Encode labels
        y_encoded = self.label_encoder.fit_transform(y)
        
        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        
        # Train base model (Random Forest for stability)
        self.base_model = RandomForestClassifier(
            n_estimators=200,
            max_depth=15,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42
        )
        self.base_model.fit(X_scaled, y_encoded)
        
        # Train online learning model (SGD for adaptability)
        self.online_model = SGDClassifier(
            loss='log_loss',
            learning_rate='adaptive',
            eta0=0.01,
            random_state=42
        )
        self.online_model.fit(X_scaled, y_encoded)
        
        # Train meta-model for combining predictions
        base_pred = self.base_model.predict_proba(X_scaled)
        online_pred = self.online_model.predict_proba(X_scaled)
        
        # Create meta-features
        meta_features = np.column_stack([
            base_pred,
            online_pred,
            np.max(base_pred, axis=1),  # Max confidence from base
            np.max(online_pred, axis=1),  # Max confidence from online
            np.std(base_pred, axis=1),   # Uncertainty from base
            np.std(online_pred, axis=1)  # Uncertainty from online
        ])
        
        self.meta_model = GradientBoostingClassifier(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=5,
            random_state=42
        )
        self.meta_model.fit(meta_features, y_encoded)
        
        # Evaluate models
        base_score = cross_val_score(self.base_model, X_scaled, y_encoded, cv=5).mean()
        online_score = cross_val_score(self.online_model, X_scaled, y_encoded, cv=5).mean()
        
        logger.info(f"Base Model CV Score: {base_score:.3f}")
        logger.info(f"Online Model CV Score: {online_score:.3f}")
        
        # Store initial performance
        self.performance_history.append({
            'timestamp': datetime.now(),
            'base_score': base_score,
            'online_score': online_score,
            'sample_count': len(df)
        })
        
        return True
    
    def predict_risk(self, client_data: Dict[str, Any]) -> Tuple[str, float, Dict[str, Any]]:
        """Predict risk with ensemble and provide detailed analysis"""
        if not self.base_model:
            self.load_model()
        
        if not self.base_model:
            return 'MEDIUM', 0.5, {}
        
        # Extract features
        features = self.extract_enhanced_features(client_data)
        
        # Ensure all features exist
        for col in self.feature_columns:
            if col not in features:
                features[col] = 0.0
        
        # Prepare feature vector
        X = np.array([list(features.values())]).reshape(1, -1)
        X_scaled = self.scaler.transform(X)
        
        # Get predictions from both models
        base_pred = self.base_model.predict_proba(X_scaled)[0]
        online_pred = self.online_model.predict_proba(X_scaled)[0]
        
        # Create meta-features for final prediction
        meta_features = np.array([[
            *base_pred,
            *online_pred,
            np.max(base_pred),
            np.max(online_pred),
            np.std(base_pred),
            np.std(online_pred)
        ]])
        
        # Final prediction using meta-model
        final_pred = self.meta_model.predict(meta_features)[0]
        final_proba = self.meta_model.predict_proba(meta_features)[0]
        confidence = final_proba[final_pred]
        
        # Convert to risk level
        risk_level = self.label_encoder.inverse_transform([final_pred])[0]
        
        # Generate detailed analysis
        analysis = {
            'base_model_confidence': float(np.max(base_pred)),
            'online_model_confidence': float(np.max(online_pred)),
            'ensemble_confidence': float(confidence),
            'feature_importance': self.get_feature_importance(),
            'risk_factors': self.analyze_risk_factors(features),
            'model_agreement': float(np.abs(np.argmax(base_pred) - np.argmax(online_pred)) == 0)
        }
        
        return risk_level, float(confidence), analysis
    
    def update_model_online(self, client_data: Dict[str, Any], true_risk: str, predicted_risk: str, confidence: float):
        """Update model with new feedback (online learning)"""
        # Add to learning buffer
        features = self.extract_enhanced_features(client_data)
        self.learning_buffer.add_sample(features, true_risk, predicted_risk, confidence)
        
        # Check if it's time to retrain
        if (datetime.now() - self.last_retrain_time) > self.retrain_interval:
            self.retrain_if_needed()
    
    def retrain_if_needed(self):
        """Retrain models if enough new samples are available"""
        samples = self.learning_buffer.get_samples(self.min_samples_for_retrain)
        
        if not samples:
            logger.info("Not enough samples for retraining")
            return
        
        logger.info(f"Retraining with {len(samples)} new samples")
        
        # Prepare new training data
        X_new = []
        y_new = []
        
        for sample in samples:
            features = sample['features']
            feature_vector = [features.get(col, 0.0) for col in self.feature_columns]
            X_new.append(feature_vector)
            y_new.append(sample['true_label'])
        
        X_new = np.array(X_new)
        y_new_encoded = self.label_encoder.transform(y_new)
        X_new_scaled = self.scaler.transform(X_new)
        
        # Update online model with partial fit
        self.online_model.partial_fit(X_new_scaled, y_new_encoded)
        
        # Check for concept drift
        current_performance = self.learning_buffer.get_performance_metrics()
        if self.detect_concept_drift(current_performance):
            logger.warning("Concept drift detected - full model retraining recommended")
            # In production, trigger full retraining here
        
        self.last_retrain_time = datetime.now()
        
        # Update performance history
        self.performance_history.append({
            'timestamp': datetime.now(),
            'accuracy': current_performance['accuracy'],
            'confidence': current_performance['avg_confidence'],
            'sample_count': current_performance['sample_count']
        })
    
    def detect_concept_drift(self, current_performance: Dict[str, float]) -> bool:
        """Detect if there's concept drift in the model"""
        if len(self.performance_history) < 2:
            return False
        
        recent_accuracy = current_performance['accuracy']
        historical_accuracy = np.mean([p.get('accuracy', p.get('base_score', 0.8)) 
                                     for p in self.performance_history[-5:]])
        
        drift_detected = (historical_accuracy - recent_accuracy) > self.drift_threshold
        
        if drift_detected:
            logger.warning(f"Concept drift detected: accuracy dropped from {historical_accuracy:.3f} to {recent_accuracy:.3f}")
        
        return drift_detected
    
    def get_feature_importance(self) -> Dict[str, float]:
        """Get feature importance from the base model"""
        if not self.base_model:
            return {}
        
        importance = dict(zip(self.feature_columns, self.base_model.feature_importances_))
        return dict(sorted(importance.items(), key=lambda x: x[1], reverse=True))
    
    def analyze_risk_factors(self, features: Dict[str, float]) -> List[Dict[str, Any]]:
        """Analyze specific risk factors for the client"""
        risk_factors = []
        
        # High-risk indicators
        if features.get('previous_audit_issues', 0) > 3:
            risk_factors.append({
                'factor': 'Previous Audit Issues',
                'level': 'HIGH',
                'value': features['previous_audit_issues'],
                'description': 'Client has significant history of audit issues'
            })
        
        if features.get('financial_distress_indicators', 0) > 3:
            risk_factors.append({
                'factor': 'Financial Distress',
                'level': 'HIGH',
                'value': features['financial_distress_indicators'],
                'description': 'Multiple indicators of financial difficulties'
            })
        
        if features.get('compliance_score', 0) > 5:
            risk_factors.append({
                'factor': 'Compliance Issues',
                'level': 'MEDIUM',
                'value': features['compliance_score'],
                'description': 'History of compliance violations'
            })
        
        if features.get('days_since_last_audit', 365) > 500:
            risk_factors.append({
                'factor': 'Audit Frequency',
                'level': 'MEDIUM',
                'value': features['days_since_last_audit'],
                'description': 'Extended period since last audit'
            })
        
        return risk_factors
    
    def save_model(self):
        """Save all model components"""
        model_data = {
            'base_model': self.base_model,
            'online_model': self.online_model,
            'meta_model': self.meta_model,
            'scaler': self.scaler,
            'label_encoder': self.label_encoder,
            'feature_columns': self.feature_columns,
            'performance_history': self.performance_history,
            'last_retrain_time': self.last_retrain_time
        }
        
        os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
        joblib.dump(model_data, self.model_path)
        logger.info(f"Adaptive model saved to {self.model_path}")
    
    def load_model(self):
        """Load all model components"""
        if os.path.exists(self.model_path):
            model_data = joblib.load(self.model_path)
            self.base_model = model_data['base_model']
            self.online_model = model_data['online_model']
            self.meta_model = model_data['meta_model']
            self.scaler = model_data['scaler']
            self.label_encoder = model_data['label_encoder']
            self.feature_columns = model_data['feature_columns']
            self.performance_history = model_data.get('performance_history', [])
            self.last_retrain_time = model_data.get('last_retrain_time', datetime.now())
            logger.info(f"Adaptive model loaded from {self.model_path}")
            return True
        return False
    
    def train_model(self, df: pd.DataFrame = None):
        """Train the complete adaptive model"""
        if df is None:
            df = self.create_enhanced_training_data()
        
        logger.info(f"Training adaptive model with {len(df)} samples")
        return self.train_ensemble_models(df)

# Example usage
if __name__ == "__main__":
    predictor = AdaptiveRiskPredictor()
    
    # Train the model
    logger.info("Training adaptive risk predictor...")
    predictor.train_model()
    
    # Save the model
    predictor.save_model()
    
    # Test prediction
    test_client = {
        'annual_turnover': 50000000,
        'number_of_employees': 150,
        'entity_type': 'PRIVATE_LIMITED',
        'industry': 'IT_SOFTWARE',
        'size_category': 'MEDIUM',
        'previous_audit_issues': 1,
        'management_changes': 0,
        'system_changes': 1,
        'regulatory_changes': 1,
        'financial_distress_indicators': 0,
        'related_party_transactions': 1,
        'complex_transactions': 1,
        'geographic_spread': 3,
        'audit_committee_effectiveness': 4,
        'last_audit_date': '2023-03-31',
        'audit_history': [
            {'issues_severity': 1, 'date': '2023-03-31'},
            {'issues_severity': 0, 'date': '2022-03-31'}
        ],
        'financial_ratios': {
            'current_ratio': 1.5,
            'debt_to_equity': 0.8,
            'return_on_assets': 0.06
        },
        'compliance_history': {
            'late_filings': 1,
            'penalties': 0
        }
    }
    
    risk_level, confidence, analysis = predictor.predict_risk(test_client)
    logger.info(f"Predicted Risk: {risk_level} (Confidence: {confidence:.3f})")
    logger.info(f"Analysis: {analysis}")
    
    # Simulate online learning
    predictor.update_model_online(test_client, 'MEDIUM', risk_level, confidence)
