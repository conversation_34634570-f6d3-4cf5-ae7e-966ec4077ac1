{
  "training_summary": {
    "document_classifier": {
      "status": "failed",
      "error": "The test_size = 3 should be greater or equal to the number of classes = 5"
    },
    "risk_predictor": {
      "status": "success",
      "training_time": 2.056469,
      "model_path": "ml_models/saved_models/risk_predictor.pkl"
    },
    "anomaly_detector": {
      "status": "success",
      "training_time": 1.183973,
      "model_path": "ml_models/saved_models/anomaly_detector.pkl"
    }
  },
  "validation_results": {
    "document_classifier": {
      "status": "model_not_found"
    },
    "risk_predictor": {
      "status": "valid",
      "test_prediction": "HIGH",
      "test_confidence": 0.9999844307261996
    },
    "anomaly_detector": {
      "status": "valid",
      "test_prediction": 