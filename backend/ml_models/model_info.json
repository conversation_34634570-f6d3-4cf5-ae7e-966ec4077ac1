{"training_date": "2025-07-09T12:24:28.793354", "models": {"document_classifier": {"model_path": "ml_models/saved_models/document_classifier.pkl", "description": "Classifies uploaded documents into types (bank statements, GST reports, etc.)", "input_format": {"type": "string", "description": "Extracted text from document"}, "output_format": {"prediction": "string (document type)", "confidence": "float (0-1)"}}, "risk_predictor": {"model_path": "ml_models/saved_models/risk_predictor.pkl", "description": "Predicts audit risk levels based on client characteristics", "input_format": {"type": "object", "properties": {"annual_turnover": "float", "number_of_employees": "int", "entity_type": "string", "industry": "string", "size_category": "string", "previous_audit_issues": "int", "management_changes": "int", "system_changes": "int", "regulatory_changes": "int", "financial_distress_indicators": "int", "related_party_transactions": "int", "complex_transactions": "int", "geographic_spread": "int", "audit_committee_effectiveness": "int"}}, "output_format": {"risk_level": "string (LOW/MEDIUM/HIGH)", "confidence": "float (0-1)"}}, "anomaly_detector": {"model_path": "ml_models/saved_models/anomaly_detector.pkl", "description": "Detects unusual patterns in financial documents and transactions", "input_format": {"type": "object", "properties": {"amounts": "array of objects with value field", "dates": "array of objects with text field", "extracted_text": "string", "entities": "array of strings", "confidence_scores": "array of floats"}}, "output_format": {"is_anomaly": "boolean", "confidence": "float", "reasons": "array of strings"}}}, "training_results": {"document_classifier": {"status": "failed", "error": "The test_size = 3 should be greater or equal to the number of classes = 5"}, "risk_predictor": {"status": "success", "training_time": 2.056469, "model_path": "ml_models/saved_models/risk_predictor.pkl"}, "anomaly_detector": {"status": "success", "training_time": 1.183973, "model_path": "ml_models/saved_models/anomaly_detector.pkl"}}}