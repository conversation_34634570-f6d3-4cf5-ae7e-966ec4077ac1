"""models.py - Working Papers and Documentation System"""

from django.db import models
from django.core.validators import FileExtensionValidator
from audit_engagements.models import AuditEngagement
from users.models import User
import uuid

class WorkingPaperFolder(models.Model):
    """Hierarchical folder structure for organizing working papers"""
    
    FOLDER_TYPES = [
        ('PLANNING', 'Planning'),
        ('RISK_ASSESSMENT', 'Risk Assessment'),
        ('INTERNAL_CONTROLS', 'Internal Controls'),
        ('SUBSTANTIVE_TESTING', 'Substantive Testing'),
        ('COMPLETION', 'Completion'),
        ('REPORTING', 'Reporting'),
        ('CORRESPONDENCE', 'Correspondence'),
        ('MANAGEMENT_LETTER', 'Management Letter'),
        ('CUSTOM', 'Custom'),
    ]
    
    engagement = models.ForeignKey(AuditEngagement, on_delete=models.CASCADE, related_name='working_paper_folders')
    name = models.CharField(max_length=255)
    folder_type = models.CharField(max_length=20, choices=FOLDER_TYPES)
    parent_folder = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='subfolders')
    
    # Folder metadata
    description = models.TextField(blank=True, null=True)
    folder_code = models.CharField(max_length=20)  # e.g., "P", "R", "S", "C"
    sequence_number = models.IntegerField(default=1)
    
    # Access control
    is_locked = models.BooleanField(default=False)
    locked_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='locked_folders')
    locked_at = models.DateTimeField(null=True, blank=True)
    
    # System fields
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_folders')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['folder_code', 'sequence_number', 'name']
        unique_together = ['engagement', 'folder_code', 'sequence_number']
    
    def __str__(self):
        return f"{self.folder_code}.{self.sequence_number} - {self.name}"
    
    @property
    def full_path(self):
        """Get the full hierarchical path of the folder"""
        if self.parent_folder:
            return f"{self.parent_folder.full_path}/{self.name}"
        return self.name

class WorkingPaper(models.Model):
    """Individual working paper documents"""
    
    PAPER_TYPES = [
        ('LEAD_SCHEDULE', 'Lead Schedule'),
        ('SUPPORTING_SCHEDULE', 'Supporting Schedule'),
        ('ANALYTICAL_REVIEW', 'Analytical Review'),
        ('TEST_OF_CONTROLS', 'Test of Controls'),
        ('SUBSTANTIVE_TEST', 'Substantive Test'),
        ('CONFIRMATION', 'Confirmation'),
        ('MEMO', 'Memo'),
        ('CHECKLIST', 'Checklist'),
        ('SAMPLING_SHEET', 'Sampling Sheet'),
        ('RECONCILIATION', 'Reconciliation'),
        ('JOURNAL_ENTRY', 'Journal Entry'),
        ('OTHER', 'Other'),
    ]
    
    STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('IN_REVIEW', 'In Review'),
        ('REVIEWED', 'Reviewed'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
    ]
    
    # Basic information
    folder = models.ForeignKey(WorkingPaperFolder, on_delete=models.CASCADE, related_name='working_papers')
    reference_number = models.CharField(max_length=50, unique=True)
    title = models.CharField(max_length=255)
    paper_type = models.CharField(max_length=20, choices=PAPER_TYPES)
    
    # Content
    description = models.TextField()
    content = models.TextField(blank=True, null=True)  # For text-based working papers
    
    # File attachments
    file = models.FileField(
        upload_to='working_papers/',
        validators=[FileExtensionValidator(allowed_extensions=['pdf', 'xlsx', 'docx', 'txt'])],
        blank=True,
        null=True
    )
    
    # Audit trail
    prepared_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='prepared_papers')
    reviewed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='reviewed_papers')
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_papers')
    
    # Status and dates
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='DRAFT')
    prepared_date = models.DateTimeField(auto_now_add=True)
    reviewed_date = models.DateTimeField(null=True, blank=True)
    approved_date = models.DateTimeField(null=True, blank=True)
    
    # Cross-references
    related_papers = models.ManyToManyField('self', blank=True, symmetrical=False)
    
    # Version control
    version = models.IntegerField(default=1)
    is_current_version = models.BooleanField(default=True)
    parent_version = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='versions')
    
    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['reference_number']
        indexes = [
            models.Index(fields=['reference_number']),
            models.Index(fields=['status']),
            models.Index(fields=['paper_type']),
        ]
    
    def __str__(self):
        return f"{self.reference_number} - {self.title}"
    
    def generate_reference_number(self):
        """Generate a unique reference number for the working paper"""
        folder_code = self.folder.folder_code
        paper_count = WorkingPaper.objects.filter(folder=self.folder).count()
        return f"{folder_code}.{paper_count + 1:03d}"

class WorkingPaperComment(models.Model):
    """Comments and annotations on working papers"""
    
    COMMENT_TYPES = [
        ('NOTE', 'Note'),
        ('QUESTION', 'Question'),
        ('ISSUE', 'Issue'),
        ('RESOLUTION', 'Resolution'),
        ('REVIEW_POINT', 'Review Point'),
    ]
    
    working_paper = models.ForeignKey(WorkingPaper, on_delete=models.CASCADE, related_name='comments')
    comment_type = models.CharField(max_length=15, choices=COMMENT_TYPES, default='NOTE')
    comment = models.TextField()
    
    # Position in document (for PDF annotations)
    page_number = models.IntegerField(null=True, blank=True)
    x_position = models.FloatField(null=True, blank=True)
    y_position = models.FloatField(null=True, blank=True)
    
    # Comment metadata
    author = models.ForeignKey(User, on_delete=models.CASCADE)
    is_resolved = models.BooleanField(default=False)
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_comments')
    resolved_at = models.DateTimeField(null=True, blank=True)
    
    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Comment on {self.working_paper.reference_number} by {self.author.username}"

class SamplingSheet(models.Model):
    """Sampling sheets for audit testing"""
    
    SAMPLING_METHODS = [
        ('RANDOM', 'Random Sampling'),
        ('SYSTEMATIC', 'Systematic Sampling'),
        ('STRATIFIED', 'Stratified Sampling'),
        ('HAPHAZARD', 'Haphazard Sampling'),
        ('BLOCK', 'Block Sampling'),
        ('JUDGMENTAL', 'Judgmental Sampling'),
    ]
    
    working_paper = models.OneToOneField(WorkingPaper, on_delete=models.CASCADE, related_name='sampling_sheet')
    
    # Population details
    population_description = models.TextField()
    population_size = models.IntegerField()
    population_value = models.DecimalField(max_digits=15, decimal_places=2)
    
    # Sampling parameters
    sampling_method = models.CharField(max_length=15, choices=SAMPLING_METHODS)
    sample_size = models.IntegerField()
    confidence_level = models.DecimalField(max_digits=5, decimal_places=2, default=95.00)
    tolerable_error = models.DecimalField(max_digits=15, decimal_places=2)
    expected_error = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    
    # Results
    errors_found = models.IntegerField(default=0)
    error_value = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    projected_error = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    
    # Conclusion
    conclusion = models.TextField(blank=True, null=True)
    
    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Sampling Sheet - {self.working_paper.reference_number}"

class CrossReference(models.Model):
    """Cross-references between working papers and other documents"""
    
    REFERENCE_TYPES = [
        ('SUPPORTS', 'Supports'),
        ('SUPPORTED_BY', 'Supported By'),
        ('RELATES_TO', 'Relates To'),
        ('CONTRADICTS', 'Contradicts'),
        ('SUPERSEDES', 'Supersedes'),
        ('SUPERSEDED_BY', 'Superseded By'),
    ]
    
    source_paper = models.ForeignKey(WorkingPaper, on_delete=models.CASCADE, related_name='outgoing_references')
    target_paper = models.ForeignKey(WorkingPaper, on_delete=models.CASCADE, related_name='incoming_references')
    reference_type = models.CharField(max_length=15, choices=REFERENCE_TYPES)
    description = models.TextField(blank=True, null=True)
    
    # System fields
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['source_paper', 'target_paper', 'reference_type']
    
    def __str__(self):
        return f"{self.source_paper.reference_number} {self.reference_type} {self.target_paper.reference_number}"

class WorkingPaperTemplate(models.Model):
    """Templates for common working paper types"""
    
    name = models.CharField(max_length=255)
    paper_type = models.CharField(max_length=20, choices=WorkingPaper.PAPER_TYPES)
    description = models.TextField()
    
    # Template content
    template_content = models.TextField()  # JSON structure for the template
    file_template = models.FileField(upload_to='templates/', blank=True, null=True)
    
    # Metadata
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']
    
    def __str__(self):
        return f"Template: {self.name}"
