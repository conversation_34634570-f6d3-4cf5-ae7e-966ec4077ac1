# Generated by Django 5.2.4 on 2025-07-09 06:06

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("audit_engagements", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="WorkingPaper",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("reference_number", models.CharField(max_length=50, unique=True)),
                ("title", models.CharField(max_length=255)),
                (
                    "paper_type",
                    models.CharField(
                        choices=[
                            ("LEAD_SCHEDULE", "Lead Schedule"),
                            ("SUPPORTING_SCHEDULE", "Supporting Schedule"),
                            ("ANALYTICAL_REVIEW", "Analytical Review"),
                            ("TEST_OF_CONTROLS", "Test of Controls"),
                            ("SUBSTANTIVE_TEST", "Substantive Test"),
                            ("CONFIRMATION", "Confirmation"),
                            ("MEMO", "Memo"),
                            ("CHECKLIST", "Checklist"),
                            ("SAMPLING_SHEET", "Sampling Sheet"),
                            ("RECONCILIATION", "Reconciliation"),
                            ("JOURNAL_ENTRY", "Journal Entry"),
                            ("OTHER", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField()),
                ("content", models.TextField(blank=True, null=True)),
                (
                    "file",
                    models.FileField(
                        blank=True,
                        null=True,
                        upload_to="working_papers/",
                        validators=[
                            django.core.validators.FileExtensionValidator(
                                allowed_extensions=["pdf", "xlsx", "docx", "txt"]
                            )
                        ],
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("DRAFT", "Draft"),
                            ("IN_REVIEW", "In Review"),
                            ("REVIEWED", "Reviewed"),
                            ("APPROVED", "Approved"),
                            ("REJECTED", "Rejected"),
                        ],
                        default="DRAFT",
                        max_length=15,
                    ),
                ),
                ("prepared_date", models.DateTimeField(auto_now_add=True)),
                ("reviewed_date", models.DateTimeField(blank=True, null=True)),
                ("approved_date", models.DateTimeField(blank=True, null=True)),
                ("version", models.IntegerField(default=1)),
                ("is_current_version", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="approved_papers",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "parent_version",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="versions",
                        to="working_papers.workingpaper",
                    ),
                ),
                (
                    "prepared_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="prepared_papers",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "related_papers",
                    models.ManyToManyField(
                        blank=True, to="working_papers.workingpaper"
                    ),
                ),
                (
                    "reviewed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="reviewed_papers",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["reference_number"],
            },
        ),
        migrations.CreateModel(
            name="SamplingSheet",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("population_description", models.TextField()),
                ("population_size", models.IntegerField()),
                (
                    "population_value",
                    models.DecimalField(decimal_places=2, max_digits=15),
                ),
                (
                    "sampling_method",
                    models.CharField(
                        choices=[
                            ("RANDOM", "Random Sampling"),
                            ("SYSTEMATIC", "Systematic Sampling"),
                            ("STRATIFIED", "Stratified Sampling"),
                            ("HAPHAZARD", "Haphazard Sampling"),
                            ("BLOCK", "Block Sampling"),
                            ("JUDGMENTAL", "Judgmental Sampling"),
                        ],
                        max_length=15,
                    ),
                ),
                ("sample_size", models.IntegerField()),
                (
                    "confidence_level",
                    models.DecimalField(decimal_places=2, default=95.0, max_digits=5),
                ),
                (
                    "tolerable_error",
                    models.DecimalField(decimal_places=2, max_digits=15),
                ),
                (
                    "expected_error",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                ("errors_found", models.IntegerField(default=0)),
                (
                    "error_value",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                (
                    "projected_error",
                    models.DecimalField(decimal_places=2, default=0, max_digits=15),
                ),
                ("conclusion", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "working_paper",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sampling_sheet",
                        to="working_papers.workingpaper",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="WorkingPaperComment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "comment_type",
                    models.CharField(
                        choices=[
                            ("NOTE", "Note"),
                            ("QUESTION", "Question"),
                            ("ISSUE", "Issue"),
                            ("RESOLUTION", "Resolution"),
                            ("REVIEW_POINT", "Review Point"),
                        ],
                        default="NOTE",
                        max_length=15,
                    ),
                ),
                ("comment", models.TextField()),
                ("page_number", models.IntegerField(blank=True, null=True)),
                ("x_position", models.FloatField(blank=True, null=True)),
                ("y_position", models.FloatField(blank=True, null=True)),
                ("is_resolved", models.BooleanField(default=False)),
                ("resolved_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "author",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "resolved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resolved_comments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "working_paper",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="comments",
                        to="working_papers.workingpaper",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="WorkingPaperFolder",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                (
                    "folder_type",
                    models.CharField(
                        choices=[
                            ("PLANNING", "Planning"),
                            ("RISK_ASSESSMENT", "Risk Assessment"),
                            ("INTERNAL_CONTROLS", "Internal Controls"),
                            ("SUBSTANTIVE_TESTING", "Substantive Testing"),
                            ("COMPLETION", "Completion"),
                            ("REPORTING", "Reporting"),
                            ("CORRESPONDENCE", "Correspondence"),
                            ("MANAGEMENT_LETTER", "Management Letter"),
                            ("CUSTOM", "Custom"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField(blank=True, null=True)),
                ("folder_code", models.CharField(max_length=20)),
                ("sequence_number", models.IntegerField(default=1)),
                ("is_locked", models.BooleanField(default=False)),
                ("locked_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_folders",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "engagement",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="working_paper_folders",
                        to="audit_engagements.auditengagement",
                    ),
                ),
                (
                    "locked_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="locked_folders",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "parent_folder",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subfolders",
                        to="working_papers.workingpaperfolder",
                    ),
                ),
            ],
            options={
                "ordering": ["folder_code", "sequence_number", "name"],
                "unique_together": {("engagement", "folder_code", "sequence_number")},
            },
        ),
        migrations.AddField(
            model_name="workingpaper",
            name="folder",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="working_papers",
                to="working_papers.workingpaperfolder",
            ),
        ),
        migrations.CreateModel(
            name="WorkingPaperTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                (
                    "paper_type",
                    models.CharField(
                        choices=[
                            ("LEAD_SCHEDULE", "Lead Schedule"),
                            ("SUPPORTING_SCHEDULE", "Supporting Schedule"),
                            ("ANALYTICAL_REVIEW", "Analytical Review"),
                            ("TEST_OF_CONTROLS", "Test of Controls"),
                            ("SUBSTANTIVE_TEST", "Substantive Test"),
                            ("CONFIRMATION", "Confirmation"),
                            ("MEMO", "Memo"),
                            ("CHECKLIST", "Checklist"),
                            ("SAMPLING_SHEET", "Sampling Sheet"),
                            ("RECONCILIATION", "Reconciliation"),
                            ("JOURNAL_ENTRY", "Journal Entry"),
                            ("OTHER", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                ("description", models.TextField()),
                ("template_content", models.TextField()),
                (
                    "file_template",
                    models.FileField(blank=True, null=True, upload_to="templates/"),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="CrossReference",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "reference_type",
                    models.CharField(
                        choices=[
                            ("SUPPORTS", "Supports"),
                            ("SUPPORTED_BY", "Supported By"),
                            ("RELATES_TO", "Relates To"),
                            ("CONTRADICTS", "Contradicts"),
                            ("SUPERSEDES", "Supersedes"),
                            ("SUPERSEDED_BY", "Superseded By"),
                        ],
                        max_length=15,
                    ),
                ),
                ("description", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "source_paper",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="outgoing_references",
                        to="working_papers.workingpaper",
                    ),
                ),
                (
                    "target_paper",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="incoming_references",
                        to="working_papers.workingpaper",
                    ),
                ),
            ],
            options={
                "unique_together": {("source_paper", "target_paper", "reference_type")},
            },
        ),
        migrations.AddIndex(
            model_name="workingpaper",
            index=models.Index(
                fields=["reference_number"], name="working_pap_referen_e5f37b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="workingpaper",
            index=models.Index(fields=["status"], name="working_pap_status_d91e6a_idx"),
        ),
        migrations.AddIndex(
            model_name="workingpaper",
            index=models.Index(
                fields=["paper_type"], name="working_pap_paper_t_e6ebdd_idx"
            ),
        ),
    ]
