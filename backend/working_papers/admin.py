"""admin.py - Working Papers admin configuration"""

from django.contrib import admin
from .models import (
    WorkingPaperFolder, WorkingPaper, WorkingPaperComment,
    SamplingSheet, CrossReference, WorkingPaperTemplate
)

@admin.register(WorkingPaperFolder)
class WorkingPaperFolderAdmin(admin.ModelAdmin):
    list_display = ('name', 'engagement', 'folder_type', 'folder_code', 'sequence_number', 'is_locked', 'created_at')
    list_filter = ('folder_type', 'is_locked', 'created_at')
    search_fields = ('name', 'engagement__engagement_number', 'folder_code')
    readonly_fields = ('created_at', 'updated_at')
    raw_id_fields = ('engagement', 'parent_folder', 'locked_by', 'created_by')

@admin.register(WorkingPaper)
class WorkingPaperAdmin(admin.ModelAdmin):
    list_display = ('reference_number', 'title', 'folder', 'paper_type', 'status', 'prepared_by', 'prepared_date')
    list_filter = ('paper_type', 'status', 'prepared_date', 'is_current_version')
    search_fields = ('reference_number', 'title', 'description')
    readonly_fields = ('prepared_date', 'reviewed_date', 'approved_date', 'created_at', 'updated_at')
    raw_id_fields = ('folder', 'prepared_by', 'reviewed_by', 'approved_by', 'parent_version')
    filter_horizontal = ('related_papers',)
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('folder', 'reference_number', 'title', 'paper_type', 'description')
        }),
        ('Content', {
            'fields': ('content', 'file')
        }),
        ('Audit Trail', {
            'fields': ('prepared_by', 'reviewed_by', 'approved_by', 'status')
        }),
        ('Dates', {
            'fields': ('prepared_date', 'reviewed_date', 'approved_date')
        }),
        ('Version Control', {
            'fields': ('version', 'is_current_version', 'parent_version')
        }),
        ('Cross References', {
            'fields': ('related_papers',)
        }),
    )

@admin.register(WorkingPaperComment)
class WorkingPaperCommentAdmin(admin.ModelAdmin):
    list_display = ('working_paper', 'comment_type', 'author', 'is_resolved', 'created_at')
    list_filter = ('comment_type', 'is_resolved', 'created_at')
    search_fields = ('working_paper__reference_number', 'comment', 'author__username')
    readonly_fields = ('created_at', 'updated_at')
    raw_id_fields = ('working_paper', 'author', 'resolved_by')

@admin.register(SamplingSheet)
class SamplingSheetAdmin(admin.ModelAdmin):
    list_display = ('working_paper', 'sampling_method', 'population_size', 'sample_size', 'errors_found')
    list_filter = ('sampling_method', 'created_at')
    search_fields = ('working_paper__reference_number', 'population_description')
    readonly_fields = ('created_at', 'updated_at')
    raw_id_fields = ('working_paper',)
    
    fieldsets = (
        ('Working Paper', {
            'fields': ('working_paper',)
        }),
        ('Population', {
            'fields': ('population_description', 'population_size', 'population_value')
        }),
        ('Sampling Parameters', {
            'fields': ('sampling_method', 'sample_size', 'confidence_level', 'tolerable_error', 'expected_error')
        }),
        ('Results', {
            'fields': ('errors_found', 'error_value', 'projected_error', 'conclusion')
        }),
    )

@admin.register(CrossReference)
class CrossReferenceAdmin(admin.ModelAdmin):
    list_display = ('source_paper', 'reference_type', 'target_paper', 'created_by', 'created_at')
    list_filter = ('reference_type', 'created_at')
    search_fields = ('source_paper__reference_number', 'target_paper__reference_number', 'description')
    readonly_fields = ('created_at',)
    raw_id_fields = ('source_paper', 'target_paper', 'created_by')

@admin.register(WorkingPaperTemplate)
class WorkingPaperTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'paper_type', 'is_active', 'created_by', 'created_at')
    list_filter = ('paper_type', 'is_active', 'created_at')
    search_fields = ('name', 'description')
    readonly_fields = ('created_at', 'updated_at')
    raw_id_fields = ('created_by',)
