"""urls.py - Working Papers API routing"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    WorkingPaperFolderViewSet,
    WorkingPaperViewSet,
    WorkingPaperCommentViewSet,
    SamplingSheetViewSet,
    WorkingPaperTemplateViewSet
)

router = DefaultRouter()
router.register(r'folders', WorkingPaperFolderViewSet)
router.register(r'papers', WorkingPaperViewSet)
router.register(r'comments', WorkingPaperCommentViewSet)
router.register(r'sampling-sheets', SamplingSheetViewSet)
router.register(r'templates', WorkingPaperTemplateViewSet)

urlpatterns = [
    path('', include(router.urls)),
]
