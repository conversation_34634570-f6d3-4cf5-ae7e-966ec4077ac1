"""serializers.py - Working Papers API serializers"""

from rest_framework import serializers
from .models import (
    WorkingPaperFolder, WorkingPaper, WorkingPaperComment,
    SamplingSheet, CrossReference, WorkingPaperTemplate
)
from users.models import User

class UserSummarySerializer(serializers.ModelSerializer):
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'full_name']

class WorkingPaperFolderSerializer(serializers.ModelSerializer):
    created_by = UserSummarySerializer(read_only=True)
    locked_by = UserSummarySerializer(read_only=True)
    full_path = serializers.CharField(read_only=True)
    subfolders = serializers.SerializerMethodField()
    
    class Meta:
        model = WorkingPaperFolder
        fields = [
            'id', 'name', 'folder_type', 'folder_code', 'sequence_number',
            'description', 'parent_folder', 'full_path', 'is_locked',
            'locked_by', 'locked_at', 'created_by', 'created_at',
            'updated_at', 'subfolders'
        ]
        read_only_fields = ['created_at', 'updated_at', 'locked_at']
    
    def get_subfolders(self, obj):
        subfolders = obj.subfolders.all()
        return WorkingPaperFolderSerializer(subfolders, many=True).data

class WorkingPaperCommentSerializer(serializers.ModelSerializer):
    author = UserSummarySerializer(read_only=True)
    resolved_by = UserSummarySerializer(read_only=True)
    
    class Meta:
        model = WorkingPaperComment
        fields = [
            'id', 'comment_type', 'comment', 'page_number', 'x_position',
            'y_position', 'author', 'is_resolved', 'resolved_by',
            'resolved_at', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'resolved_at']

class SamplingSheetSerializer(serializers.ModelSerializer):
    class Meta:
        model = SamplingSheet
        fields = [
            'id', 'population_description', 'population_size', 'population_value',
            'sampling_method', 'sample_size', 'confidence_level', 'tolerable_error',
            'expected_error', 'errors_found', 'error_value', 'projected_error',
            'conclusion', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class CrossReferenceSerializer(serializers.ModelSerializer):
    source_paper = serializers.StringRelatedField(read_only=True)
    target_paper = serializers.StringRelatedField(read_only=True)
    created_by = UserSummarySerializer(read_only=True)
    
    class Meta:
        model = CrossReference
        fields = [
            'id', 'source_paper', 'target_paper', 'reference_type',
            'description', 'created_by', 'created_at'
        ]
        read_only_fields = ['created_at']

class WorkingPaperSerializer(serializers.ModelSerializer):
    folder = WorkingPaperFolderSerializer(read_only=True)
    folder_id = serializers.IntegerField(write_only=True)
    
    prepared_by = UserSummarySerializer(read_only=True)
    reviewed_by = UserSummarySerializer(read_only=True)
    approved_by = UserSummarySerializer(read_only=True)
    
    comments = WorkingPaperCommentSerializer(many=True, read_only=True)
    sampling_sheet = SamplingSheetSerializer(read_only=True)
    outgoing_references = CrossReferenceSerializer(many=True, read_only=True)
    incoming_references = CrossReferenceSerializer(many=True, read_only=True)
    
    class Meta:
        model = WorkingPaper
        fields = [
            'id', 'folder', 'folder_id', 'reference_number', 'title',
            'paper_type', 'description', 'content', 'file', 'prepared_by',
            'reviewed_by', 'approved_by', 'status', 'prepared_date',
            'reviewed_date', 'approved_date', 'version', 'is_current_version',
            'parent_version', 'created_at', 'updated_at', 'comments',
            'sampling_sheet', 'outgoing_references', 'incoming_references'
        ]
        read_only_fields = [
            'prepared_date', 'reviewed_date', 'approved_date',
            'created_at', 'updated_at'
        ]
    
    def create(self, validated_data):
        # Auto-generate reference number if not provided
        working_paper = WorkingPaper(**validated_data)
        if not working_paper.reference_number:
            working_paper.reference_number = working_paper.generate_reference_number()
        working_paper.prepared_by = self.context['request'].user
        working_paper.save()
        return working_paper

class WorkingPaperSummarySerializer(serializers.ModelSerializer):
    """Lightweight serializer for working paper listings"""
    folder_name = serializers.CharField(source='folder.name', read_only=True)
    prepared_by_name = serializers.CharField(source='prepared_by.get_full_name', read_only=True)
    
    class Meta:
        model = WorkingPaper
        fields = [
            'id', 'reference_number', 'title', 'paper_type', 'status',
            'folder_name', 'prepared_by_name', 'prepared_date', 'version'
        ]

class WorkingPaperTemplateSerializer(serializers.ModelSerializer):
    created_by = UserSummarySerializer(read_only=True)
    
    class Meta:
        model = WorkingPaperTemplate
        fields = [
            'id', 'name', 'paper_type', 'description', 'template_content',
            'file_template', 'is_active', 'created_by', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']
