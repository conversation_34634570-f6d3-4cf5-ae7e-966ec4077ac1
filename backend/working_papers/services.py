"""services.py - Working Papers automation services"""

from django.utils import timezone
from audit_engagements.models import AuditEngagement
from .models import WorkingPaperFolder, WorkingPaper, WorkingPaperTemplate, SamplingSheet
import json

def create_default_folder_structure(engagement: AuditEngagement) -> list:
    """
    Create default folder structure for an audit engagement.
    
    Args:
        engagement (AuditEngagement): The audit engagement
        
    Returns:
        list: List of created folders
    """
    default_folders = [
        {
            'name': 'Planning and Risk Assessment',
            'folder_type': 'PLANNING',
            'folder_code': 'P',
            'sequence_number': 1,
            'description': 'Planning documents, risk assessment, and materiality calculations'
        },
        {
            'name': 'Understanding the Entity',
            'folder_type': 'PLANNING',
            'folder_code': 'P',
            'sequence_number': 2,
            'description': 'Client understanding, industry analysis, and business processes'
        },
        {
            'name': 'Risk Assessment',
            'folder_type': 'RISK_ASSESSMENT',
            'folder_code': 'R',
            'sequence_number': 1,
            'description': 'Risk identification, assessment, and response'
        },
        {
            'name': 'Internal Controls Evaluation',
            'folder_type': 'INTERNAL_CONTROLS',
            'folder_code': 'IC',
            'sequence_number': 1,
            'description': 'Internal controls testing and evaluation'
        },
        {
            'name': 'Revenue and Receivables',
            'folder_type': 'SUBSTANTIVE_TESTING',
            'folder_code': 'S',
            'sequence_number': 1,
            'description': 'Revenue recognition and accounts receivable testing'
        },
        {
            'name': 'Purchases and Payables',
            'folder_type': 'SUBSTANTIVE_TESTING',
            'folder_code': 'S',
            'sequence_number': 2,
            'description': 'Purchase transactions and accounts payable testing'
        },
        {
            'name': 'Inventory',
            'folder_type': 'SUBSTANTIVE_TESTING',
            'folder_code': 'S',
            'sequence_number': 3,
            'description': 'Inventory existence, valuation, and cutoff testing'
        },
        {
            'name': 'Fixed Assets',
            'folder_type': 'SUBSTANTIVE_TESTING',
            'folder_code': 'S',
            'sequence_number': 4,
            'description': 'Fixed assets existence, valuation, and depreciation'
        },
        {
            'name': 'Cash and Bank',
            'folder_type': 'SUBSTANTIVE_TESTING',
            'folder_code': 'S',
            'sequence_number': 5,
            'description': 'Cash and bank reconciliations and confirmations'
        },
        {
            'name': 'Completion and Review',
            'folder_type': 'COMPLETION',
            'folder_code': 'C',
            'sequence_number': 1,
            'description': 'Completion procedures, subsequent events, and final review'
        },
        {
            'name': 'Audit Reports',
            'folder_type': 'REPORTING',
            'folder_code': 'REP',
            'sequence_number': 1,
            'description': 'Audit reports, management letters, and communications'
        },
        {
            'name': 'Correspondence',
            'folder_type': 'CORRESPONDENCE',
            'folder_code': 'CORR',
            'sequence_number': 1,
            'description': 'Client correspondence and communications'
        }
    ]
    
    created_folders = []
    for folder_data in default_folders:
        folder = WorkingPaperFolder.objects.create(
            engagement=engagement,
            **folder_data
        )
        created_folders.append(folder)
    
    return created_folders

def generate_standard_working_papers(folder: WorkingPaperFolder) -> list:
    """
    Generate standard working papers for a folder based on its type.
    
    Args:
        folder (WorkingPaperFolder): The folder to generate papers for
        
    Returns:
        list: List of created working papers
    """
    paper_templates = get_standard_paper_templates(folder.folder_type)
    created_papers = []
    
    for template in paper_templates:
        paper = WorkingPaper.objects.create(
            folder=folder,
            title=template['title'],
            paper_type=template['paper_type'],
            description=template['description'],
            content=template.get('content', ''),
            status='DRAFT'
        )
        paper.reference_number = paper.generate_reference_number()
        paper.save()
        created_papers.append(paper)
    
    return created_papers

def get_standard_paper_templates(folder_type: str) -> list:
    """
    Get standard working paper templates for a folder type.
    
    Args:
        folder_type (str): The type of folder
        
    Returns:
        list: List of paper templates
    """
    templates = {
        'PLANNING': [
            {
                'title': 'Engagement Planning Memorandum',
                'paper_type': 'MEMO',
                'description': 'Overall engagement planning and strategy',
                'content': json.dumps({
                    'sections': [
                        'Client Background',
                        'Engagement Objectives',
                        'Risk Assessment Summary',
                        'Materiality Calculations',
                        'Audit Strategy',
                        'Team Assignment',
                        'Timeline'
                    ]
                })
            },
            {
                'title': 'Materiality Calculation',
                'paper_type': 'ANALYTICAL_REVIEW',
                'description': 'Calculation of overall and performance materiality',
                'content': json.dumps({
                    'fields': [
                        'Base Amount',
                        'Percentage Applied',
                        'Overall Materiality',
                        'Performance Materiality',
                        'Trivial Threshold'
                    ]
                })
            }
        ],
        'RISK_ASSESSMENT': [
            {
                'title': 'Risk Assessment Matrix',
                'paper_type': 'ANALYTICAL_REVIEW',
                'description': 'Identification and assessment of audit risks',
                'content': json.dumps({
                    'columns': [
                        'Risk Area',
                        'Risk Description',
                        'Likelihood',
                        'Impact',
                        'Overall Risk',
                        'Audit Response'
                    ]
                })
            }
        ],
        'INTERNAL_CONTROLS': [
            {
                'title': 'Internal Controls Evaluation',
                'paper_type': 'TEST_OF_CONTROLS',
                'description': 'Evaluation of internal control systems',
                'content': json.dumps({
                    'sections': [
                        'Control Environment',
                        'Risk Assessment Process',
                        'Control Activities',
                        'Information & Communication',
                        'Monitoring Activities'
                    ]
                })
            }
        ],
        'SUBSTANTIVE_TESTING': [
            {
                'title': 'Lead Schedule',
                'paper_type': 'LEAD_SCHEDULE',
                'description': 'Summary of account balances and testing',
                'content': json.dumps({
                    'columns': [
                        'Account',
                        'Prior Year',
                        'Current Year',
                        'Variance',
                        'Audit Adjustments',
                        'Final Balance'
                    ]
                })
            },
            {
                'title': 'Analytical Review',
                'paper_type': 'ANALYTICAL_REVIEW',
                'description': 'Analytical procedures and variance analysis',
                'content': json.dumps({
                    'sections': [
                        'Expectation Development',
                        'Comparison with Expectation',
                        'Variance Analysis',
                        'Investigation Results',
                        'Conclusion'
                    ]
                })
            }
        ],
        'COMPLETION': [
            {
                'title': 'Completion Checklist',
                'paper_type': 'CHECKLIST',
                'description': 'Final completion procedures checklist',
                'content': json.dumps({
                    'items': [
                        'Subsequent events review',
                        'Going concern assessment',
                        'Related party transactions',
                        'Commitments and contingencies',
                        'Final analytical review',
                        'Management representations'
                    ]
                })
            }
        ],
        'REPORTING': [
            {
                'title': 'Audit Report Draft',
                'paper_type': 'MEMO',
                'description': 'Draft audit report and key audit matters',
                'content': json.dumps({
                    'sections': [
                        'Opinion',
                        'Basis for Opinion',
                        'Key Audit Matters',
                        'Other Information',
                        'Management Responsibilities',
                        'Auditor Responsibilities'
                    ]
                })
            }
        ]
    }
    
    return templates.get(folder_type, [])

def create_sampling_sheet(working_paper: WorkingPaper, population_data: dict) -> SamplingSheet:
    """
    Create a sampling sheet for a working paper.
    
    Args:
        working_paper (WorkingPaper): The working paper
        population_data (dict): Population information
        
    Returns:
        SamplingSheet: Created sampling sheet
    """
    sampling_sheet = SamplingSheet.objects.create(
        working_paper=working_paper,
        population_description=population_data.get('description', ''),
        population_size=population_data.get('size', 0),
        population_value=population_data.get('value', 0),
        sampling_method=population_data.get('method', 'RANDOM'),
        sample_size=calculate_sample_size(
            population_data.get('size', 0),
            population_data.get('confidence_level', 95),
            population_data.get('tolerable_error', 0)
        ),
        confidence_level=population_data.get('confidence_level', 95),
        tolerable_error=population_data.get('tolerable_error', 0),
        expected_error=population_data.get('expected_error', 0)
    )
    
    return sampling_sheet

def calculate_sample_size(population_size: int, confidence_level: float, tolerable_error: float) -> int:
    """
    Calculate statistical sample size.
    
    Args:
        population_size (int): Size of the population
        confidence_level (float): Confidence level (e.g., 95)
        tolerable_error (float): Tolerable error rate
        
    Returns:
        int: Calculated sample size
    """
    # Simplified sample size calculation
    # In practice, use proper statistical formulas
    
    if population_size <= 50:
        return min(population_size, 10)
    elif population_size <= 100:
        return min(population_size, 20)
    elif population_size <= 500:
        return min(population_size, 50)
    elif population_size <= 1000:
        return min(population_size, 75)
    else:
        return min(population_size, 100)

def auto_generate_reference_numbers(engagement: AuditEngagement):
    """
    Auto-generate reference numbers for all working papers in an engagement.
    
    Args:
        engagement (AuditEngagement): The audit engagement
    """
    folders = WorkingPaperFolder.objects.filter(engagement=engagement).order_by('folder_code', 'sequence_number')
    
    for folder in folders:
        papers = folder.working_papers.filter(is_current_version=True).order_by('created_at')
        
        for index, paper in enumerate(papers, 1):
            if not paper.reference_number:
                paper.reference_number = f"{folder.folder_code}.{index:03d}"
                paper.save()

def create_cross_references(source_paper: WorkingPaper, target_papers: list, reference_type: str, user):
    """
    Create cross-references between working papers.
    
    Args:
        source_paper (WorkingPaper): Source working paper
        target_papers (list): List of target working papers
        reference_type (str): Type of reference
        user: User creating the references
    """
    from .models import CrossReference
    
    for target_paper in target_papers:
        CrossReference.objects.get_or_create(
            source_paper=source_paper,
            target_paper=target_paper,
            reference_type=reference_type,
            defaults={'created_by': user}
        )
