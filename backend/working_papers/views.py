"""views.py - Working Papers API views"""

from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count
from django.utils import timezone
from .models import (
    WorkingPaperFolder, WorkingPaper, WorkingPaperComment,
    SamplingSheet, CrossReference, WorkingPaperTemplate
)
from .serializers import (
    WorkingPaperFolderSerializer, WorkingPaperSerializer, WorkingPaperSummarySerializer,
    WorkingPaperCommentSerializer, SamplingSheetSerializer, CrossReferenceSerializer,
    WorkingPaperTemplateSerializer
)

class WorkingPaperFolderViewSet(viewsets.ModelViewSet):
    queryset = WorkingPaperFolder.objects.all()
    serializer_class = WorkingPaperFolderSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['engagement', 'folder_type', 'is_locked']
    search_fields = ['name', 'description', 'folder_code']
    ordering_fields = ['folder_code', 'sequence_number', 'name', 'created_at']
    ordering = ['folder_code', 'sequence_number']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    @action(detail=True, methods=['post'])
    def lock_folder(self, request, pk=None):
        """Lock a folder to prevent modifications"""
        folder = self.get_object()
        if folder.is_locked:
            return Response({'error': 'Folder is already locked'}, status=status.HTTP_400_BAD_REQUEST)
        
        folder.is_locked = True
        folder.locked_by = request.user
        folder.locked_at = timezone.now()
        folder.save()
        
        return Response({'status': 'Folder locked successfully'})
    
    @action(detail=True, methods=['post'])
    def unlock_folder(self, request, pk=None):
        """Unlock a folder"""
        folder = self.get_object()
        if not folder.is_locked:
            return Response({'error': 'Folder is not locked'}, status=status.HTTP_400_BAD_REQUEST)
        
        # Only the user who locked it or a supervisor can unlock
        if folder.locked_by != request.user and not request.user.is_staff:
            return Response({'error': 'You do not have permission to unlock this folder'}, 
                          status=status.HTTP_403_FORBIDDEN)
        
        folder.is_locked = False
        folder.locked_by = None
        folder.locked_at = None
        folder.save()
        
        return Response({'status': 'Folder unlocked successfully'})
    
    @action(detail=True, methods=['get'])
    def folder_structure(self, request, pk=None):
        """Get the complete folder structure for an engagement"""
        folder = self.get_object()
        engagement = folder.engagement
        
        # Get all folders for this engagement
        folders = WorkingPaperFolder.objects.filter(engagement=engagement).order_by('folder_code', 'sequence_number')
        serializer = WorkingPaperFolderSerializer(folders, many=True)
        
        return Response({
            'engagement': engagement.engagement_number,
            'folders': serializer.data
        })

class WorkingPaperViewSet(viewsets.ModelViewSet):
    queryset = WorkingPaper.objects.all()
    serializer_class = WorkingPaperSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['folder', 'paper_type', 'status', 'prepared_by', 'is_current_version']
    search_fields = ['reference_number', 'title', 'description']
    ordering_fields = ['reference_number', 'title', 'prepared_date', 'status']
    ordering = ['reference_number']
    
    def get_serializer_class(self):
        if self.action == 'list':
            return WorkingPaperSummarySerializer
        return WorkingPaperSerializer
    
    def get_queryset(self):
        queryset = WorkingPaper.objects.select_related('folder', 'prepared_by', 'reviewed_by', 'approved_by')
        
        # Filter by engagement if specified
        engagement_id = self.request.query_params.get('engagement')
        if engagement_id:
            queryset = queryset.filter(folder__engagement_id=engagement_id)
        
        # Filter by current versions only if specified
        if self.request.query_params.get('current_only') == 'true':
            queryset = queryset.filter(is_current_version=True)
        
        return queryset
    
    @action(detail=True, methods=['post'])
    def submit_for_review(self, request, pk=None):
        """Submit working paper for review"""
        paper = self.get_object()
        if paper.status != 'DRAFT':
            return Response({'error': 'Only draft papers can be submitted for review'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        paper.status = 'IN_REVIEW'
        paper.save()
        
        return Response({'status': 'Paper submitted for review'})
    
    @action(detail=True, methods=['post'])
    def approve_paper(self, request, pk=None):
        """Approve a working paper"""
        paper = self.get_object()
        if paper.status != 'REVIEWED':
            return Response({'error': 'Only reviewed papers can be approved'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        paper.status = 'APPROVED'
        paper.approved_by = request.user
        paper.approved_date = timezone.now()
        paper.save()
        
        return Response({'status': 'Paper approved'})
    
    @action(detail=True, methods=['post'])
    def reject_paper(self, request, pk=None):
        """Reject a working paper"""
        paper = self.get_object()
        if paper.status not in ['IN_REVIEW', 'REVIEWED']:
            return Response({'error': 'Only papers in review can be rejected'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        paper.status = 'REJECTED'
        paper.save()
        
        return Response({'status': 'Paper rejected'})
    
    @action(detail=True, methods=['post'])
    def create_new_version(self, request, pk=None):
        """Create a new version of a working paper"""
        original_paper = self.get_object()
        
        # Mark original as not current
        original_paper.is_current_version = False
        original_paper.save()
        
        # Create new version
        new_paper = WorkingPaper.objects.create(
            folder=original_paper.folder,
            reference_number=original_paper.reference_number,
            title=original_paper.title,
            paper_type=original_paper.paper_type,
            description=original_paper.description,
            content=original_paper.content,
            prepared_by=request.user,
            version=original_paper.version + 1,
            parent_version=original_paper,
            status='DRAFT'
        )
        
        serializer = WorkingPaperSerializer(new_paper)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    
    @action(detail=True, methods=['get'])
    def version_history(self, request, pk=None):
        """Get version history of a working paper"""
        paper = self.get_object()
        
        # Get all versions of this paper
        versions = WorkingPaper.objects.filter(
            Q(reference_number=paper.reference_number) |
            Q(parent_version=paper) |
            Q(id=paper.id)
        ).order_by('-version')
        
        serializer = WorkingPaperSummarySerializer(versions, many=True)
        return Response(serializer.data)

class WorkingPaperCommentViewSet(viewsets.ModelViewSet):
    queryset = WorkingPaperComment.objects.all()
    serializer_class = WorkingPaperCommentSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['working_paper', 'comment_type', 'is_resolved', 'author']
    ordering = ['-created_at']
    
    def perform_create(self, serializer):
        serializer.save(author=self.request.user)
    
    @action(detail=True, methods=['post'])
    def resolve_comment(self, request, pk=None):
        """Mark a comment as resolved"""
        comment = self.get_object()
        comment.is_resolved = True
        comment.resolved_by = request.user
        comment.resolved_at = timezone.now()
        comment.save()
        
        return Response({'status': 'Comment resolved'})

class SamplingSheetViewSet(viewsets.ModelViewSet):
    queryset = SamplingSheet.objects.all()
    serializer_class = SamplingSheetSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['working_paper', 'sampling_method']
    ordering = ['-created_at']
    
    @action(detail=True, methods=['post'])
    def calculate_sample_size(self, request, pk=None):
        """Calculate recommended sample size based on parameters"""
        sampling_sheet = self.get_object()
        
        # Basic sample size calculation (simplified)
        population_size = sampling_sheet.population_size
        confidence_level = float(sampling_sheet.confidence_level)
        
        # Simplified calculation - in practice, use statistical formulas
        if population_size <= 100:
            recommended_size = min(population_size, 25)
        elif population_size <= 1000:
            recommended_size = min(population_size, 50)
        else:
            recommended_size = min(population_size, 100)
        
        return Response({
            'recommended_sample_size': recommended_size,
            'current_sample_size': sampling_sheet.sample_size,
            'population_size': population_size
        })

class WorkingPaperTemplateViewSet(viewsets.ModelViewSet):
    queryset = WorkingPaperTemplate.objects.filter(is_active=True)
    serializer_class = WorkingPaperTemplateSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['paper_type', 'is_active']
    search_fields = ['name', 'description']
    ordering = ['name']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    @action(detail=True, methods=['post'])
    def create_from_template(self, request, pk=None):
        """Create a new working paper from a template"""
        template = self.get_object()
        folder_id = request.data.get('folder_id')
        
        if not folder_id:
            return Response({'error': 'folder_id is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            folder = WorkingPaperFolder.objects.get(id=folder_id)
        except WorkingPaperFolder.DoesNotExist:
            return Response({'error': 'Folder not found'}, status=status.HTTP_404_NOT_FOUND)
        
        # Create working paper from template
        working_paper = WorkingPaper.objects.create(
            folder=folder,
            title=template.name,
            paper_type=template.paper_type,
            description=template.description,
            content=template.template_content,
            prepared_by=request.user
        )
        working_paper.reference_number = working_paper.generate_reference_number()
        working_paper.save()
        
        serializer = WorkingPaperSerializer(working_paper)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
