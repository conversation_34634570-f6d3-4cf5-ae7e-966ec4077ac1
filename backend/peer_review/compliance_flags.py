"""compliance_flags.py - Auto-detection logic for common audit compliance issues"""

def detect_compliance_flags(audit_data: dict) -> list:
    """
    Scan audit data and return a list of compliance flags.

    Args:
        audit_data (dict): Dictionary of key audit metrics

    Returns:
        list[dict]: List of compliance issues with severity and description
    """
    flags = []

    if audit_data.get("tds_mismatch", 0) > 1000:
        flags.append({
            "clause": "Clause 26",
            "flag_type": "HIGH",
            "description": "TDS mismatch exceeds ₹1,000"
        })

    if not audit_data.get("internal_audit_done", True):
        flags.append({
            "clause": "CARO xiv",
            "flag_type": "MEDIUM",
            "description": "Internal audit system not present"
        })

    if audit_data.get("gst_mismatch_count", 0) > 5:
        flags.append({
            "clause": "Clause 44",
            "flag_type": "HIGH",
            "description": "Multiple GST mismatches found in GSTR-2B"
        })

    return flags
