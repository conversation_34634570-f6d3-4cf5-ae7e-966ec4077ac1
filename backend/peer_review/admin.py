# backend/peer_review/admin.py

from django.contrib import admin
from .models import PeerReviewNote, ComplianceFlag

@admin.register(PeerReviewNote)
class PeerReviewNoteAdmin(admin.ModelAdmin):
    list_display = ('reviewer', 'audit', 'created_at')
    search_fields = ('note',)
    list_filter = ('created_at', 'reviewer')

@admin.register(ComplianceFlag)
class ComplianceFlagAdmin(admin.ModelAdmin):
    list_display = ('audit', 'clause', 'flag_type', 'resolved', 'flagged_at')
    search_fields = ('clause', 'description')
    list_filter = ('flag_type', 'resolved', 'flagged_at')
