"""reviewer_logs.py - Save peer review logs and actions"""

from .models import ComplianceFlag, PeerReviewNote

def save_flags(audit, reviewer, flags: list):
    """
    Save detected compliance flags into the database.

    Args:
        audit (Audit): Related Audit object
        reviewer (User): Reviewer performing the action
        flags (list): List of flag dicts
    """
    for flag in flags:
        ComplianceFlag.objects.create(
            audit=audit,
            clause=flag["clause"],
            flag_type=flag["flag_type"],
            description=flag["description"],
        )
        PeerReviewNote.objects.create(
            audit=audit,
            reviewer=reviewer,
            note=f"Flag raised for {flag['clause']}: {flag['description']}"
        )
