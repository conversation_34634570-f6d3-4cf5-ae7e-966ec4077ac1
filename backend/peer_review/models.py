"""models.py - Peer review models for audit compliance and feedback"""

from django.db import models
from audit.models import Audit
from users.models import User  # assuming you have a User model

class PeerReviewNote(models.Model):
    audit = models.ForeignKey(Audit, on_delete=models.CASCADE, related_name='peer_notes')
    reviewer = models.ForeignKey(User, on_delete=models.CASCADE)
    note = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.reviewer} on {self.audit} at {self.created_at}"

class ComplianceFlag(models.Model):
    audit = models.ForeignKey(Audit, on_delete=models.CASCADE, related_name='compliance_flags')
    clause = models.CharField(max_length=100)
    flag_type = models.CharField(max_length=50, choices=[
        ("HIGH", "High Risk"),
        ("MEDIUM", "Medium Risk"),
        ("LOW", "Low Risk")
    ])
    description = models.TextField()
    resolved = models.<PERSON><PERSON><PERSON><PERSON>ield(default=False)
    flagged_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.clause} - {self.flag_type} - Resolved: {self.resolved}"
