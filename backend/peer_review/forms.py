"""forms.py - Forms for manual peer review inputs (admin or internal panel)"""

from django import forms
from .models import PeerReviewNote, ComplianceFlag

class PeerReviewNoteForm(forms.ModelForm):
    class Meta:
        model = PeerReviewNote
        fields = ['audit', 'reviewer', 'note']

class ComplianceFlagForm(forms.ModelForm):
    class Meta:
        model = ComplianceFlag
        fields = ['audit', 'clause', 'flag_type', 'description', 'resolved']
