#!/usr/bin/env python
"""
Sample Data Generator for AuditSmartAI
This script generates comprehensive sample data for testing all system features.
"""

import os
import sys
import django
import random
from datetime import datetime, date, timedelta
from decimal import Decimal

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings.development')
django.setup()

from django.contrib.auth import get_user_model
from clients.models import Client
from audit_engagements.models import AuditEngagement, EngagementLetter, AuditTeamAssignment, EngagementChecklist
from documents.models import UploadedDocument, DocumentAnalysis, ExtractedEntity, DocumentAnomalyFlag
from working_papers.models import WorkingPaperFolder, WorkingPaper, WorkingPaperComment
from risk_assessment.models import RiskAssessment, RiskFactor, AuditPlan, AuditProcedure, MaterialityCalculation
from notifications.models import Notification

User = get_user_model()

class SampleDataGenerator:
    """Generate comprehensive sample data for the audit system"""
    
    def __init__(self):
        self.users = []
        self.clients = []
        self.engagements = []
        
    def generate_all_data(self):
        """Generate all sample data"""
        print("🎯 Generating Sample Data for AuditSmartAI...")
        
        self.create_users()
        self.create_clients()
        self.create_engagements()
        self.create_documents()
        self.create_working_papers()
        self.create_risk_assessments()
        self.create_notifications()
        
        print("✅ Sample data generation completed!")
        self.print_summary()
    
    def create_users(self):
        """Create sample users with different roles"""
        print("👥 Creating users...")
        
        users_data = [
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'first_name': 'Admin',
                'last_name': 'User',
                'password': 'admin123',
                'is_staff': True,
                'is_superuser': True
            },
            {
                'username': 'partner1',
                'email': '<EMAIL>',
                'first_name': 'Rajesh',
                'last_name': 'Sharma',
                'password': 'partner123',
                'is_reviewer': True
            },
            {
                'username': 'manager1',
                'email': '<EMAIL>',
                'first_name': 'Priya',
                'last_name': 'Patel',
                'password': 'manager123',
                'is_reviewer': True
            },
            {
                'username': 'senior1',
                'email': '<EMAIL>',
                'first_name': 'Amit',
                'last_name': 'Kumar',
                'password': 'senior123'
            },
            {
                'username': 'junior1',
                'email': '<EMAIL>',
                'first_name': 'Sneha',
                'last_name': 'Singh',
                'password': 'junior123'
            },
            {
                'username': 'assistant1',
                'email': '<EMAIL>',
                'first_name': 'Rohit',
                'last_name': 'Gupta',
                'password': 'assistant123'
            }
        ]
        
        for user_data in users_data:
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults=user_data
            )
            if created:
                user.set_password(user_data['password'])
                user.save()
            self.users.append(user)
        
        print(f"✅ Created {len(self.users)} users")
    
    def create_clients(self):
        """Create sample clients"""
        print("🏢 Creating clients...")
        
        clients_data = [
            {
                'name': 'TechCorp Solutions Pvt Ltd',
                'entity_type': 'PRIVATE_LIMITED',
                'industry': 'IT_SOFTWARE',
                'size_category': 'MEDIUM',
                'pan_number': '**********',
                'cin_number': 'U72900DL2015PTC123456',
                'gst_number': '07**********1ZM',
                'annual_turnover': Decimal('50000000'),
                'number_of_employees': 150,
                'registered_address': '123 Tech Park, Sector 62',
                'city': 'Noida',
                'state': 'Uttar Pradesh',
                'pincode': '201301'
            },
            {
                'name': 'Manufacturing Industries Ltd',
                'entity_type': 'PUBLIC_LIMITED',
                'industry': 'MANUFACTURING',
                'size_category': 'LARGE',
                'pan_number': '**********',
                'cin_number': 'L25199MH1995PLC123789',
                'gst_number': '27**********1ZN',
                'annual_turnover': Decimal('200000000'),
                'number_of_employees': 500,
                'registered_address': 'Plot 45, Industrial Area',
                'city': 'Mumbai',
                'state': 'Maharashtra',
                'pincode': '400001'
            },
            {
                'name': 'Green Energy Solutions',
                'entity_type': 'PRIVATE_LIMITED',
                'industry': 'ENERGY',
                'size_category': 'MEDIUM',
                'pan_number': '**********',
                'gst_number': '09**********1ZO',
                'annual_turnover': Decimal('75000000'),
                'number_of_employees': 200,
                'registered_address': 'Solar Complex, Phase 2',
                'city': 'Bangalore',
                'state': 'Karnataka',
                'pincode': '560001'
            },
            {
                'name': 'Retail Chain Enterprises',
                'entity_type': 'PRIVATE_LIMITED',
                'industry': 'RETAIL',
                'size_category': 'LARGE',
                'pan_number': '**********',
                'gst_number': '19**********1ZP',
                'annual_turnover': Decimal('150000000'),
                'number_of_employees': 800,
                'registered_address': 'Mall Complex, Central Avenue',
                'city': 'Chennai',
                'state': 'Tamil Nadu',
                'pincode': '600001'
            },
            {
                'name': 'Financial Services Corp',
                'entity_type': 'PRIVATE_LIMITED',
                'industry': 'FINANCE',
                'size_category': 'MEDIUM',
                'pan_number': '**********',
                'gst_number': '07**********1ZQ',
                'annual_turnover': Decimal('80000000'),
                'number_of_employees': 120,
                'registered_address': 'Financial District, Block A',
                'city': 'Hyderabad',
                'state': 'Telangana',
                'pincode': '500001'
            }
        ]
        
        for client_data in clients_data:
            client, created = Client.objects.get_or_create(
                pan_number=client_data['pan_number'],
                defaults=client_data
            )
            self.clients.append(client)
        
        print(f"✅ Created {len(self.clients)} clients")
    
    def create_engagements(self):
        """Create sample audit engagements"""
        print("📋 Creating audit engagements...")
        
        engagement_types = ['STATUTORY_AUDIT', 'TAX_AUDIT', 'CARO_AUDIT', 'INTERNAL_AUDIT', 'GST_AUDIT']
        statuses = ['PLANNING', 'FIELDWORK', 'REVIEW', 'REPORTING', 'COMPLETED']
        
        for i, client in enumerate(self.clients):
            engagement_type = engagement_types[i % len(engagement_types)]
            status = statuses[i % len(statuses)]
            
            # Create engagement
            engagement = AuditEngagement.objects.create(
                client=client,
                engagement_type=engagement_type,
                engagement_number=f"AUD-2024-{i+1:03d}",
                financial_year='2023-24',
                period_start=date(2023, 4, 1),
                period_end=date(2024, 3, 31),
                planned_start_date=date(2024, 1, 15),
                planned_end_date=date(2024, 3, 15),
                status=status,
                priority='MEDIUM',
                engagement_partner=self.users[1],  # Partner
                audit_manager=self.users[2],       # Manager
                audit_senior=self.users[3],        # Senior
                scope_of_work=f"Complete {engagement_type.lower().replace('_', ' ')} for FY 2023-24",
                materiality_amount=Decimal('500000'),
                budgeted_hours=120,
                budget_amount=Decimal('600000'),
                overall_risk_level='MEDIUM'
            )
            
            # Add team members
            engagement.team_members.add(self.users[3], self.users[4], self.users[5])
            
            # Create engagement letter
            EngagementLetter.objects.create(
                engagement=engagement,
                letter_number=f"EL-{engagement.engagement_number}",
                subject=f"Engagement Letter for {engagement_type}",
                content="This letter confirms our understanding of the terms of our engagement...",
                terms_and_conditions="Standard terms and conditions apply...",
                issue_date=date(2024, 1, 1),
                validity_date=date(2024, 12, 31),
                status='SIGNED'
            )
            
            # Create team assignments
            assignments = [
                {'user': self.users[1], 'role': 'PARTNER', 'planned_hours': 20},
                {'user': self.users[2], 'role': 'MANAGER', 'planned_hours': 40},
                {'user': self.users[3], 'role': 'SENIOR', 'planned_hours': 60},
                {'user': self.users[4], 'role': 'JUNIOR', 'planned_hours': 80},
                {'user': self.users[5], 'role': 'ASSISTANT', 'planned_hours': 40}
            ]
            
            for assignment in assignments:
                AuditTeamAssignment.objects.create(
                    engagement=engagement,
                    team_member=assignment['user'],
                    role=assignment['role'],
                    assigned_date=date(2024, 1, 10),
                    planned_hours=assignment['planned_hours'],
                    hourly_rate=Decimal('2000'),
                    responsibilities=f"{assignment['role']} responsibilities for {engagement_type}"
                )
            
            self.engagements.append(engagement)
        
        print(f"✅ Created {len(self.engagements)} engagements")
    
    def create_documents(self):
        """Create sample documents"""
        print("📄 Creating documents...")
        
        document_types = ['BANK_STATEMENT', 'GST_REPORT', 'FORM_26AS', 'INVOICE', 'BALANCE_SHEET']
        
        for i, engagement in enumerate(self.engagements):
            for j in range(3):  # 3 documents per engagement
                doc_type = document_types[j % len(document_types)]
                
                document = UploadedDocument.objects.create(
                    client=engagement.client,
                    engagement=engagement,
                    document_name=f"{doc_type}_{engagement.client.name}_{j+1}.pdf",
                    document_type=doc_type,
                    processing_status='COMPLETED',
                    confidence_score=random.uniform(0.7, 0.95),
                    extracted_text=f"Sample extracted text from {doc_type}...",
                    extracted_data={
                        'amounts': [{'value': random.randint(10000, 1000000), 'confidence': 0.9}],
                        'dates': [{'text': '2024-01-15', 'confidence': 0.85}],
                        'entities': ['Sample Entity 1', 'Sample Entity 2']
                    },
                    uploaded_by=self.users[3],
                    has_anomalies=random.choice([True, False]),
                    requires_review=random.choice([True, False])
                )
                
                # Create document analysis
                DocumentAnalysis.objects.create(
                    document=document,
                    analysis_type='OCR',
                    results={'text_confidence': 0.9, 'processing_time': 2.5},
                    confidence_score=0.9,
                    processing_time=2.5,
                    model_name='Tesseract',
                    model_version='4.0'
                )
                
                # Create extracted entities
                for k in range(2):
                    ExtractedEntity.objects.create(
                        document=document,
                        entity_type='AMOUNT',
                        entity_value=str(random.randint(10000, 100000)),
                        confidence_score=random.uniform(0.8, 0.95)
                    )
        
        print(f"✅ Created documents for all engagements")
    
    def create_working_papers(self):
        """Create sample working papers"""
        print("📝 Creating working papers...")
        
        folder_types = ['PLANNING', 'RISK_ASSESSMENT', 'SUBSTANTIVE_TESTING', 'COMPLETION']
        
        for engagement in self.engagements:
            for i, folder_type in enumerate(folder_types):
                # Create folder
                folder = WorkingPaperFolder.objects.create(
                    engagement=engagement,
                    name=f"{folder_type.title()} Folder",
                    folder_type=folder_type,
                    folder_code=folder_type[0],
                    sequence_number=i+1,
                    description=f"Working papers for {folder_type.lower().replace('_', ' ')}",
                    created_by=self.users[3]
                )
                
                # Create working papers in folder
                for j in range(2):
                    paper = WorkingPaper.objects.create(
                        folder=folder,
                        reference_number=f"{folder.folder_code}.{j+1:03d}",
                        title=f"Working Paper {j+1}",
                        paper_type='LEAD_SCHEDULE',
                        description=f"Sample working paper for {folder_type}",
                        content="Sample working paper content...",
                        prepared_by=self.users[3],
                        status='REVIEWED'
                    )
                    
                    # Add comments
                    WorkingPaperComment.objects.create(
                        working_paper=paper,
                        comment_type='NOTE',
                        comment="Sample review comment",
                        author=self.users[2]
                    )
        
        print(f"✅ Created working papers for all engagements")
    
    def create_risk_assessments(self):
        """Create sample risk assessments"""
        print("⚠️ Creating risk assessments...")
        
        for engagement in self.engagements:
            # Create risk assessment
            risk_assessment = RiskAssessment.objects.create(
                engagement=engagement,
                inherent_risk='MEDIUM',
                control_risk='MEDIUM',
                detection_risk='MEDIUM',
                overall_audit_risk='MEDIUM',
                overall_materiality=Decimal('500000'),
                performance_materiality=Decimal('375000'),
                trivial_threshold=Decimal('25000'),
                materiality_basis='REVENUE',
                assessment_date=date(2024, 1, 20),
                status='APPROVED',
                prepared_by=self.users[3],
                approved_by=self.users[1]
            )
            
            # Create risk factors
            risk_factors = [
                {
                    'category': 'FINANCIAL',
                    'factor_name': 'Revenue Recognition Risk',
                    'description': 'Risk of material misstatement in revenue recognition',
                    'likelihood': 3,
                    'impact': 4,
                    'risk_level': 'HIGH'
                },
                {
                    'category': 'OPERATIONAL',
                    'factor_name': 'Internal Controls Risk',
                    'description': 'Risk due to weak internal controls',
                    'likelihood': 2,
                    'impact': 3,
                    'risk_level': 'MEDIUM'
                }
            ]
            
            for factor_data in risk_factors:
                RiskFactor.objects.create(
                    risk_assessment=risk_assessment,
                    **factor_data,
                    existing_controls="Sample existing controls",
                    control_effectiveness='MEDIUM',
                    residual_risk='MEDIUM',
                    audit_response="Sample audit response"
                )
            
            # Create audit plan
            audit_plan = AuditPlan.objects.create(
                risk_assessment=risk_assessment,
                audit_strategy="Risk-based audit approach",
                audit_approach="Combined substantive and controls testing",
                key_audit_areas=['Revenue', 'Expenses', 'Assets'],
                budgeted_hours=120,
                team_composition={'partner': 20, 'manager': 40, 'senior': 60},
                timeline={'planning': 2, 'fieldwork': 6, 'completion': 2},
                status='APPROVED',
                approved_by=self.users[1]
            )
            
            # Create audit procedures
            procedures = [
                {
                    'procedure_code': 'REV-001',
                    'procedure_name': 'Revenue Testing',
                    'procedure_type': 'SUBSTANTIVE_DETAIL',
                    'description': 'Test revenue transactions for accuracy',
                    'expected_hours': Decimal('8.0')
                },
                {
                    'procedure_code': 'EXP-001',
                    'procedure_name': 'Expense Testing',
                    'procedure_type': 'SUBSTANTIVE_DETAIL',
                    'description': 'Test expense transactions for validity',
                    'expected_hours': Decimal('6.0')
                }
            ]
            
            for proc_data in procedures:
                AuditProcedure.objects.create(
                    audit_plan=audit_plan,
                    **proc_data,
                    addresses_risk="General audit risk",
                    assertion_tested="Accuracy, Completeness",
                    assigned_to=self.users[3],
                    status='PLANNED'
                )
        
        print(f"✅ Created risk assessments for all engagements")
    
    def create_notifications(self):
        """Create sample notifications"""
        print("🔔 Creating notifications...")
        
        notification_types = ['EMAIL', 'SMS', 'PUSH']
        
        for user in self.users[1:]:  # Skip admin
            for i in range(3):
                Notification.objects.create(
                    recipient=user,
                    notification_type=random.choice(notification_types),
                    subject=f"Sample Notification {i+1}",
                    message=f"This is a sample notification message for {user.get_full_name()}",
                    status='SENT'
                )
        
        print(f"✅ Created notifications for all users")
    
    def print_summary(self):
        """Print summary of created data"""
        print("\n📊 SAMPLE DATA SUMMARY")
        print("=" * 50)
        print(f"👥 Users: {User.objects.count()}")
        print(f"🏢 Clients: {Client.objects.count()}")
        print(f"📋 Engagements: {AuditEngagement.objects.count()}")
        print(f"📄 Documents: {UploadedDocument.objects.count()}")
        print(f"📝 Working Papers: {WorkingPaper.objects.count()}")
        print(f"⚠️ Risk Assessments: {RiskAssessment.objects.count()}")
        print(f"🔔 Notifications: {Notification.objects.count()}")
        print("=" * 50)
        print("\n🎉 Ready for testing!")
        print("\nLogin Credentials:")
        print("Admin: admin/admin123")
        print("Partner: partner1/partner123")
        print("Manager: manager1/manager123")
        print("Senior: senior1/senior123")

if __name__ == '__main__':
    generator = SampleDataGenerator()
    generator.generate_all_data()
