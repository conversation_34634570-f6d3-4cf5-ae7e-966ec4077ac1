"""models.py - Audit models for engagements and checklists"""

from django.db import models
from clients.models import Client

class Audit(models.Model):
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name="audits")
    title = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.client.name} - {self.title}"

class AuditChecklist(models.Model):
    audit = models.ForeignKey(Audit, on_delete=models.CASCADE, related_name="checklist_items")
    item = models.CharField(max_length=500)
    is_completed = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.audit} - {self.item[:50]}"
