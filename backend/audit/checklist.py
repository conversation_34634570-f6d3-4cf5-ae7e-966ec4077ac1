"""checklist.py - Logic for generating audit checklists"""

DEFAULT_CHECKLIST_ITEMS = [
    "Verify Books of Accounts",
    "Reconcile GSTR-2B with Purchase Register",
    "Match TDS deductions with Form 26AS",
    "Verify Depreciation as per IT Act",
    "Check Related Party Transactions",
    "Review CARO applicability and disclosures",
    "Validate Bank Reconciliation Statements",
    "Confirm Inventory Valuation Method",
    "Check Bonus/Dividend Payments Compliance"
]

def generate_default_checklist(audit):
    """
    Populate the default checklist items for a new audit engagement.

    Args:
        audit (Audit): Audit model instance
    """
    from .models import AuditChecklist
    checklist_objects = [
        AuditChecklist(audit=audit, item=item)
        for item in DEFAULT_CHECKLIST_ITEMS
    ]
    AuditChecklist.objects.bulk_create(checklist_objects)
