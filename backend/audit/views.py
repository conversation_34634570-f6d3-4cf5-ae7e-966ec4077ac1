"""views.py - API views for audit operations"""

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response

from .models import Audit, AuditChecklist
from .checklist import generate_default_checklist
from .report_generator import generate_audit_summary
from api.serializers import AuditSerializer  # reuse from backend/api/

class AuditViewSet(viewsets.ModelViewSet):
    queryset = Audit.objects.all()
    serializer_class = AuditSerializer

    def perform_create(self, serializer):
        audit = serializer.save()
        generate_default_checklist(audit)

    @action(detail=True, methods=["get"])
    def checklist(self, request, pk=None):
        try:
            audit = Audit.objects.get(pk=pk)
            checklist = audit.checklist_items.values("id", "item", "is_completed")
            return Response({"audit_id": pk, "checklist": checklist})
        except Audit.DoesNotExist:
            return Response({"error": "Audit not found"}, status=404)

    @action(detail=True, methods=["get"])
    def summary(self, request, pk=None):
        summary = generate_audit_summary(pk)
        if "error" in summary:
            return Response(summary, status=404)
        return Response(summary)
