"""report_generator.py - Generate simple text-based audit reports"""

from .models import <PERSON>t, AuditChecklist

def generate_audit_summary(audit_id: int) -> dict:
    """
    Generate a report summary for the given audit ID.

    Args:
        audit_id (int): ID of the Audit object

    Returns:
        dict: Summary report
    """
    try:
        audit = Audit.objects.get(id=audit_id)
        checklist_items = audit.checklist_items.all()
        total_items = checklist_items.count()
        completed = checklist_items.filter(is_completed=True).count()

        summary = {
            "audit_title": audit.title,
            "client": audit.client.name,
            "total_checkpoints": total_items,
            "completed": completed,
            "pending": total_items - completed,
            "completion_percent": round((completed / total_items) * 100, 2) if total_items else 0
        }
        return summary

    except Audit.DoesNotExist:
        return {"error": "Audit not found"}
