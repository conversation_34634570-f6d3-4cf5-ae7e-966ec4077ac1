# backend/users/admin.py

from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import User

@admin.register(User)
class UserAdmin(BaseUserAdmin):
    fieldsets = BaseUserAdmin.fieldsets + (
        ("Additional Info", {
            "fields": ("phone_number", "is_reviewer")
        }),
    )
    list_display = ("username", "email", "first_name", "last_name", "is_staff", "is_reviewer")
    list_filter = ("is_staff", "is_superuser", "is_reviewer", "is_active")
    search_fields = ("username", "email", "phone_number")
