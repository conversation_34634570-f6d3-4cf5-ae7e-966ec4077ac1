"""models.py - Enhanced Client model for ICAI compliance"""

from django.db import models
from django.core.validators import RegexValidator
from users.models import User

class Client(models.Model):
    ENTITY_TYPE_CHOICES = [
        ('PRIVATE_LIMITED', 'Private Limited Company'),
        ('PUBLIC_LIMITED', 'Public Limited Company'),
        ('LLP', 'Limited Liability Partnership'),
        ('PARTNERSHIP', 'Partnership Firm'),
        ('PROPRIETORSHIP', 'Proprietorship'),
        ('TRUST', 'Trust'),
        ('SOCIETY', 'Society'),
        ('COOPERATIVE', 'Cooperative Society'),
        ('NGO', 'Non-Governmental Organization'),
        ('GOVERNMENT', 'Government Entity'),
        ('OTHER', 'Other'),
    ]

    INDUSTRY_CHOICES = [
        ('MANUFACTURING', 'Manufacturing'),
        ('TRADING', 'Trading'),
        ('SERVICES', 'Services'),
        ('IT_SOFTWARE', 'IT & Software'),
        ('CONSTRUCTION', 'Construction'),
        ('REAL_ESTATE', 'Real Estate'),
        ('HEALTHCARE', 'Healthcare'),
        ('EDUCATION', 'Education'),
        ('FINANCE', 'Finance & Banking'),
        ('AGRICULTURE', 'Agriculture'),
        ('TEXTILE', 'Textile'),
        ('PHARMACEUTICAL', 'Pharmaceutical'),
        ('AUTOMOBILE', 'Automobile'),
        ('FOOD_BEVERAGE', 'Food & Beverage'),
        ('RETAIL', 'Retail'),
        ('LOGISTICS', 'Logistics & Transportation'),
        ('ENERGY', 'Energy & Power'),
        ('TELECOM', 'Telecommunications'),
        ('MEDIA', 'Media & Entertainment'),
        ('OTHER', 'Other'),
    ]

    SIZE_CATEGORY_CHOICES = [
        ('MICRO', 'Micro Enterprise'),
        ('SMALL', 'Small Enterprise'),
        ('MEDIUM', 'Medium Enterprise'),
        ('LARGE', 'Large Enterprise'),
    ]

    # Basic Information
    name = models.CharField(max_length=255)
    entity_type = models.CharField(max_length=20, choices=ENTITY_TYPE_CHOICES)
    industry = models.CharField(max_length=20, choices=INDUSTRY_CHOICES)
    size_category = models.CharField(max_length=10, choices=SIZE_CATEGORY_CHOICES, default='SMALL')

    # Legal Identifiers
    pan_validator = RegexValidator(regex=r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$', message='Enter a valid PAN number')
    pan_number = models.CharField(max_length=10, validators=[pan_validator], unique=True)

    cin_validator = RegexValidator(regex=r'^[LU][0-9]{5}[A-Z]{2}[0-9]{4}[A-Z]{3}[0-9]{6}$', message='Enter a valid CIN number')
    cin_number = models.CharField(max_length=21, validators=[cin_validator], blank=True, null=True)

    gst_validator = RegexValidator(regex=r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$', message='Enter a valid GST number')
    gst_number = models.CharField(max_length=15, validators=[gst_validator], blank=True, null=True)

    tan_validator = RegexValidator(regex=r'^[A-Z]{4}[0-9]{5}[A-Z]{1}$', message='Enter a valid TAN number')
    tan_number = models.CharField(max_length=10, validators=[tan_validator], blank=True, null=True)

    # Contact Information
    contact_email = models.EmailField(blank=True, null=True)
    contact_phone = models.CharField(max_length=15, blank=True, null=True)

    # Address Information
    registered_address = models.TextField()
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    pincode = models.CharField(max_length=6)

    # Business Information
    date_of_incorporation = models.DateField(blank=True, null=True)
    annual_turnover = models.DecimalField(max_digits=15, decimal_places=2, blank=True, null=True)
    number_of_employees = models.IntegerField(blank=True, null=True)

    # Audit Information
    previous_auditor = models.CharField(max_length=255, blank=True, null=True)
    audit_history_notes = models.TextField(blank=True, null=True)

    # System Fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['name']
        indexes = [
            models.Index(fields=['pan_number']),
            models.Index(fields=['gst_number']),
            models.Index(fields=['entity_type']),
            models.Index(fields=['industry']),
        ]

    def __str__(self):
        return f"{self.name} ({self.entity_type})"

    def get_display_name(self):
        return f"{self.name} - {self.pan_number}"

    @property
    def is_company(self):
        return self.entity_type in ['PRIVATE_LIMITED', 'PUBLIC_LIMITED']

    @property
    def requires_caro(self):
        """Check if CARO audit is required based on entity type and turnover"""
        if not self.is_company:
            return False
        if self.annual_turnover and self.annual_turnover >= 10000000:  # 1 Crore
            return True
        return False
