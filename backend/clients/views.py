"""views.py - Enhanced Client API views with comprehensive management"""

from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count
from .models import Client
from api.serializers import ClientSerializer, ClientSummarySerializer

class ClientViewSet(viewsets.ModelViewSet):
    queryset = Client.objects.all()
    serializer_class = ClientSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['entity_type', 'industry', 'size_category', 'is_active']
    search_fields = ['name', 'pan_number', 'gst_number', 'contact_email']
    ordering_fields = ['name', 'created_at', 'annual_turnover']
    ordering = ['name']

    def get_serializer_class(self):
        if self.action == 'list':
            return ClientSummarySerializer
        return ClientSerializer

    def get_queryset(self):
        queryset = Client.objects.all()

        # Filter by active status
        if self.action == 'list':
            queryset = queryset.filter(is_active=True)

        return queryset

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """Get client statistics"""
        stats = {
            'total_clients': Client.objects.filter(is_active=True).count(),
            'by_entity_type': dict(
                Client.objects.filter(is_active=True)
                .values_list('entity_type')
                .annotate(count=Count('id'))
            ),
            'by_industry': dict(
                Client.objects.filter(is_active=True)
                .values_list('industry')
                .annotate(count=Count('id'))
            ),
            'by_size_category': dict(
                Client.objects.filter(is_active=True)
                .values_list('size_category')
                .annotate(count=Count('id'))
            ),
            'companies_requiring_caro': Client.objects.filter(
                is_active=True,
                entity_type__in=['PRIVATE_LIMITED', 'PUBLIC_LIMITED'],
                annual_turnover__gte=10000000
            ).count()
        }
        return Response(stats)

    @action(detail=True, methods=['get'])
    def engagements(self, request, pk=None):
        """Get all engagements for a client"""
        client = self.get_object()
        engagements = client.engagements.filter(is_active=True).order_by('-created_at')

        from api.serializers import AuditEngagementSerializer
        serializer = AuditEngagementSerializer(engagements, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """Deactivate a client"""
        client = self.get_object()
        client.is_active = False
        client.save()
        return Response({'status': 'Client deactivated'})

    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """Activate a client"""
        client = self.get_object()
        client.is_active = True
        client.save()
        return Response({'status': 'Client activated'})
