# Generated by Django 5.2.4 on 2025-07-09 05:50

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("clients", "0001_initial"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="client",
            options={"ordering": ["name"]},
        ),
        migrations.AddField(
            model_name="client",
            name="annual_turnover",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=15, null=True
            ),
        ),
        migrations.AddField(
            model_name="client",
            name="audit_history_notes",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="client",
            name="cin_number",
            field=models.CharField(
                blank=True,
                max_length=21,
                null=True,
                validators=[
                    django.core.validators.RegexValidator(
                        message="Enter a valid CIN number",
                        regex="^[LU][0-9]{5}[A-Z]{2}[0-9]{4}[A-Z]{3}[0-9]{6}$",
                    )
                ],
            ),
        ),
        migrations.AddField(
            model_name="client",
            name="city",
            field=models.CharField(default="Not Provided", max_length=100),
        ),
        migrations.AddField(
            model_name="client",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, null=True),
        ),
        migrations.AddField(
            model_name="client",
            name="date_of_incorporation",
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="client",
            name="entity_type",
            field=models.CharField(
                choices=[
                    ("PRIVATE_LIMITED", "Private Limited Company"),
                    ("PUBLIC_LIMITED", "Public Limited Company"),
                    ("LLP", "Limited Liability Partnership"),
                    ("PARTNERSHIP", "Partnership Firm"),
                    ("PROPRIETORSHIP", "Proprietorship"),
                    ("TRUST", "Trust"),
                    ("SOCIETY", "Society"),
                    ("COOPERATIVE", "Cooperative Society"),
                    ("NGO", "Non-Governmental Organization"),
                    ("GOVERNMENT", "Government Entity"),
                    ("OTHER", "Other"),
                ],
                default="PRIVATE_LIMITED",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="client",
            name="industry",
            field=models.CharField(
                choices=[
                    ("MANUFACTURING", "Manufacturing"),
                    ("TRADING", "Trading"),
                    ("SERVICES", "Services"),
                    ("IT_SOFTWARE", "IT & Software"),
                    ("CONSTRUCTION", "Construction"),
                    ("REAL_ESTATE", "Real Estate"),
                    ("HEALTHCARE", "Healthcare"),
                    ("EDUCATION", "Education"),
                    ("FINANCE", "Finance & Banking"),
                    ("AGRICULTURE", "Agriculture"),
                    ("TEXTILE", "Textile"),
                    ("PHARMACEUTICAL", "Pharmaceutical"),
                    ("AUTOMOBILE", "Automobile"),
                    ("FOOD_BEVERAGE", "Food & Beverage"),
                    ("RETAIL", "Retail"),
                    ("LOGISTICS", "Logistics & Transportation"),
                    ("ENERGY", "Energy & Power"),
                    ("TELECOM", "Telecommunications"),
                    ("MEDIA", "Media & Entertainment"),
                    ("OTHER", "Other"),
                ],
                default="OTHER",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="client",
            name="is_active",
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name="client",
            name="number_of_employees",
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="client",
            name="pan_number",
            field=models.CharField(
                default="**********",
                max_length=10,
                validators=[
                    django.core.validators.RegexValidator(
                        message="Enter a valid PAN number",
                        regex="^[A-Z]{5}[0-9]{4}[A-Z]{1}$",
                    )
                ],
            ),
        ),
        migrations.AddField(
            model_name="client",
            name="pincode",
            field=models.CharField(default="000000", max_length=6),
        ),
        migrations.AddField(
            model_name="client",
            name="previous_auditor",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="client",
            name="registered_address",
            field=models.TextField(default="Not Provided"),
        ),
        migrations.AddField(
            model_name="client",
            name="size_category",
            field=models.CharField(
                choices=[
                    ("MICRO", "Micro Enterprise"),
                    ("SMALL", "Small Enterprise"),
                    ("MEDIUM", "Medium Enterprise"),
                    ("LARGE", "Large Enterprise"),
                ],
                default="SMALL",
                max_length=10,
            ),
        ),
        migrations.AddField(
            model_name="client",
            name="state",
            field=models.CharField(default="Not Provided", max_length=100),
        ),
        migrations.AddField(
            model_name="client",
            name="tan_number",
            field=models.CharField(
                blank=True,
                max_length=10,
                null=True,
                validators=[
                    django.core.validators.RegexValidator(
                        message="Enter a valid TAN number",
                        regex="^[A-Z]{4}[0-9]{5}[A-Z]{1}$",
                    )
                ],
            ),
        ),
        migrations.AddField(
            model_name="client",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, null=True),
        ),
        migrations.AlterField(
            model_name="client",
            name="gst_number",
            field=models.CharField(
                blank=True,
                max_length=15,
                null=True,
                validators=[
                    django.core.validators.RegexValidator(
                        message="Enter a valid GST number",
                        regex="^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$",
                    )
                ],
            ),
        ),
        migrations.AddIndex(
            model_name="client",
            index=models.Index(
                fields=["pan_number"], name="clients_cli_pan_num_d14f13_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="client",
            index=models.Index(
                fields=["gst_number"], name="clients_cli_gst_num_58ca2b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="client",
            index=models.Index(
                fields=["entity_type"], name="clients_cli_entity__7191c4_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="client",
            index=models.Index(
                fields=["industry"], name="clients_cli_industr_522683_idx"
            ),
        ),
    ]
