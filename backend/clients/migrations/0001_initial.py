# Generated by Django 5.2.4 on 2025-07-05 12:14

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Client",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ("gst_number", models.Char<PERSON>ield(blank=True, max_length=15, null=True)),
                (
                    "contact_email",
                    models.EmailField(blank=True, max_length=254, null=True),
                ),
                (
                    "contact_phone",
                    models.Char<PERSON>ield(blank=True, max_length=15, null=True),
                ),
            ],
        ),
    ]
