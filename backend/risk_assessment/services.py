"""services.py - Risk Assessment services"""

from typing import Dict, List, Any
from django.utils import timezone
from .models import RiskAssessment, RiskFactor, AuditPlan, AuditProcedure, MaterialityCalculation, RiskTemplate
from .ml_risk_predictor import RiskPredictor, TrialBalanceAnalyzer
from audit_engagements.models import AuditEngagement

class RiskAssessmentService:
    """Service for comprehensive risk assessment"""
    
    def __init__(self):
        self.risk_predictor = RiskPredictor()
        self.trial_balance_analyzer = TrialBalanceAnalyzer()
    
    def create_risk_assessment(self, engagement: AuditEngagement, financial_data: Dict[str, Any]) -> RiskAssessment:
        """
        Create comprehensive risk assessment for an engagement.
        
        Args:
            engagement (AuditEngagement): The audit engagement
            financial_data (Dict[str, Any]): Financial data for analysis
            
        Returns:
            RiskAssessment: Created risk assessment
        """
        # Get client data
        client_data = {
            'entity_type': engagement.client.entity_type,
            'industry': engagement.client.industry,
            'size_category': engagement.client.size_category,
            'annual_turnover': float(engagement.client.annual_turnover or 0),
            'number_of_employees': engagement.client.number_of_employees or 0
        }
        
        # Predict risks using ML
        risk_prediction = self.risk_predictor.predict_engagement_risk(client_data, financial_data)
        
        # Calculate materiality
        materiality_calc = self.risk_predictor.calculate_materiality(
            financial_data, risk_prediction['overall_risk']
        )
        
        # Create risk assessment
        risk_assessment = RiskAssessment.objects.create(
            engagement=engagement,
            inherent_risk=risk_prediction['inherent_risk'],
            control_risk=risk_prediction['control_risk'],
            detection_risk=risk_prediction['detection_risk'],
            overall_audit_risk=risk_prediction['overall_risk'],
            overall_materiality=materiality_calc['overall_materiality'],
            performance_materiality=materiality_calc['performance_materiality'],
            trivial_threshold=materiality_calc['trivial_threshold'],
            materiality_basis=materiality_calc['recommended_base'],
            risk_factors=risk_prediction,
            assessment_date=timezone.now().date(),
            status='DRAFT'
        )
        
        # Create detailed risk factors
        self._create_risk_factors(risk_assessment, risk_prediction['risk_factors'])
        
        # Create materiality calculations
        self._create_materiality_calculations(risk_assessment, materiality_calc)
        
        return risk_assessment
    
    def generate_audit_plan(self, risk_assessment: RiskAssessment) -> AuditPlan:
        """
        Generate audit plan based on risk assessment.
        
        Args:
            risk_assessment (RiskAssessment): The risk assessment
            
        Returns:
            AuditPlan: Generated audit plan
        """
        # Determine audit strategy based on risk level
        audit_strategy = self._determine_audit_strategy(risk_assessment)
        
        # Calculate resource requirements
        resource_planning = self._calculate_resource_requirements(risk_assessment)
        
        # Identify key audit areas
        key_areas = self._identify_key_audit_areas(risk_assessment)
        
        # Create audit plan
        audit_plan = AuditPlan.objects.create(
            risk_assessment=risk_assessment,
            audit_strategy=audit_strategy['strategy'],
            audit_approach=audit_strategy['approach'],
            key_audit_areas=key_areas,
            budgeted_hours=resource_planning['total_hours'],
            team_composition=resource_planning['team_composition'],
            timeline=resource_planning['timeline'],
            controls_testing_extent=audit_strategy['controls_testing'],
            substantive_testing_extent=audit_strategy['substantive_testing'],
            analytical_procedures_extent=audit_strategy['analytical_procedures'],
            status='DRAFT'
        )
        
        # Generate audit procedures
        self._generate_audit_procedures(audit_plan, risk_assessment)
        
        return audit_plan
    
    def analyze_trial_balance(self, trial_balance_data: List[Dict[str, Any]], engagement: AuditEngagement) -> Dict[str, Any]:
        """
        Analyze trial balance for risk indicators.
        
        Args:
            trial_balance_data (List[Dict[str, Any]]): Trial balance data
            engagement (AuditEngagement): The audit engagement
            
        Returns:
            Dict[str, Any]: Analysis results
        """
        analysis = self.trial_balance_analyzer.analyze_trial_balance(trial_balance_data)
        
        # Add engagement-specific context
        analysis['engagement_context'] = {
            'engagement_number': engagement.engagement_number,
            'client_name': engagement.client.name,
            'materiality_amount': float(engagement.materiality_amount or 100000),
            'overall_risk_level': engagement.overall_risk_level
        }
        
        return analysis
    
    def apply_risk_template(self, engagement: AuditEngagement, template_id: int) -> RiskAssessment:
        """
        Apply a risk template to create initial risk assessment.
        
        Args:
            engagement (AuditEngagement): The audit engagement
            template_id (int): Risk template ID
            
        Returns:
            RiskAssessment: Created risk assessment
        """
        try:
            template = RiskTemplate.objects.get(id=template_id, is_active=True)
        except RiskTemplate.DoesNotExist:
            raise ValueError("Risk template not found")
        
        # Create risk assessment from template
        risk_assessment = RiskAssessment.objects.create(
            engagement=engagement,
            inherent_risk='MEDIUM',  # Default values
            control_risk='MEDIUM',
            detection_risk='MEDIUM',
            overall_audit_risk='MEDIUM',
            risk_factors=template.risk_factors_template,
            assessment_date=timezone.now().date(),
            status='DRAFT'
        )
        
        # Apply materiality guidelines from template
        if template.materiality_guidelines:
            self._apply_materiality_guidelines(risk_assessment, template.materiality_guidelines)
        
        # Create risk factors from template
        for factor_template in template.risk_factors_template.get('factors', []):
            RiskFactor.objects.create(
                risk_assessment=risk_assessment,
                category=factor_template.get('category', 'BUSINESS'),
                factor_name=factor_template.get('name', ''),
                description=factor_template.get('description', ''),
                likelihood=factor_template.get('likelihood', 3),
                impact=factor_template.get('impact', 3),
                risk_level=factor_template.get('risk_level', 'MEDIUM'),
                audit_response=factor_template.get('audit_response', ''),
                residual_risk=factor_template.get('residual_risk', 'MEDIUM')
            )
        
        # Update template usage
        template.usage_count += 1
        template.save()
        
        return risk_assessment
    
    def _create_risk_factors(self, risk_assessment: RiskAssessment, risk_factors: List[Dict[str, Any]]):
        """Create detailed risk factors"""
        for factor in risk_factors:
            RiskFactor.objects.create(
                risk_assessment=risk_assessment,
                category=factor.get('category', 'BUSINESS'),
                factor_name=factor.get('factor_name', ''),
                description=factor.get('description', ''),
                likelihood=factor.get('likelihood', 3),
                impact=factor.get('impact', 3),
                risk_level=factor.get('risk_level', 'MEDIUM'),
                audit_response=factor.get('audit_response', ''),
                residual_risk=factor.get('risk_level', 'MEDIUM')  # Assume same as initial risk
            )
    
    def _create_materiality_calculations(self, risk_assessment: RiskAssessment, materiality_calc: Dict[str, Any]):
        """Create materiality calculations"""
        for calc in materiality_calc.get('base_calculations', []):
            MaterialityCalculation.objects.create(
                risk_assessment=risk_assessment,
                base_type=calc['base_type'],
                base_amount=calc['base_amount'],
                percentage_applied=calc['percentage'],
                calculated_materiality=calc['calculated_amount'],
                calculated_performance_materiality=calc['calculated_amount'] * 0.75,
                calculated_trivial_threshold=calc['calculated_amount'] * 0.05,
                rationale=f"Based on {calc['base_type']} with {calc['percentage']}% applied"
            )
    
    def _determine_audit_strategy(self, risk_assessment: RiskAssessment) -> Dict[str, str]:
        """Determine audit strategy based on risk assessment"""
        overall_risk = risk_assessment.overall_audit_risk
        control_risk = risk_assessment.control_risk
        
        if overall_risk == 'HIGH':
            return {
                'strategy': 'Substantive approach with extensive testing',
                'approach': 'Risk-based audit with focus on high-risk areas',
                'controls_testing': 'LOW',
                'substantive_testing': 'HIGH',
                'analytical_procedures': 'MEDIUM'
            }
        elif overall_risk == 'MEDIUM':
            return {
                'strategy': 'Balanced approach combining controls and substantive testing',
                'approach': 'Risk-based audit with moderate testing',
                'controls_testing': 'MEDIUM',
                'substantive_testing': 'MEDIUM',
                'analytical_procedures': 'HIGH'
            }
        else:
            return {
                'strategy': 'Controls reliance approach with targeted substantive testing',
                'approach': 'Efficient audit with focus on key areas',
                'controls_testing': 'HIGH',
                'substantive_testing': 'LOW',
                'analytical_procedures': 'HIGH'
            }
    
    def _calculate_resource_requirements(self, risk_assessment: RiskAssessment) -> Dict[str, Any]:
        """Calculate resource requirements based on risk"""
        base_hours = 120  # Base audit hours
        
        # Adjust based on risk level
        risk_multiplier = {
            'LOW': 0.8,
            'MEDIUM': 1.0,
            'HIGH': 1.5
        }
        
        total_hours = int(base_hours * risk_multiplier.get(risk_assessment.overall_audit_risk, 1.0))
        
        return {
            'total_hours': total_hours,
            'team_composition': {
                'partner_hours': max(8, total_hours * 0.1),
                'manager_hours': max(16, total_hours * 0.2),
                'senior_hours': max(24, total_hours * 0.4),
                'junior_hours': max(16, total_hours * 0.3)
            },
            'timeline': {
                'planning_weeks': 1,
                'fieldwork_weeks': max(2, total_hours // 40),
                'completion_weeks': 1
            }
        }
    
    def _identify_key_audit_areas(self, risk_assessment: RiskAssessment) -> List[str]:
        """Identify key audit areas based on risk factors"""
        key_areas = ['Revenue', 'Expenses', 'Assets', 'Liabilities']
        
        # Add specific areas based on risk factors
        risk_factors = risk_assessment.risk_factors_detail.all()
        
        for factor in risk_factors:
            if factor.risk_level == 'HIGH':
                if factor.category == 'FINANCIAL':
                    key_areas.append('Financial Reporting')
                elif factor.category == 'COMPLIANCE':
                    key_areas.append('Regulatory Compliance')
                elif factor.category == 'FRAUD':
                    key_areas.append('Fraud Risk Areas')
        
        return list(set(key_areas))  # Remove duplicates
    
    def _generate_audit_procedures(self, audit_plan: AuditPlan, risk_assessment: RiskAssessment):
        """Generate audit procedures based on risk assessment"""
        risk_factors = list(risk_assessment.risk_factors_detail.all())
        materiality = float(risk_assessment.overall_materiality or 100000)
        
        # Generate procedures using ML predictor
        procedures = self.risk_predictor.generate_audit_procedures(
            [self._risk_factor_to_dict(rf) for rf in risk_factors],
            materiality
        )
        
        # Create procedure objects
        for proc in procedures:
            AuditProcedure.objects.create(
                audit_plan=audit_plan,
                procedure_code=proc.get('procedure_code', 'PROC-001'),
                procedure_name=proc.get('procedure_name', 'Audit Procedure'),
                procedure_type=proc.get('procedure_type', 'SUBSTANTIVE_DETAIL'),
                description=proc.get('description', ''),
                addresses_risk='General audit risk',
                assertion_tested=proc.get('assertion_tested', 'Accuracy'),
                sample_size=proc.get('sample_size'),
                expected_hours=proc.get('expected_hours', 4.0),
                status='PLANNED'
            )
    
    def _risk_factor_to_dict(self, risk_factor: RiskFactor) -> Dict[str, Any]:
        """Convert RiskFactor model to dictionary"""
        return {
            'category': risk_factor.category,
            'factor_name': risk_factor.factor_name,
            'description': risk_factor.description,
            'likelihood': risk_factor.likelihood,
            'impact': risk_factor.impact,
            'risk_level': risk_factor.risk_level,
            'audit_response': risk_factor.audit_response
        }
    
    def _apply_materiality_guidelines(self, risk_assessment: RiskAssessment, guidelines: Dict[str, Any]):
        """Apply materiality guidelines from template"""
        # This would apply template-specific materiality calculations
        # For now, just set default values
        risk_assessment.overall_materiality = guidelines.get('default_materiality', 100000)
        risk_assessment.performance_materiality = risk_assessment.overall_materiality * 0.75
        risk_assessment.trivial_threshold = risk_assessment.overall_materiality * 0.05
        risk_assessment.save()
