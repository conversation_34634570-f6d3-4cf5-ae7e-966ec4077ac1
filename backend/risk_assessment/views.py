"""views.py - Risk Assessment API views"""

from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count, Avg
from .models import RiskAssessment, RiskFactor, AuditPlan, AuditProcedure, MaterialityCalculation, RiskTemplate
from .serializers import (
    RiskAssessmentSerializer, RiskFactorSerializer, AuditPlanSerializer,
    AuditProcedureSerializer, MaterialityCalculationSerializer, RiskTemplateSerializer
)
from .services import RiskAssessmentService
from audit_engagements.models import AuditEngagement

class RiskAssessmentViewSet(viewsets.ModelViewSet):
    queryset = RiskAssessment.objects.all()
    serializer_class = RiskAssessmentSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['engagement', 'overall_audit_risk', 'status']
    ordering = ['-created_at']
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.risk_service = RiskAssessmentService()
    
    @action(detail=False, methods=['post'])
    def create_assessment(self, request):
        """Create risk assessment for an engagement"""
        engagement_id = request.data.get('engagement_id')
        financial_data = request.data.get('financial_data', {})
        
        if not engagement_id:
            return Response({'error': 'engagement_id required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            engagement = AuditEngagement.objects.get(id=engagement_id)
        except AuditEngagement.DoesNotExist:
            return Response({'error': 'Engagement not found'}, status=status.HTTP_404_NOT_FOUND)
        
        # Check if assessment already exists
        if hasattr(engagement, 'risk_assessment'):
            return Response({'error': 'Risk assessment already exists for this engagement'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        # Create risk assessment
        risk_assessment = self.risk_service.create_risk_assessment(engagement, financial_data)
        
        serializer = RiskAssessmentSerializer(risk_assessment)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    
    @action(detail=True, methods=['post'])
    def generate_audit_plan(self, request, pk=None):
        """Generate audit plan based on risk assessment"""
        risk_assessment = self.get_object()
        
        # Check if audit plan already exists
        if hasattr(risk_assessment, 'audit_plan'):
            return Response({'error': 'Audit plan already exists'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        # Generate audit plan
        audit_plan = self.risk_service.generate_audit_plan(risk_assessment)
        
        serializer = AuditPlanSerializer(audit_plan)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    
    @action(detail=True, methods=['post'])
    def approve_assessment(self, request, pk=None):
        """Approve risk assessment"""
        risk_assessment = self.get_object()
        
        if risk_assessment.status != 'IN_REVIEW':
            return Response({'error': 'Assessment must be in review to approve'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        risk_assessment.status = 'APPROVED'
        risk_assessment.approved_by = request.user
        risk_assessment.save()
        
        return Response({'status': 'Risk assessment approved'})
    
    @action(detail=False, methods=['post'])
    def analyze_trial_balance(self, request):
        """Analyze trial balance for risk indicators"""
        engagement_id = request.data.get('engagement_id')
        trial_balance_data = request.data.get('trial_balance_data', [])
        
        if not engagement_id or not trial_balance_data:
            return Response({'error': 'engagement_id and trial_balance_data required'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        try:
            engagement = AuditEngagement.objects.get(id=engagement_id)
        except AuditEngagement.DoesNotExist:
            return Response({'error': 'Engagement not found'}, status=status.HTTP_404_NOT_FOUND)
        
        # Analyze trial balance
        analysis = self.risk_service.analyze_trial_balance(trial_balance_data, engagement)
        
        return Response(analysis)
    
    @action(detail=False, methods=['get'])
    def risk_dashboard(self, request):
        """Get risk assessment dashboard data"""
        stats = {
            'total_assessments': RiskAssessment.objects.count(),
            'by_risk_level': dict(
                RiskAssessment.objects.values_list('overall_audit_risk')
                .annotate(count=Count('id'))
            ),
            'by_status': dict(
                RiskAssessment.objects.values_list('status')
                .annotate(count=Count('id'))
            ),
            'avg_risk_score': RiskAssessment.objects.aggregate(
                avg_score=Avg('risk_score')
            )['avg_score'] or 0,
            'pending_approval': RiskAssessment.objects.filter(status='IN_REVIEW').count(),
            'high_risk_engagements': RiskAssessment.objects.filter(overall_audit_risk='HIGH').count()
        }
        
        return Response(stats)

class RiskFactorViewSet(viewsets.ModelViewSet):
    queryset = RiskFactor.objects.all()
    serializer_class = RiskFactorSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['risk_assessment', 'category', 'risk_level']
    ordering = ['category', 'factor_name']

class AuditPlanViewSet(viewsets.ModelViewSet):
    queryset = AuditPlan.objects.all()
    serializer_class = AuditPlanSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['status']
    ordering = ['-created_at']
    
    @action(detail=True, methods=['post'])
    def approve_plan(self, request, pk=None):
        """Approve audit plan"""
        audit_plan = self.get_object()
        
        if audit_plan.status != 'DRAFT':
            return Response({'error': 'Plan must be in draft to approve'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        audit_plan.status = 'APPROVED'
        audit_plan.approved_by = request.user
        audit_plan.approved_date = timezone.now()
        audit_plan.save()
        
        return Response({'status': 'Audit plan approved'})
    
    @action(detail=True, methods=['get'])
    def resource_summary(self, request, pk=None):
        """Get resource summary for audit plan"""
        audit_plan = self.get_object()
        
        procedures = audit_plan.procedures.all()
        total_planned_hours = sum(proc.expected_hours for proc in procedures)
        total_actual_hours = sum(proc.actual_hours for proc in procedures)
        
        summary = {
            'budgeted_hours': audit_plan.budgeted_hours,
            'planned_procedure_hours': total_planned_hours,
            'actual_hours': total_actual_hours,
            'variance': total_actual_hours - total_planned_hours,
            'completion_percentage': (total_actual_hours / total_planned_hours * 100) if total_planned_hours > 0 else 0,
            'procedures_count': procedures.count(),
            'completed_procedures': procedures.filter(status='COMPLETED').count()
        }
        
        return Response(summary)

class AuditProcedureViewSet(viewsets.ModelViewSet):
    queryset = AuditProcedure.objects.all()
    serializer_class = AuditProcedureSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['audit_plan', 'procedure_type', 'status', 'assigned_to']
    search_fields = ['procedure_name', 'description']
    ordering = ['procedure_code']
    
    @action(detail=True, methods=['post'])
    def start_procedure(self, request, pk=None):
        """Start execution of audit procedure"""
        procedure = self.get_object()
        
        if procedure.status != 'PLANNED':
            return Response({'error': 'Procedure must be planned to start'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        procedure.status = 'IN_PROGRESS'
        procedure.assigned_to = request.user
        procedure.save()
        
        return Response({'status': 'Procedure started'})
    
    @action(detail=True, methods=['post'])
    def complete_procedure(self, request, pk=None):
        """Complete audit procedure"""
        procedure = self.get_object()
        
        if procedure.status != 'IN_PROGRESS':
            return Response({'error': 'Procedure must be in progress to complete'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        # Update procedure with results
        procedure.status = 'COMPLETED'
        procedure.actual_hours = request.data.get('actual_hours', procedure.expected_hours)
        procedure.results = request.data.get('results', '')
        procedure.exceptions_noted = request.data.get('exceptions_noted', '')
        procedure.conclusion = request.data.get('conclusion', '')
        procedure.save()
        
        return Response({'status': 'Procedure completed'})

class MaterialityCalculationViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = MaterialityCalculation.objects.all()
    serializer_class = MaterialityCalculationSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['risk_assessment', 'base_type']
    ordering = ['-created_at']

class RiskTemplateViewSet(viewsets.ModelViewSet):
    queryset = RiskTemplate.objects.filter(is_active=True)
    serializer_class = RiskTemplateSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['industry', 'entity_type', 'is_active']
    search_fields = ['name', 'description']
    ordering = ['name']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    @action(detail=True, methods=['post'])
    def apply_template(self, request, pk=None):
        """Apply risk template to an engagement"""
        template = self.get_object()
        engagement_id = request.data.get('engagement_id')
        
        if not engagement_id:
            return Response({'error': 'engagement_id required'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        try:
            engagement = AuditEngagement.objects.get(id=engagement_id)
        except AuditEngagement.DoesNotExist:
            return Response({'error': 'Engagement not found'}, status=status.HTTP_404_NOT_FOUND)
        
        # Check if assessment already exists
        if hasattr(engagement, 'risk_assessment'):
            return Response({'error': 'Risk assessment already exists for this engagement'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        # Apply template
        risk_service = RiskAssessmentService()
        risk_assessment = risk_service.apply_risk_template(engagement, template.id)
        
        serializer = RiskAssessmentSerializer(risk_assessment)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    
    @action(detail=False, methods=['get'])
    def template_recommendations(self, request):
        """Get template recommendations based on client characteristics"""
        industry = request.query_params.get('industry')
        entity_type = request.query_params.get('entity_type')
        
        templates = RiskTemplate.objects.filter(is_active=True)
        
        if industry:
            templates = templates.filter(Q(industry=industry) | Q(industry__isnull=True))
        
        if entity_type:
            templates = templates.filter(Q(entity_type=entity_type) | Q(entity_type__isnull=True))
        
        # Order by usage count (most used first)
        templates = templates.order_by('-usage_count')[:5]
        
        serializer = RiskTemplateSerializer(templates, many=True)
        return Response(serializer.data)
