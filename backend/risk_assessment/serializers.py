"""serializers.py - Risk Assessment API serializers"""

from rest_framework import serializers
from .models import RiskAssessment, RiskFactor, AuditPlan, AuditProcedure, MaterialityCalculation, RiskTemplate
from users.models import User
from audit_engagements.models import AuditEngagement

class UserSummarySerializer(serializers.ModelSerializer):
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'full_name']

class EngagementSummarySerializer(serializers.ModelSerializer):
    client_name = serializers.CharField(source='client.name', read_only=True)
    
    class Meta:
        model = AuditEngagement
        fields = ['id', 'engagement_number', 'engagement_type', 'client_name']

class MaterialityCalculationSerializer(serializers.ModelSerializer):
    class Meta:
        model = MaterialityCalculation
        fields = [
            'id', 'base_type', 'base_amount', 'percentage_applied',
            'calculated_materiality', 'performance_materiality_percentage',
            'calculated_performance_materiality', 'trivial_threshold_percentage',
            'calculated_trivial_threshold', 'rationale', 'adjustments_made',
            'created_at'
        ]
        read_only_fields = ['created_at']

class RiskFactorSerializer(serializers.ModelSerializer):
    risk_score = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = RiskFactor
        fields = [
            'id', 'category', 'factor_name', 'description', 'likelihood',
            'impact', 'risk_level', 'existing_controls', 'control_effectiveness',
            'residual_risk', 'audit_response', 'testing_approach', 'sample_size',
            'risk_score', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class RiskAssessmentSerializer(serializers.ModelSerializer):
    engagement = EngagementSummarySerializer(read_only=True)
    prepared_by = UserSummarySerializer(read_only=True)
    reviewed_by = UserSummarySerializer(read_only=True)
    approved_by = UserSummarySerializer(read_only=True)
    
    risk_factors_detail = RiskFactorSerializer(many=True, read_only=True)
    materiality_calculations = MaterialityCalculationSerializer(many=True, read_only=True)
    
    risk_score = serializers.FloatField(read_only=True)
    
    class Meta:
        model = RiskAssessment
        fields = [
            'id', 'engagement', 'inherent_risk', 'control_risk', 'detection_risk',
            'overall_audit_risk', 'overall_materiality', 'performance_materiality',
            'trivial_threshold', 'materiality_basis', 'risk_factors', 'assessment_date',
            'status', 'prepared_by', 'reviewed_by', 'approved_by', 'risk_score',
            'risk_factors_detail', 'materiality_calculations', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class AuditProcedureSerializer(serializers.ModelSerializer):
    assigned_to = UserSummarySerializer(read_only=True)
    assigned_to_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    
    class Meta:
        model = AuditProcedure
        fields = [
            'id', 'procedure_code', 'procedure_name', 'procedure_type',
            'description', 'addresses_risk', 'assertion_tested', 'sample_size',
            'sampling_method', 'expected_hours', 'assigned_to', 'assigned_to_id',
            'planned_start_date', 'planned_completion_date', 'status',
            'actual_hours', 'results', 'exceptions_noted', 'conclusion',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class AuditPlanSerializer(serializers.ModelSerializer):
    risk_assessment = RiskAssessmentSerializer(read_only=True)
    approved_by = UserSummarySerializer(read_only=True)
    
    procedures = AuditProcedureSerializer(many=True, read_only=True)
    
    class Meta:
        model = AuditPlan
        fields = [
            'id', 'risk_assessment', 'audit_strategy', 'audit_approach',
            'key_audit_areas', 'budgeted_hours', 'team_composition', 'timeline',
            'controls_testing_extent', 'substantive_testing_extent',
            'analytical_procedures_extent', 'status', 'approved_by',
            'approved_date', 'procedures', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'approved_date']

class RiskTemplateSerializer(serializers.ModelSerializer):
    created_by = UserSummarySerializer(read_only=True)
    
    class Meta:
        model = RiskTemplate
        fields = [
            'id', 'name', 'industry', 'entity_type', 'description',
            'risk_factors_template', 'materiality_guidelines', 'is_active',
            'usage_count', 'created_by', 'created_at', 'updated_at'
        ]
        read_only_fields = ['usage_count', 'created_at', 'updated_at']

class RiskAssessmentRequestSerializer(serializers.Serializer):
    """Serializer for risk assessment creation requests"""
    engagement_id = serializers.IntegerField()
    financial_data = serializers.JSONField(default=dict)
    use_template = serializers.BooleanField(default=False)
    template_id = serializers.IntegerField(required=False, allow_null=True)

class TrialBalanceAnalysisRequestSerializer(serializers.Serializer):
    """Serializer for trial balance analysis requests"""
    engagement_id = serializers.IntegerField()
    trial_balance_data = serializers.ListField(
        child=serializers.JSONField(),
        min_length=1
    )

class TrialBalanceItemSerializer(serializers.Serializer):
    """Serializer for trial balance items"""
    account_name = serializers.CharField()
    account_code = serializers.CharField(required=False, allow_blank=True)
    debit = serializers.DecimalField(max_digits=15, decimal_places=2, default=0)
    credit = serializers.DecimalField(max_digits=15, decimal_places=2, default=0)

class RiskDashboardSerializer(serializers.Serializer):
    """Serializer for risk dashboard statistics"""
    total_assessments = serializers.IntegerField()
    by_risk_level = serializers.DictField()
    by_status = serializers.DictField()
    avg_risk_score = serializers.FloatField()
    pending_approval = serializers.IntegerField()
    high_risk_engagements = serializers.IntegerField()

class MaterialityCalculationRequestSerializer(serializers.Serializer):
    """Serializer for materiality calculation requests"""
    financial_data = serializers.JSONField()
    risk_level = serializers.ChoiceField(choices=['LOW', 'MEDIUM', 'HIGH'])
    base_preference = serializers.ChoiceField(
        choices=['NET_INCOME', 'REVENUE', 'TOTAL_ASSETS', 'AUTO'],
        default='AUTO'
    )

class AuditProcedureUpdateSerializer(serializers.Serializer):
    """Serializer for audit procedure updates"""
    actual_hours = serializers.DecimalField(max_digits=6, decimal_places=2, required=False)
    results = serializers.CharField(required=False, allow_blank=True)
    exceptions_noted = serializers.CharField(required=False, allow_blank=True)
    conclusion = serializers.CharField(required=False, allow_blank=True)
    status = serializers.ChoiceField(
        choices=['PLANNED', 'IN_PROGRESS', 'COMPLETED', 'NOT_APPLICABLE'],
        required=False
    )

class RiskFactorCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating risk factors"""
    class Meta:
        model = RiskFactor
        fields = [
            'category', 'factor_name', 'description', 'likelihood', 'impact',
            'risk_level', 'existing_controls', 'control_effectiveness',
            'residual_risk', 'audit_response', 'testing_approach', 'sample_size'
        ]

class AuditPlanSummarySerializer(serializers.ModelSerializer):
    """Lightweight serializer for audit plan listings"""
    engagement_number = serializers.CharField(source='risk_assessment.engagement.engagement_number', read_only=True)
    client_name = serializers.CharField(source='risk_assessment.engagement.client.name', read_only=True)
    overall_risk = serializers.CharField(source='risk_assessment.overall_audit_risk', read_only=True)
    
    class Meta:
        model = AuditPlan
        fields = [
            'id', 'engagement_number', 'client_name', 'overall_risk',
            'budgeted_hours', 'status', 'created_at'
        ]

class RiskAssessmentSummarySerializer(serializers.ModelSerializer):
    """Lightweight serializer for risk assessment listings"""
    engagement_number = serializers.CharField(source='engagement.engagement_number', read_only=True)
    client_name = serializers.CharField(source='engagement.client.name', read_only=True)
    prepared_by_name = serializers.CharField(source='prepared_by.get_full_name', read_only=True)
    
    class Meta:
        model = RiskAssessment
        fields = [
            'id', 'engagement_number', 'client_name', 'overall_audit_risk',
            'overall_materiality', 'status', 'prepared_by_name', 'assessment_date'
        ]
