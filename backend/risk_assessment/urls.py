"""urls.py - Risk Assessment API routing"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    RiskAssessmentViewSet,
    RiskFactorViewSet,
    AuditPlanViewSet,
    AuditProcedureViewSet,
    MaterialityCalculationViewSet,
    RiskTemplateViewSet
)

router = DefaultRouter()
router.register(r'assessments', RiskAssessmentViewSet)
router.register(r'risk-factors', RiskFactorViewSet)
router.register(r'audit-plans', AuditPlanViewSet)
router.register(r'procedures', AuditProcedureViewSet)
router.register(r'materiality', MaterialityCalculationViewSet)
router.register(r'templates', RiskTemplateViewSet)

urlpatterns = [
    path('', include(router.urls)),
]
