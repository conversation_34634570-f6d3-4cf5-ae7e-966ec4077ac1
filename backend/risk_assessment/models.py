"""models.py - Risk-Based Audit Planning models"""

from django.db import models
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from audit_engagements.models import AuditEngagement
from clients.models import Client
from users.models import User
import json

class RiskAssessment(models.Model):
    """Main risk assessment for an audit engagement"""
    
    RISK_LEVELS = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
    ]
    
    ASSESSMENT_STATUS = [
        ('DRAFT', 'Draft'),
        ('IN_REVIEW', 'In Review'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
    ]
    
    engagement = models.OneToOneField(AuditEngagement, on_delete=models.CASCADE, related_name='risk_assessment')
    
    # Overall Risk Assessment
    inherent_risk = models.CharField(max_length=10, choices=RISK_LEVELS, default='MEDIUM')
    control_risk = models.CharField(max_length=10, choices=RISK_LEVELS, default='MEDIUM')
    detection_risk = models.CharField(max_length=10, choices=RISK_LEVELS, default='MEDIUM')
    overall_audit_risk = models.CharField(max_length=10, choices=RISK_LEVELS, default='MEDIUM')
    
    # Materiality Calculations
    overall_materiality = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    performance_materiality = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    trivial_threshold = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    materiality_basis = models.CharField(max_length=100, null=True, blank=True)
    
    # Risk Factors
    risk_factors = models.JSONField(default=dict)  # Store detailed risk factors
    
    # Assessment Details
    assessment_date = models.DateField()
    status = models.CharField(max_length=15, choices=ASSESSMENT_STATUS, default='DRAFT')
    
    # Team
    prepared_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='prepared_risk_assessments')
    reviewed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='reviewed_risk_assessments')
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_risk_assessments')
    
    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Risk Assessment - {self.engagement.engagement_number}"
    
    @property
    def risk_score(self):
        """Calculate overall risk score (1-10)"""
        risk_mapping = {'LOW': 2, 'MEDIUM': 5, 'HIGH': 8}
        inherent = risk_mapping.get(self.inherent_risk, 5)
        control = risk_mapping.get(self.control_risk, 5)
        detection = risk_mapping.get(self.detection_risk, 5)
        return round((inherent + control + detection) / 3, 1)

class RiskFactor(models.Model):
    """Individual risk factors for assessment"""
    
    RISK_CATEGORIES = [
        ('BUSINESS', 'Business Risk'),
        ('FINANCIAL', 'Financial Risk'),
        ('OPERATIONAL', 'Operational Risk'),
        ('COMPLIANCE', 'Compliance Risk'),
        ('FRAUD', 'Fraud Risk'),
        ('TECHNOLOGY', 'Technology Risk'),
        ('ENVIRONMENTAL', 'Environmental Risk'),
    ]
    
    risk_assessment = models.ForeignKey(RiskAssessment, on_delete=models.CASCADE, related_name='risk_factors_detail')
    category = models.CharField(max_length=20, choices=RISK_CATEGORIES)
    factor_name = models.CharField(max_length=255)
    description = models.TextField()
    
    # Risk Evaluation
    likelihood = models.IntegerField(validators=[MinValueValidator(1), MaxValueValidator(5)])  # 1-5 scale
    impact = models.IntegerField(validators=[MinValueValidator(1), MaxValueValidator(5)])  # 1-5 scale
    risk_level = models.CharField(max_length=10, choices=RiskAssessment.RISK_LEVELS)
    
    # Mitigation
    existing_controls = models.TextField(blank=True, null=True)
    control_effectiveness = models.CharField(max_length=10, choices=RiskAssessment.RISK_LEVELS, default='MEDIUM')
    residual_risk = models.CharField(max_length=10, choices=RiskAssessment.RISK_LEVELS)
    
    # Audit Response
    audit_response = models.TextField()
    testing_approach = models.CharField(max_length=100, blank=True, null=True)
    sample_size = models.IntegerField(null=True, blank=True)
    
    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['category', 'factor_name']
    
    def __str__(self):
        return f"{self.factor_name} ({self.category})"
    
    @property
    def risk_score(self):
        """Calculate risk score (likelihood × impact)"""
        return self.likelihood * self.impact

class AuditPlan(models.Model):
    """Detailed audit plan based on risk assessment"""
    
    PLAN_STATUS = [
        ('DRAFT', 'Draft'),
        ('APPROVED', 'Approved'),
        ('IN_EXECUTION', 'In Execution'),
        ('COMPLETED', 'Completed'),
    ]
    
    risk_assessment = models.OneToOneField(RiskAssessment, on_delete=models.CASCADE, related_name='audit_plan')
    
    # Plan Details
    audit_strategy = models.TextField()
    audit_approach = models.TextField()
    key_audit_areas = models.JSONField(default=list)
    
    # Resource Planning
    budgeted_hours = models.IntegerField()
    team_composition = models.JSONField(default=dict)
    timeline = models.JSONField(default=dict)
    
    # Testing Strategy
    controls_testing_extent = models.CharField(max_length=10, choices=RiskAssessment.RISK_LEVELS, default='MEDIUM')
    substantive_testing_extent = models.CharField(max_length=10, choices=RiskAssessment.RISK_LEVELS, default='MEDIUM')
    analytical_procedures_extent = models.CharField(max_length=10, choices=RiskAssessment.RISK_LEVELS, default='MEDIUM')
    
    # Status
    status = models.CharField(max_length=15, choices=PLAN_STATUS, default='DRAFT')
    
    # Approval
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    approved_date = models.DateTimeField(null=True, blank=True)
    
    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Audit Plan - {self.risk_assessment.engagement.engagement_number}"

class AuditProcedure(models.Model):
    """Specific audit procedures based on risk assessment"""
    
    PROCEDURE_TYPES = [
        ('RISK_ASSESSMENT', 'Risk Assessment Procedure'),
        ('TEST_OF_CONTROLS', 'Test of Controls'),
        ('SUBSTANTIVE_ANALYTICAL', 'Substantive Analytical Procedure'),
        ('SUBSTANTIVE_DETAIL', 'Substantive Test of Details'),
        ('DUAL_PURPOSE', 'Dual Purpose Test'),
    ]
    
    PROCEDURE_STATUS = [
        ('PLANNED', 'Planned'),
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('NOT_APPLICABLE', 'Not Applicable'),
    ]
    
    audit_plan = models.ForeignKey(AuditPlan, on_delete=models.CASCADE, related_name='procedures')
    risk_factor = models.ForeignKey(RiskFactor, on_delete=models.CASCADE, related_name='procedures', null=True, blank=True)
    
    # Procedure Details
    procedure_code = models.CharField(max_length=20)
    procedure_name = models.CharField(max_length=255)
    procedure_type = models.CharField(max_length=20, choices=PROCEDURE_TYPES)
    description = models.TextField()
    
    # Risk Response
    addresses_risk = models.TextField()
    assertion_tested = models.CharField(max_length=100)  # Existence, Completeness, etc.
    
    # Execution Details
    sample_size = models.IntegerField(null=True, blank=True)
    sampling_method = models.CharField(max_length=50, blank=True, null=True)
    expected_hours = models.DecimalField(max_digits=6, decimal_places=2)
    
    # Assignment
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    planned_start_date = models.DateField(null=True, blank=True)
    planned_completion_date = models.DateField(null=True, blank=True)
    
    # Status
    status = models.CharField(max_length=15, choices=PROCEDURE_STATUS, default='PLANNED')
    actual_hours = models.DecimalField(max_digits=6, decimal_places=2, default=0)
    
    # Results
    results = models.TextField(blank=True, null=True)
    exceptions_noted = models.TextField(blank=True, null=True)
    conclusion = models.TextField(blank=True, null=True)
    
    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['procedure_code']
        unique_together = ['audit_plan', 'procedure_code']
    
    def __str__(self):
        return f"{self.procedure_code} - {self.procedure_name}"

class MaterialityCalculation(models.Model):
    """Detailed materiality calculations"""
    
    MATERIALITY_BASES = [
        ('NET_INCOME', 'Net Income'),
        ('REVENUE', 'Revenue'),
        ('TOTAL_ASSETS', 'Total Assets'),
        ('EQUITY', 'Equity'),
        ('EXPENSES', 'Total Expenses'),
    ]
    
    risk_assessment = models.ForeignKey(RiskAssessment, on_delete=models.CASCADE, related_name='materiality_calculations')
    
    # Base Amount
    base_type = models.CharField(max_length=20, choices=MATERIALITY_BASES)
    base_amount = models.DecimalField(max_digits=15, decimal_places=2)
    percentage_applied = models.DecimalField(max_digits=5, decimal_places=2)
    
    # Calculated Amounts
    calculated_materiality = models.DecimalField(max_digits=15, decimal_places=2)
    performance_materiality_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=75.00)
    calculated_performance_materiality = models.DecimalField(max_digits=15, decimal_places=2)
    trivial_threshold_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=5.00)
    calculated_trivial_threshold = models.DecimalField(max_digits=15, decimal_places=2)
    
    # Justification
    rationale = models.TextField()
    adjustments_made = models.TextField(blank=True, null=True)
    
    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Materiality - {self.base_type} ({self.risk_assessment.engagement.engagement_number})"

class RiskTemplate(models.Model):
    """Templates for common risk factors by industry/entity type"""
    
    name = models.CharField(max_length=255)
    industry = models.CharField(max_length=50, blank=True, null=True)
    entity_type = models.CharField(max_length=50, blank=True, null=True)
    description = models.TextField()
    
    # Template Data
    risk_factors_template = models.JSONField()  # Template for risk factors
    materiality_guidelines = models.JSONField(default=dict)
    
    # Metadata
    is_active = models.BooleanField(default=True)
    usage_count = models.IntegerField(default=0)
    
    # System fields
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']
    
    def __str__(self):
        return f"Risk Template: {self.name}"
