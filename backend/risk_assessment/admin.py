"""admin.py - Risk Assessment admin configuration"""

from django.contrib import admin
from .models import RiskAssessment, RiskFactor, AuditPlan, AuditProcedure, MaterialityCalculation, RiskTemplate

@admin.register(RiskAssessment)
class RiskAssessmentAdmin(admin.ModelAdmin):
    list_display = (
        'engagement', 'overall_audit_risk', 'overall_materiality',
        'status', 'prepared_by', 'assessment_date'
    )
    list_filter = ('overall_audit_risk', 'status', 'assessment_date')
    search_fields = ('engagement__engagement_number', 'engagement__client__name')
    readonly_fields = ('risk_score', 'created_at', 'updated_at')
    raw_id_fields = ('engagement', 'prepared_by', 'reviewed_by', 'approved_by')
    
    fieldsets = (
        ('Engagement Information', {
            'fields': ('engagement',)
        }),
        ('Risk Assessment', {
            'fields': (
                'inherent_risk', 'control_risk', 'detection_risk', 'overall_audit_risk',
                'risk_score'
            )
        }),
        ('Materiality', {
            'fields': (
                'overall_materiality', 'performance_materiality', 'trivial_threshold',
                'materiality_basis'
            )
        }),
        ('Assessment Details', {
            'fields': ('risk_factors', 'assessment_date', 'status')
        }),
        ('Approval Workflow', {
            'fields': ('prepared_by', 'reviewed_by', 'approved_by')
        }),
        ('System Fields', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(RiskFactor)
class RiskFactorAdmin(admin.ModelAdmin):
    list_display = (
        'factor_name', 'risk_assessment', 'category', 'risk_level',
        'likelihood', 'impact', 'risk_score'
    )
    list_filter = ('category', 'risk_level', 'likelihood', 'impact')
    search_fields = ('factor_name', 'description', 'risk_assessment__engagement__engagement_number')
    readonly_fields = ('risk_score', 'created_at', 'updated_at')
    raw_id_fields = ('risk_assessment',)
    
    fieldsets = (
        ('Risk Factor Information', {
            'fields': ('risk_assessment', 'category', 'factor_name', 'description')
        }),
        ('Risk Evaluation', {
            'fields': ('likelihood', 'impact', 'risk_level', 'risk_score')
        }),
        ('Controls Assessment', {
            'fields': ('existing_controls', 'control_effectiveness', 'residual_risk')
        }),
        ('Audit Response', {
            'fields': ('audit_response', 'testing_approach', 'sample_size')
        }),
        ('System Fields', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(AuditPlan)
class AuditPlanAdmin(admin.ModelAdmin):
    list_display = (
        'risk_assessment', 'budgeted_hours', 'status', 'approved_by', 'created_at'
    )
    list_filter = ('status', 'controls_testing_extent', 'substantive_testing_extent')
    search_fields = ('risk_assessment__engagement__engagement_number',)
    readonly_fields = ('created_at', 'updated_at', 'approved_date')
    raw_id_fields = ('risk_assessment', 'approved_by')
    
    fieldsets = (
        ('Risk Assessment', {
            'fields': ('risk_assessment',)
        }),
        ('Audit Strategy', {
            'fields': ('audit_strategy', 'audit_approach', 'key_audit_areas')
        }),
        ('Resource Planning', {
            'fields': ('budgeted_hours', 'team_composition', 'timeline')
        }),
        ('Testing Strategy', {
            'fields': (
                'controls_testing_extent', 'substantive_testing_extent',
                'analytical_procedures_extent'
            )
        }),
        ('Approval', {
            'fields': ('status', 'approved_by', 'approved_date')
        }),
        ('System Fields', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(AuditProcedure)
class AuditProcedureAdmin(admin.ModelAdmin):
    list_display = (
        'procedure_code', 'procedure_name', 'audit_plan', 'procedure_type',
        'status', 'assigned_to', 'expected_hours', 'actual_hours'
    )
    list_filter = ('procedure_type', 'status', 'assigned_to')
    search_fields = (
        'procedure_code', 'procedure_name', 'description',
        'audit_plan__risk_assessment__engagement__engagement_number'
    )
    readonly_fields = ('created_at', 'updated_at')
    raw_id_fields = ('audit_plan', 'risk_factor', 'assigned_to')
    
    fieldsets = (
        ('Procedure Information', {
            'fields': (
                'audit_plan', 'risk_factor', 'procedure_code', 'procedure_name',
                'procedure_type', 'description'
            )
        }),
        ('Risk Response', {
            'fields': ('addresses_risk', 'assertion_tested')
        }),
        ('Execution Details', {
            'fields': (
                'sample_size', 'sampling_method', 'expected_hours',
                'assigned_to', 'planned_start_date', 'planned_completion_date'
            )
        }),
        ('Status and Results', {
            'fields': ('status', 'actual_hours', 'results', 'exceptions_noted', 'conclusion')
        }),
        ('System Fields', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(MaterialityCalculation)
class MaterialityCalculationAdmin(admin.ModelAdmin):
    list_display = (
        'risk_assessment', 'base_type', 'base_amount', 'percentage_applied',
        'calculated_materiality', 'created_at'
    )
    list_filter = ('base_type', 'created_at')
    search_fields = ('risk_assessment__engagement__engagement_number', 'rationale')
    readonly_fields = ('created_at',)
    raw_id_fields = ('risk_assessment',)
    
    fieldsets = (
        ('Risk Assessment', {
            'fields': ('risk_assessment',)
        }),
        ('Base Calculation', {
            'fields': ('base_type', 'base_amount', 'percentage_applied')
        }),
        ('Calculated Amounts', {
            'fields': (
                'calculated_materiality', 'performance_materiality_percentage',
                'calculated_performance_materiality', 'trivial_threshold_percentage',
                'calculated_trivial_threshold'
            )
        }),
        ('Justification', {
            'fields': ('rationale', 'adjustments_made')
        }),
        ('System Fields', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

@admin.register(RiskTemplate)
class RiskTemplateAdmin(admin.ModelAdmin):
    list_display = (
        'name', 'industry', 'entity_type', 'is_active', 'usage_count', 'created_by'
    )
    list_filter = ('industry', 'entity_type', 'is_active', 'created_at')
    search_fields = ('name', 'description')
    readonly_fields = ('usage_count', 'created_at', 'updated_at')
    raw_id_fields = ('created_by',)
    
    fieldsets = (
        ('Template Information', {
            'fields': ('name', 'industry', 'entity_type', 'description')
        }),
        ('Template Data', {
            'fields': ('risk_factors_template', 'materiality_guidelines')
        }),
        ('Metadata', {
            'fields': ('is_active', 'usage_count')
        }),
        ('System Fields', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
