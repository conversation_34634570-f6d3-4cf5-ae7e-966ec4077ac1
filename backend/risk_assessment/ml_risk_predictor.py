"""ml_risk_predictor.py - Machine Learning based risk prediction and assessment"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any
from decimal import Decimal
from sklearn.ensemble import RandomForestClassifier, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, mean_squared_error
import joblib
import json

class RiskPredictor:
    """ML-based risk prediction for audit planning"""
    
    def __init__(self):
        self.risk_classifier = RandomForestClassifier(n_estimators=100, random_state=42)
        self.materiality_predictor = GradientBoostingRegressor(n_estimators=100, random_state=42)
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.is_trained = False
        
        # Risk factor weights based on audit standards
        self.risk_weights = {
            'BUSINESS': 0.25,
            'FINANCIAL': 0.30,
            'OPERATIONAL': 0.20,
            'COMPLIANCE': 0.15,
            'FRAUD': 0.10
        }
    
    def predict_engagement_risk(self, client_data: Dict[str, Any], financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Predict overall engagement risk based on client and financial data.
        
        Args:
            client_data (Dict[str, Any]): Client information
            financial_data (Dict[str, Any]): Financial data
            
        Returns:
            Dict[str, Any]: Risk prediction results
        """
        # Extract features for risk prediction
        features = self._extract_risk_features(client_data, financial_data)
        
        # Calculate individual risk components
        inherent_risk = self._assess_inherent_risk(features)
        control_risk = self._assess_control_risk(features)
        detection_risk = self._assess_detection_risk(features)
        
        # Calculate overall audit risk
        overall_risk = self._calculate_overall_risk(inherent_risk, control_risk, detection_risk)
        
        # Identify key risk areas
        key_risk_areas = self._identify_key_risk_areas(features)
        
        # Generate risk factors
        risk_factors = self._generate_risk_factors(features, key_risk_areas)
        
        return {
            'inherent_risk': inherent_risk,
            'control_risk': control_risk,
            'detection_risk': detection_risk,
            'overall_risk': overall_risk,
            'risk_score': self._calculate_risk_score(overall_risk),
            'key_risk_areas': key_risk_areas,
            'risk_factors': risk_factors,
            'confidence_score': 0.85  # Model confidence
        }
    
    def calculate_materiality(self, financial_data: Dict[str, Any], risk_level: str) -> Dict[str, Any]:
        """
        Calculate materiality amounts based on financial data and risk level.
        
        Args:
            financial_data (Dict[str, Any]): Financial data
            risk_level (str): Overall risk level
            
        Returns:
            Dict[str, Any]: Materiality calculations
        """
        # Determine base amount and percentage
        base_calculations = []
        
        # Net Income based calculation
        net_income = financial_data.get('net_income', 0)
        if net_income > 0:
            percentage = self._get_materiality_percentage('NET_INCOME', risk_level)
            base_calculations.append({
                'base_type': 'NET_INCOME',
                'base_amount': net_income,
                'percentage': percentage,
                'calculated_amount': net_income * (percentage / 100),
                'weight': 0.4
            })
        
        # Revenue based calculation
        revenue = financial_data.get('revenue', 0)
        if revenue > 0:
            percentage = self._get_materiality_percentage('REVENUE', risk_level)
            base_calculations.append({
                'base_type': 'REVENUE',
                'base_amount': revenue,
                'percentage': percentage,
                'calculated_amount': revenue * (percentage / 100),
                'weight': 0.3
            })
        
        # Total Assets based calculation
        total_assets = financial_data.get('total_assets', 0)
        if total_assets > 0:
            percentage = self._get_materiality_percentage('TOTAL_ASSETS', risk_level)
            base_calculations.append({
                'base_type': 'TOTAL_ASSETS',
                'base_amount': total_assets,
                'percentage': percentage,
                'calculated_amount': total_assets * (percentage / 100),
                'weight': 0.3
            })
        
        # Calculate weighted average materiality
        if base_calculations:
            weighted_materiality = sum(calc['calculated_amount'] * calc['weight'] for calc in base_calculations)
            weighted_materiality = weighted_materiality / sum(calc['weight'] for calc in base_calculations)
        else:
            weighted_materiality = 100000  # Default materiality
        
        # Calculate performance materiality and trivial threshold
        performance_materiality = weighted_materiality * 0.75  # 75% of overall materiality
        trivial_threshold = weighted_materiality * 0.05  # 5% of overall materiality
        
        # Determine best base
        best_base = max(base_calculations, key=lambda x: x['weight']) if base_calculations else None
        
        return {
            'overall_materiality': round(weighted_materiality, 2),
            'performance_materiality': round(performance_materiality, 2),
            'trivial_threshold': round(trivial_threshold, 2),
            'recommended_base': best_base['base_type'] if best_base else 'REVENUE',
            'base_calculations': base_calculations,
            'risk_adjustment': self._get_risk_adjustment_factor(risk_level)
        }
    
    def generate_audit_procedures(self, risk_factors: List[Dict[str, Any]], materiality: float) -> List[Dict[str, Any]]:
        """
        Generate audit procedures based on identified risk factors.
        
        Args:
            risk_factors (List[Dict[str, Any]]): Identified risk factors
            materiality (float): Overall materiality amount
            
        Returns:
            List[Dict[str, Any]]: Generated audit procedures
        """
        procedures = []
        procedure_counter = 1
        
        for risk_factor in risk_factors:
            category = risk_factor.get('category', 'GENERAL')
            risk_level = risk_factor.get('risk_level', 'MEDIUM')
            
            # Generate procedures based on risk category
            if category == 'FINANCIAL':
                procedures.extend(self._generate_financial_procedures(risk_factor, procedure_counter, materiality))
            elif category == 'OPERATIONAL':
                procedures.extend(self._generate_operational_procedures(risk_factor, procedure_counter, materiality))
            elif category == 'COMPLIANCE':
                procedures.extend(self._generate_compliance_procedures(risk_factor, procedure_counter, materiality))
            elif category == 'FRAUD':
                procedures.extend(self._generate_fraud_procedures(risk_factor, procedure_counter, materiality))
            
            procedure_counter += len(procedures)
        
        return procedures
    
    def _extract_risk_features(self, client_data: Dict[str, Any], financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract features for risk assessment"""
        features = {}
        
        # Client features
        features['entity_type'] = client_data.get('entity_type', 'PRIVATE_LIMITED')
        features['industry'] = client_data.get('industry', 'OTHER')
        features['size_category'] = client_data.get('size_category', 'SMALL')
        features['annual_turnover'] = client_data.get('annual_turnover', 0)
        features['number_of_employees'] = client_data.get('number_of_employees', 0)
        
        # Financial features
        features['revenue'] = financial_data.get('revenue', 0)
        features['net_income'] = financial_data.get('net_income', 0)
        features['total_assets'] = financial_data.get('total_assets', 0)
        features['current_ratio'] = financial_data.get('current_ratio', 1.0)
        features['debt_to_equity'] = financial_data.get('debt_to_equity', 0.5)
        features['revenue_growth'] = financial_data.get('revenue_growth', 0)
        features['profit_margin'] = financial_data.get('profit_margin', 0)
        
        # Risk indicators
        features['related_party_transactions'] = len(financial_data.get('related_party_transactions', []))
        features['unusual_transactions'] = len(financial_data.get('unusual_transactions', []))
        features['prior_year_adjustments'] = financial_data.get('prior_year_adjustments', 0)
        features['management_changes'] = financial_data.get('management_changes', 0)
        
        return features
    
    def _assess_inherent_risk(self, features: Dict[str, Any]) -> str:
        """Assess inherent risk based on features"""
        risk_score = 0
        
        # Industry risk
        high_risk_industries = ['FINANCE', 'CONSTRUCTION', 'REAL_ESTATE', 'IT_SOFTWARE']
        if features['industry'] in high_risk_industries:
            risk_score += 2
        
        # Size and complexity
        if features['annual_turnover'] > 100000000:  # 10 Crore
            risk_score += 2
        elif features['annual_turnover'] > 50000000:  # 5 Crore
            risk_score += 1
        
        # Financial indicators
        if features['debt_to_equity'] > 2.0:
            risk_score += 2
        elif features['debt_to_equity'] > 1.0:
            risk_score += 1
        
        if features['current_ratio'] < 1.0:
            risk_score += 2
        elif features['current_ratio'] < 1.5:
            risk_score += 1
        
        # Growth and volatility
        if abs(features['revenue_growth']) > 50:  # High growth or decline
            risk_score += 2
        elif abs(features['revenue_growth']) > 25:
            risk_score += 1
        
        # Related party transactions
        if features['related_party_transactions'] > 10:
            risk_score += 2
        elif features['related_party_transactions'] > 5:
            risk_score += 1
        
        # Convert score to risk level
        if risk_score >= 6:
            return 'HIGH'
        elif risk_score >= 3:
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def _assess_control_risk(self, features: Dict[str, Any]) -> str:
        """Assess control risk based on features"""
        risk_score = 0
        
        # Entity size (smaller entities typically have weaker controls)
        if features['size_category'] == 'MICRO':
            risk_score += 3
        elif features['size_category'] == 'SMALL':
            risk_score += 2
        elif features['size_category'] == 'MEDIUM':
            risk_score += 1
        
        # Number of employees (segregation of duties)
        if features['number_of_employees'] < 10:
            risk_score += 2
        elif features['number_of_employees'] < 50:
            risk_score += 1
        
        # Management changes
        if features['management_changes'] > 2:
            risk_score += 2
        elif features['management_changes'] > 0:
            risk_score += 1
        
        # Prior year adjustments (indicator of control weaknesses)
        if features['prior_year_adjustments'] > 5:
            risk_score += 2
        elif features['prior_year_adjustments'] > 2:
            risk_score += 1
        
        # Convert score to risk level
        if risk_score >= 5:
            return 'HIGH'
        elif risk_score >= 3:
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def _assess_detection_risk(self, features: Dict[str, Any]) -> str:
        """Assess detection risk based on audit approach"""
        # Detection risk is typically set based on audit strategy
        # For now, return MEDIUM as default
        return 'MEDIUM'
    
    def _calculate_overall_risk(self, inherent: str, control: str, detection: str) -> str:
        """Calculate overall audit risk"""
        risk_values = {'LOW': 1, 'MEDIUM': 2, 'HIGH': 3}
        
        # Audit Risk = Inherent Risk × Control Risk × Detection Risk
        overall_score = risk_values[inherent] * risk_values[control] * risk_values[detection]
        
        if overall_score >= 18:  # 3×3×2 or higher
            return 'HIGH'
        elif overall_score >= 8:   # 2×2×2 or higher
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def _calculate_risk_score(self, risk_level: str) -> float:
        """Convert risk level to numeric score (1-10)"""
        mapping = {'LOW': 3.0, 'MEDIUM': 6.0, 'HIGH': 9.0}
        return mapping.get(risk_level, 6.0)
    
    def _identify_key_risk_areas(self, features: Dict[str, Any]) -> List[str]:
        """Identify key risk areas based on features"""
        risk_areas = []
        
        if features['revenue'] > 50000000:  # 5 Crore
            risk_areas.append('Revenue Recognition')
        
        if features['total_assets'] > 100000000:  # 10 Crore
            risk_areas.append('Asset Valuation')
        
        if features['related_party_transactions'] > 0:
            risk_areas.append('Related Party Transactions')
        
        if features['debt_to_equity'] > 1.5:
            risk_areas.append('Going Concern')
        
        if features['unusual_transactions'] > 0:
            risk_areas.append('Unusual Transactions')
        
        if features['industry'] in ['FINANCE', 'CONSTRUCTION']:
            risk_areas.append('Regulatory Compliance')
        
        return risk_areas
    
    def _generate_risk_factors(self, features: Dict[str, Any], key_areas: List[str]) -> List[Dict[str, Any]]:
        """Generate detailed risk factors"""
        risk_factors = []
        
        for area in key_areas:
            if area == 'Revenue Recognition':
                risk_factors.append({
                    'category': 'FINANCIAL',
                    'factor_name': 'Revenue Recognition Risk',
                    'description': 'Risk of material misstatement in revenue recognition due to complexity or management pressure',
                    'likelihood': 3,
                    'impact': 4,
                    'risk_level': 'HIGH',
                    'audit_response': 'Perform detailed testing of revenue transactions and cutoff procedures'
                })
            
            elif area == 'Related Party Transactions':
                risk_factors.append({
                    'category': 'COMPLIANCE',
                    'factor_name': 'Related Party Transaction Risk',
                    'description': 'Risk of undisclosed or improperly valued related party transactions',
                    'likelihood': 3,
                    'impact': 3,
                    'risk_level': 'MEDIUM',
                    'audit_response': 'Review all related party transactions for proper authorization and disclosure'
                })
        
        return risk_factors
    
    def _get_materiality_percentage(self, base_type: str, risk_level: str) -> float:
        """Get materiality percentage based on base type and risk level"""
        base_percentages = {
            'NET_INCOME': {'LOW': 10.0, 'MEDIUM': 7.5, 'HIGH': 5.0},
            'REVENUE': {'LOW': 1.0, 'MEDIUM': 0.75, 'HIGH': 0.5},
            'TOTAL_ASSETS': {'LOW': 2.0, 'MEDIUM': 1.5, 'HIGH': 1.0}
        }
        
        return base_percentages.get(base_type, {}).get(risk_level, 1.0)
    
    def _get_risk_adjustment_factor(self, risk_level: str) -> float:
        """Get risk adjustment factor for materiality"""
        factors = {'LOW': 1.2, 'MEDIUM': 1.0, 'HIGH': 0.8}
        return factors.get(risk_level, 1.0)
    
    def _generate_financial_procedures(self, risk_factor: Dict[str, Any], start_counter: int, materiality: float) -> List[Dict[str, Any]]:
        """Generate financial audit procedures"""
        procedures = []
        
        procedures.append({
            'procedure_code': f'FIN-{start_counter:03d}',
            'procedure_name': 'Revenue Testing',
            'procedure_type': 'SUBSTANTIVE_DETAIL',
            'description': 'Test revenue transactions for accuracy and completeness',
            'sample_size': max(25, int(materiality / 100000)),
            'expected_hours': 8.0,
            'assertion_tested': 'Occurrence, Completeness, Accuracy'
        })
        
        return procedures
    
    def _generate_operational_procedures(self, risk_factor: Dict[str, Any], start_counter: int, materiality: float) -> List[Dict[str, Any]]:
        """Generate operational audit procedures"""
        return []  # Placeholder
    
    def _generate_compliance_procedures(self, risk_factor: Dict[str, Any], start_counter: int, materiality: float) -> List[Dict[str, Any]]:
        """Generate compliance audit procedures"""
        return []  # Placeholder
    
    def _generate_fraud_procedures(self, risk_factor: Dict[str, Any], start_counter: int, materiality: float) -> List[Dict[str, Any]]:
        """Generate fraud-related audit procedures"""
        return []  # Placeholder


class TrialBalanceAnalyzer:
    """Analyze trial balance for risk indicators and anomalies"""

    def __init__(self):
        self.account_categories = {
            'ASSETS': ['cash', 'bank', 'receivables', 'inventory', 'fixed assets', 'investments'],
            'LIABILITIES': ['payables', 'loans', 'provisions', 'accruals'],
            'INCOME': ['sales', 'revenue', 'income', 'other income'],
            'EXPENSES': ['cost of sales', 'expenses', 'depreciation', 'interest']
        }

    def analyze_trial_balance(self, trial_balance_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze trial balance for risk indicators.

        Args:
            trial_balance_data (List[Dict[str, Any]]): Trial balance data

        Returns:
            Dict[str, Any]: Analysis results
        """
        analysis = {
            'balance_check': self._check_trial_balance(trial_balance_data),
            'unusual_balances': self._identify_unusual_balances(trial_balance_data),
            'ratio_analysis': self._perform_ratio_analysis(trial_balance_data),
            'trend_analysis': self._analyze_trends(trial_balance_data),
            'risk_indicators': self._identify_risk_indicators(trial_balance_data),
            'materiality_items': self._identify_material_items(trial_balance_data),
            'recommendations': self._generate_recommendations(trial_balance_data)
        }

        return analysis

    def _check_trial_balance(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Check if trial balance balances"""
        total_debits = sum(item.get('debit', 0) for item in data)
        total_credits = sum(item.get('credit', 0) for item in data)
        difference = total_debits - total_credits

        return {
            'total_debits': total_debits,
            'total_credits': total_credits,
            'difference': difference,
            'is_balanced': abs(difference) < 1.0,  # Allow for rounding differences
            'balance_status': 'BALANCED' if abs(difference) < 1.0 else 'OUT_OF_BALANCE'
        }

    def _identify_unusual_balances(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Identify unusual account balances"""
        unusual_balances = []

        for item in data:
            account_name = item.get('account_name', '').lower()
            debit = item.get('debit', 0)
            credit = item.get('credit', 0)

            # Check for unusual debit/credit balances
            if 'cash' in account_name and credit > debit:
                unusual_balances.append({
                    'account': item.get('account_name'),
                    'issue': 'Cash account with credit balance',
                    'amount': credit - debit,
                    'risk_level': 'HIGH'
                })

            elif 'receivables' in account_name and credit > debit:
                unusual_balances.append({
                    'account': item.get('account_name'),
                    'issue': 'Receivables with credit balance',
                    'amount': credit - debit,
                    'risk_level': 'MEDIUM'
                })

            elif 'payables' in account_name and debit > credit:
                unusual_balances.append({
                    'account': item.get('account_name'),
                    'issue': 'Payables with debit balance',
                    'amount': debit - credit,
                    'risk_level': 'MEDIUM'
                })

        return unusual_balances

    def _perform_ratio_analysis(self, data: List[Dict[str, Any]]) -> Dict[str, float]:
        """Perform basic ratio analysis"""
        # Extract key amounts
        current_assets = self._sum_accounts(data, ['cash', 'bank', 'receivables', 'inventory'])
        current_liabilities = self._sum_accounts(data, ['payables', 'accruals'])
        total_assets = self._sum_accounts(data, ['cash', 'bank', 'receivables', 'inventory', 'fixed assets'])
        total_liabilities = self._sum_accounts(data, ['payables', 'loans', 'provisions'])
        revenue = self._sum_accounts(data, ['sales', 'revenue'])

        ratios = {}

        if current_liabilities > 0:
            ratios['current_ratio'] = current_assets / current_liabilities

        if total_assets > 0:
            ratios['debt_to_assets'] = total_liabilities / total_assets

        if revenue > 0:
            ratios['asset_turnover'] = revenue / total_assets if total_assets > 0 else 0

        return ratios

    def _analyze_trends(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze trends in account balances"""
        # This would compare with prior year data if available
        # For now, return placeholder
        return {
            'significant_changes': [],
            'growth_rates': {},
            'volatility_indicators': []
        }

    def _identify_risk_indicators(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Identify risk indicators from trial balance"""
        risk_indicators = []

        # Large cash balances
        cash_balance = self._sum_accounts(data, ['cash', 'bank'])
        total_assets = self._sum_accounts(data, ['cash', 'bank', 'receivables', 'inventory', 'fixed assets'])

        if total_assets > 0 and cash_balance / total_assets > 0.3:
            risk_indicators.append({
                'indicator': 'High cash proportion',
                'description': 'Cash represents more than 30% of total assets',
                'risk_level': 'MEDIUM',
                'audit_focus': 'Cash management and investment policies'
            })

        # High receivables
        receivables = self._sum_accounts(data, ['receivables'])
        revenue = self._sum_accounts(data, ['sales', 'revenue'])

        if revenue > 0 and receivables / revenue > 0.2:  # More than 2.4 months of sales
            risk_indicators.append({
                'indicator': 'High receivables balance',
                'description': 'Receivables represent more than 20% of annual revenue',
                'risk_level': 'MEDIUM',
                'audit_focus': 'Receivables aging and collectibility'
            })

        return risk_indicators

    def _identify_material_items(self, data: List[Dict[str, Any]], materiality_threshold: float = 100000) -> List[Dict[str, Any]]:
        """Identify material account balances"""
        material_items = []

        for item in data:
            balance = max(item.get('debit', 0), item.get('credit', 0))
            if balance > materiality_threshold:
                material_items.append({
                    'account': item.get('account_name'),
                    'balance': balance,
                    'percentage_of_materiality': (balance / materiality_threshold) * 100
                })

        return sorted(material_items, key=lambda x: x['balance'], reverse=True)

    def _generate_recommendations(self, data: List[Dict[str, Any]]) -> List[str]:
        """Generate audit recommendations based on analysis"""
        recommendations = []

        # Check trial balance
        balance_check = self._check_trial_balance(data)
        if not balance_check['is_balanced']:
            recommendations.append("Investigate trial balance differences before proceeding with audit")

        # Check for unusual balances
        unusual = self._identify_unusual_balances(data)
        if unusual:
            recommendations.append("Review unusual account balances identified in the analysis")

        # General recommendations
        recommendations.append("Perform analytical procedures on significant account balances")
        recommendations.append("Focus audit attention on material and high-risk accounts")

        return recommendations

    def _sum_accounts(self, data: List[Dict[str, Any]], keywords: List[str]) -> float:
        """Sum account balances matching keywords"""
        total = 0
        for item in data:
            account_name = item.get('account_name', '').lower()
            if any(keyword in account_name for keyword in keywords):
                total += max(item.get('debit', 0), item.get('credit', 0))
        return total
