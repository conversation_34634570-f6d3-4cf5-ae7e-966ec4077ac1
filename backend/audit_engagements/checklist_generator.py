"""checklist_generator.py - Generate dynamic checklists for engagements"""

DEFAULT_CHECKLIST = {
    "Tax Audit": [
        "Verify books of accounts under Section 44AB",
        "Match Form 26AS with TDS records",
        "Review depreciation under Income Tax Act",
        "Check compliance with Clause 44 of Form 3CD"
    ],
    "Statutory Audit": [
        "Evaluate internal controls",
        "Confirm physical inventory verification",
        "Review board resolutions",
        "Ensure revenue recognition policies are followed"
    ],
    "CARO": [
        "Check fixed asset records and tagging",
        "Verify statutory dues under CARO clause vii",
        "Analyze related party transactions",
        "Evaluate internal audit systems"
    ]
}

def generate_checklist_for_engagement(engagement_type: str) -> list:
    """
    Return a checklist list for a given engagement type.

    Args:
        engagement_type (str): The type of audit engagement

    Returns:
        list: Checklist items
    """
    return DEFAULT_CHECKLIST.get(engagement_type, [])
