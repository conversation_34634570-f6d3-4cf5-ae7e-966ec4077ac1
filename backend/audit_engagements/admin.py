# backend/audit_engagements/admin.py

from django.contrib import admin
from .models import AuditEngagement

@admin.register(AuditEngagement)
class AuditEngagementAdmin(admin.ModelAdmin):
    list_display = ('client', 'engagement_type', 'financial_year', 'start_date', 'end_date', 'is_active')
    list_filter = ('engagement_type', 'financial_year', 'is_active')
    search_fields = ('client__name', 'financial_year')
    date_hierarchy = 'start_date'
