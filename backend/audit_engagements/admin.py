# backend/audit_engagements/admin.py

from django.contrib import admin
from .models import AuditEngagement, EngagementLetter, AuditTeamAssignment, EngagementChecklist

@admin.register(AuditEngagement)
class AuditEngagementAdmin(admin.ModelAdmin):
    list_display = ('engagement_number', 'client', 'engagement_type', 'financial_year', 'planned_start_date', 'planned_end_date', 'status', 'is_active')
    list_filter = ('engagement_type', 'financial_year', 'status', 'priority', 'is_active')
    search_fields = ('engagement_number', 'client__name', 'financial_year')
    date_hierarchy = 'planned_start_date'
    readonly_fields = ('created_at', 'updated_at')
    filter_horizontal = ('team_members',)

@admin.register(EngagementLetter)
class EngagementLetterAdmin(admin.ModelAdmin):
    list_display = ('letter_number', 'engagement', 'status', 'issue_date', 'validity_date')
    list_filter = ('status', 'issue_date')
    search_fields = ('letter_number', 'engagement__engagement_number', 'subject')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(AuditTeamAssignment)
class AuditTeamAssignmentAdmin(admin.ModelAdmin):
    list_display = ('engagement', 'team_member', 'role', 'assigned_date', 'is_active')
    list_filter = ('role', 'assigned_date', 'is_active')
    search_fields = ('engagement__engagement_number', 'team_member__username', 'team_member__first_name', 'team_member__last_name')

@admin.register(EngagementChecklist)
class EngagementChecklistAdmin(admin.ModelAdmin):
    list_display = ('engagement', 'item_number', 'description', 'category', 'assigned_to', 'is_completed')
    list_filter = ('category', 'is_completed', 'assigned_to')
    search_fields = ('engagement__engagement_number', 'item_number', 'description')
    readonly_fields = ('created_at', 'updated_at')
