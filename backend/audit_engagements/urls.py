"""urls.py - Audit Engagement API routing"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    AuditEngagementViewSet, 
    EngagementLetterViewSet,
    AuditTeamAssignmentViewSet,
    EngagementChecklistViewSet
)

router = DefaultRouter()
router.register(r'engagements', AuditEngagementViewSet)
router.register(r'letters', EngagementLetterViewSet)
router.register(r'team-assignments', AuditTeamAssignmentViewSet)
router.register(r'checklist', EngagementChecklistViewSet)

urlpatterns = [
    path('', include(router.urls)),
]
