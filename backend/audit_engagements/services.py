"""services.py - Service layer for audit engagement logic"""

from .models import AuditEngagement
from .checklist_generator import generate_checklist_for_engagement

def create_engagement_checklist(engagement: AuditEngagement) -> dict:
    """
    Generate a checklist and return it in a structured dict.

    Args:
        engagement (AuditEngagement): An instance of AuditEngagement

    Returns:
        dict: Dictionary containing engagement metadata and checklist
    """
    checklist_items = generate_checklist_for_engagement(engagement.engagement_type)
    return {
        "client": engagement.client.name,
        "engagement_type": engagement.engagement_type,
        "financial_year": engagement.financial_year,
        "checklist": checklist_items
    }
