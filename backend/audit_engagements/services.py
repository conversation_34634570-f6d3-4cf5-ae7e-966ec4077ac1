"""services.py - Enhanced service layer for audit engagement logic"""

from django.utils import timezone
from .models import AuditEngagement, EngagementChecklist, AuditTeamAssignment
from .checklist_generator import DEFAULT_CHECKLIST

def create_engagement_checklist(engagement: AuditEngagement) -> dict:
    """
    Generate a checklist and return it in a structured dict.

    Args:
        engagement (AuditEngagement): An instance of AuditEngagement

    Returns:
        dict: Dictionary containing engagement metadata and checklist
    """
    checklist_items = generate_checklist_for_engagement(engagement.engagement_type)
    return {
        "client": engagement.client.name,
        "engagement_type": engagement.engagement_type,
        "financial_year": engagement.financial_year,
        "checklist": checklist_items
    }

def generate_checklist_for_engagement(engagement_type: str) -> list:
    """
    Generate checklist items based on engagement type.

    Args:
        engagement_type (str): Type of audit engagement

    Returns:
        list: List of checklist items
    """
    return DEFAULT_CHECKLIST.get(engagement_type, [])

def generate_engagement_checklist(engagement: AuditEngagement) -> list:
    """
    Generate and save comprehensive checklist items for an engagement.

    Args:
        engagement (AuditEngagement): The engagement to generate checklist for

    Returns:
        list: List of created checklist items
    """
    # Clear existing checklist items
    engagement.checklist_items.all().delete()

    # Define comprehensive checklist based on engagement type and client characteristics
    checklist_templates = get_checklist_templates(engagement)

    created_items = []
    for template in checklist_templates:
        item = EngagementChecklist.objects.create(
            engagement=engagement,
            category=template['category'],
            item_number=template['item_number'],
            description=template['description'],
            reference_standard=template.get('reference_standard', ''),
        )
        created_items.append({
            'id': item.id,
            'category': item.category,
            'item_number': item.item_number,
            'description': item.description,
            'reference_standard': item.reference_standard
        })

    return created_items

def get_checklist_templates(engagement: AuditEngagement) -> list:
    """
    Get checklist templates based on engagement type and client characteristics.

    Args:
        engagement (AuditEngagement): The engagement

    Returns:
        list: List of checklist template dictionaries
    """
    templates = []

    # Common planning items for all engagements
    templates.extend([
        {
            'category': 'PLANNING',
            'item_number': 'P.1',
            'description': 'Obtain understanding of client and its environment',
            'reference_standard': 'SA 315'
        },
        {
            'category': 'PLANNING',
            'item_number': 'P.2',
            'description': 'Assess materiality for the financial statements as a whole',
            'reference_standard': 'SA 320'
        },
        {
            'category': 'PLANNING',
            'item_number': 'P.3',
            'description': 'Identify and assess risks of material misstatement',
            'reference_standard': 'SA 315'
        },
    ])

    # Risk assessment items
    templates.extend([
        {
            'category': 'RISK_ASSESSMENT',
            'item_number': 'R.1',
            'description': 'Evaluate design and implementation of internal controls',
            'reference_standard': 'SA 315'
        },
        {
            'category': 'RISK_ASSESSMENT',
            'item_number': 'R.2',
            'description': 'Assess fraud risks and management override controls',
            'reference_standard': 'SA 240'
        },
    ])

    # Engagement-specific items
    if engagement.engagement_type == 'STATUTORY_AUDIT':
        templates.extend(get_statutory_audit_items())
    elif engagement.engagement_type == 'TAX_AUDIT':
        templates.extend(get_tax_audit_items())
    elif engagement.engagement_type == 'CARO_AUDIT':
        templates.extend(get_caro_audit_items())
    elif engagement.engagement_type == 'INTERNAL_AUDIT':
        templates.extend(get_internal_audit_items())
    elif engagement.engagement_type == 'GST_AUDIT':
        templates.extend(get_gst_audit_items())

    # Client-specific items based on industry and entity type
    if engagement.client.industry == 'MANUFACTURING':
        templates.extend(get_manufacturing_specific_items())
    elif engagement.client.industry == 'IT_SOFTWARE':
        templates.extend(get_it_specific_items())

    # Company-specific items
    if engagement.client.is_company:
        templates.extend(get_company_specific_items())

    return templates

def get_statutory_audit_items() -> list:
    """Get statutory audit specific checklist items"""
    return [
        {
            'category': 'SUBSTANTIVE_TESTING',
            'item_number': 'S.1',
            'description': 'Test revenue recognition and cutoff procedures',
            'reference_standard': 'SA 315'
        },
        {
            'category': 'SUBSTANTIVE_TESTING',
            'item_number': 'S.2',
            'description': 'Verify inventory valuation and existence',
            'reference_standard': 'SA 501'
        },
        {
            'category': 'COMPLETION',
            'item_number': 'C.1',
            'description': 'Review subsequent events',
            'reference_standard': 'SA 560'
        },
        {
            'category': 'REPORTING',
            'item_number': 'REP.1',
            'description': 'Prepare audit report in accordance with SA 700',
            'reference_standard': 'SA 700'
        },
    ]

def get_tax_audit_items() -> list:
    """Get tax audit (44AB) specific checklist items"""
    return [
        {
            'category': 'SUBSTANTIVE_TESTING',
            'item_number': 'T.1',
            'description': 'Verify books of accounts maintenance under Section 44AA',
            'reference_standard': 'Section 44AA'
        },
        {
            'category': 'SUBSTANTIVE_TESTING',
            'item_number': 'T.2',
            'description': 'Match TDS deductions with Form 26AS',
            'reference_standard': 'Form 3CD Clause 26'
        },
        {
            'category': 'SUBSTANTIVE_TESTING',
            'item_number': 'T.3',
            'description': 'Verify depreciation claimed as per Income Tax Act',
            'reference_standard': 'Form 3CD Clause 18'
        },
        {
            'category': 'REPORTING',
            'item_number': 'T.4',
            'description': 'Prepare Form 3CD report',
            'reference_standard': 'Form 3CD'
        },
    ]

def get_caro_audit_items() -> list:
    """Get CARO audit specific checklist items"""
    return [
        {
            'category': 'SUBSTANTIVE_TESTING',
            'item_number': 'CARO.1',
            'description': 'Verify fixed assets records and physical verification',
            'reference_standard': 'CARO Clause (i)'
        },
        {
            'category': 'SUBSTANTIVE_TESTING',
            'item_number': 'CARO.2',
            'description': 'Check statutory dues deposits and defaults',
            'reference_standard': 'CARO Clause (vii)'
        },
        {
            'category': 'SUBSTANTIVE_TESTING',
            'item_number': 'CARO.3',
            'description': 'Review related party transactions',
            'reference_standard': 'CARO Clause (xiii)'
        },
    ]

def get_internal_audit_items() -> list:
    """Get internal audit specific checklist items"""
    return [
        {
            'category': 'INTERNAL_CONTROLS',
            'item_number': 'IA.1',
            'description': 'Evaluate adequacy of internal control systems',
            'reference_standard': 'Internal Audit Standards'
        },
        {
            'category': 'INTERNAL_CONTROLS',
            'item_number': 'IA.2',
            'description': 'Test compliance with company policies',
            'reference_standard': 'Company Policies'
        },
    ]

def get_gst_audit_items() -> list:
    """Get GST audit specific checklist items"""
    return [
        {
            'category': 'SUBSTANTIVE_TESTING',
            'item_number': 'GST.1',
            'description': 'Reconcile GSTR-2B with purchase register',
            'reference_standard': 'GST Audit Manual'
        },
        {
            'category': 'SUBSTANTIVE_TESTING',
            'item_number': 'GST.2',
            'description': 'Verify input tax credit claims',
            'reference_standard': 'CGST Act Section 16'
        },
    ]

def get_manufacturing_specific_items() -> list:
    """Get manufacturing industry specific items"""
    return [
        {
            'category': 'SUBSTANTIVE_TESTING',
            'item_number': 'MFG.1',
            'description': 'Verify raw material consumption and wastage',
            'reference_standard': 'Industry Practice'
        },
        {
            'category': 'SUBSTANTIVE_TESTING',
            'item_number': 'MFG.2',
            'description': 'Test work-in-progress valuation',
            'reference_standard': 'AS 2'
        },
    ]

def get_it_specific_items() -> list:
    """Get IT industry specific items"""
    return [
        {
            'category': 'SUBSTANTIVE_TESTING',
            'item_number': 'IT.1',
            'description': 'Verify software development costs capitalization',
            'reference_standard': 'AS 26'
        },
        {
            'category': 'SUBSTANTIVE_TESTING',
            'item_number': 'IT.2',
            'description': 'Test revenue recognition for software licenses',
            'reference_standard': 'AS 9'
        },
    ]

def get_company_specific_items() -> list:
    """Get company (corporate) specific items"""
    return [
        {
            'category': 'COMPLETION',
            'item_number': 'CORP.1',
            'description': 'Review board resolutions and minutes',
            'reference_standard': 'Companies Act 2013'
        },
        {
            'category': 'COMPLETION',
            'item_number': 'CORP.2',
            'description': 'Verify compliance with Companies Act provisions',
            'reference_standard': 'Companies Act 2013'
        },
    ]

def generate_engagement_number(engagement_type: str, financial_year: str, client_name: str) -> str:
    """
    Generate a unique engagement number.

    Args:
        engagement_type (str): Type of engagement
        financial_year (str): Financial year
        client_name (str): Client name

    Returns:
        str: Generated engagement number
    """
    # Get type prefix
    type_prefixes = {
        'STATUTORY_AUDIT': 'SA',
        'TAX_AUDIT': 'TA',
        'CARO_AUDIT': 'CARO',
        'INTERNAL_AUDIT': 'IA',
        'GST_AUDIT': 'GST',
    }

    prefix = type_prefixes.get(engagement_type, 'ENG')
    year_suffix = financial_year.split('-')[0][-2:]  # Last 2 digits of start year

    # Get next sequence number for this type and year
    existing_count = AuditEngagement.objects.filter(
        engagement_type=engagement_type,
        financial_year=financial_year
    ).count()

    sequence = str(existing_count + 1).zfill(3)

    return f"{prefix}-{year_suffix}-{sequence}"

def calculate_materiality(annual_turnover: float, net_profit: float = None) -> dict:
    """
    Calculate materiality amounts for audit planning.

    Args:
        annual_turnover (float): Annual turnover
        net_profit (float): Net profit (optional)

    Returns:
        dict: Materiality calculations
    """
    # Basic materiality calculation (5% of net profit or 0.5% of turnover)
    if net_profit and net_profit > 0:
        overall_materiality = net_profit * 0.05
    else:
        overall_materiality = annual_turnover * 0.005

    performance_materiality = overall_materiality * 0.75
    trivial_threshold = overall_materiality * 0.05

    return {
        'overall_materiality': round(overall_materiality, 2),
        'performance_materiality': round(performance_materiality, 2),
        'trivial_threshold': round(trivial_threshold, 2),
        'basis': 'Net Profit' if net_profit and net_profit > 0 else 'Turnover'
    }
