"""caro_generator.py - Generate CARO 2020 audit reporting flags"""

def generate_caro_report(inputs: dict) -> dict:
    """
    Generate CARO clause-wise summary.

    Args:
        inputs (dict): Input compliance flags

    Returns:
        dict: CARO clause summary
    """
    return {
        "Clause iii": "No adverse remarks on investments in firms/LLPs.",
        "Clause vii": "Company is regular in depositing statutory dues." if inputs.get("statutory_dues", True) else "Statutory dues pending.",
        "Clause ix": "Company has not defaulted in repayment of loans.",
        "Clause xiv": "Internal audit system is adequate." if inputs.get("internal_audit", True) else "Internal audit mechanism is missing."
    }
