"""tax_audit_3cd.py - Generate tax audit (Form 3CD) section-wise summary"""

def generate_3cd_summary(financial_data: dict) -> dict:
    """
    Generate key clause highlights for Form 3CD.

    Args:
        financial_data (dict): Input financials

    Returns:
        dict: Clause-wise 3CD compliance data
    """
    return {
        "Clause 18": f"Depreciation claimed: ₹{financial_data.get('depreciation', 0)}",
        "Clause 21(b)": f"Disallowable expenses u/s 40A(3): ₹{financial_data.get('disallowed_expenses', 0)}",
        "Clause 26": f"TDS not deducted: ₹{financial_data.get('tds_non_compliance', 0)}",
        "Clause 44": "GST-wise expenditure details to be verified against GSTR-2B"
    }
