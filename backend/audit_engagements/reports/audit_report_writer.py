"""audit_report_writer.py - Comprehensive audit report generation system"""

import json
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Any, Optional
from django.template.loader import render_to_string
from django.conf import settings
from .tax_audit_3cd import generate_3cd_summary
from .caro_generator import generate_caro_report

class AuditReportGenerator:
    """Comprehensive audit report generation"""

    def __init__(self):
        self.report_templates = {
            'STATUTORY_AUDIT': 'reports/statutory_audit_report.html',
            'TAX_AUDIT': 'reports/tax_audit_report.html',
            'CARO_AUDIT': 'reports/caro_report.html',
            'INTERNAL_AUDIT': 'reports/internal_audit_report.html',
            'GST_AUDIT': 'reports/gst_audit_report.html'
        }

    def generate_comprehensive_report(self, engagement, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate comprehensive audit report for an engagement.

        Args:
            engagement: AuditEngagement instance
            financial_data (Dict[str, Any]): Financial data and findings

        Returns:
            Dict[str, Any]: Complete audit report structure
        """
        report = {
            'engagement_info': self._get_engagement_info(engagement),
            'executive_summary': self._generate_executive_summary(engagement, financial_data),
            'audit_opinion': self._generate_audit_opinion(engagement, financial_data),
            'key_audit_matters': self._identify_key_audit_matters(engagement, financial_data),
            'financial_statements': self._process_financial_statements(financial_data),
            'audit_procedures': self._summarize_audit_procedures(engagement),
            'findings_and_recommendations': self._compile_findings(engagement, financial_data),
            'compliance_summary': self._generate_compliance_summary(engagement, financial_data),
            'annexures': self._generate_annexures(engagement, financial_data),
            'management_letter': self._generate_management_letter(engagement, financial_data),
            'generated_at': datetime.now().isoformat(),
            'report_metadata': self._get_report_metadata(engagement)
        }

        # Add engagement-specific sections
        if engagement.engagement_type == 'TAX_AUDIT':
            report['form_3cd'] = generate_3cd_summary(financial_data)
        elif engagement.engagement_type == 'CARO_AUDIT':
            report['caro_compliance'] = generate_caro_report(financial_data)
        elif engagement.engagement_type == 'STATUTORY_AUDIT':
            report['statutory_compliance'] = self._generate_statutory_compliance(financial_data)
        elif engagement.engagement_type == 'GST_AUDIT':
            report['gst_compliance'] = self._generate_gst_compliance(financial_data)

        return report

    def _get_engagement_info(self, engagement) -> Dict[str, Any]:
        """Get engagement basic information"""
        return {
            'engagement_number': engagement.engagement_number,
            'client_name': engagement.client.name,
            'client_pan': engagement.client.pan_number,
            'client_gstin': engagement.client.gst_number,
            'engagement_type': engagement.engagement_type,
            'financial_year': engagement.financial_year,
            'period_start': engagement.period_start.isoformat(),
            'period_end': engagement.period_end.isoformat(),
            'engagement_partner': engagement.engagement_partner.get_full_name() if engagement.engagement_partner else None,
            'audit_manager': engagement.audit_manager.get_full_name() if engagement.audit_manager else None,
            'report_date': datetime.now().date().isoformat()
        }

    def _generate_executive_summary(self, engagement, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate executive summary"""
        return {
            'scope_of_audit': engagement.scope_of_work,
            'audit_approach': self._get_audit_approach(engagement),
            'key_findings_count': len(financial_data.get('findings', [])),
            'material_misstatements': len([f for f in financial_data.get('findings', []) if f.get('materiality') == 'HIGH']),
            'overall_assessment': self._get_overall_assessment(financial_data),
            'recommendations_count': len(financial_data.get('recommendations', []))
        }

    def _generate_audit_opinion(self, engagement, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate audit opinion based on findings"""
        findings = financial_data.get('findings', [])
        material_issues = [f for f in findings if f.get('materiality') == 'HIGH']

        if not material_issues:
            opinion_type = 'UNQUALIFIED'
            opinion_text = "In our opinion, the financial statements present fairly, in all material respects, the financial position of the company."
        elif len(material_issues) <= 2:
            opinion_type = 'QUALIFIED'
            opinion_text = "In our opinion, except for the effects of the matters described in the Basis for Qualified Opinion section, the financial statements present fairly..."
        else:
            opinion_type = 'ADVERSE'
            opinion_text = "In our opinion, because of the significance of the matters described in the Basis for Adverse Opinion section, the financial statements do not present fairly..."

        return {
            'opinion_type': opinion_type,
            'opinion_text': opinion_text,
            'basis_for_opinion': self._get_basis_for_opinion(material_issues),
            'material_issues': material_issues
        }

    def _identify_key_audit_matters(self, engagement, financial_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify and describe key audit matters"""
        key_matters = []

        # Revenue recognition
        if financial_data.get('revenue_amount', 0) > 10000000:  # 1 Crore
            key_matters.append({
                'matter': 'Revenue Recognition',
                'description': 'Revenue recognition is a key audit matter due to the significant judgment involved in determining the timing and amount of revenue to be recognized.',
                'audit_response': 'We performed detailed testing of revenue transactions, reviewed contracts, and tested cut-off procedures.',
                'risk_level': 'HIGH'
            })

        # Inventory valuation
        if financial_data.get('inventory_amount', 0) > 5000000:  # 50 Lakh
            key_matters.append({
                'matter': 'Inventory Valuation',
                'description': 'Inventory represents a significant portion of total assets and involves management estimates.',
                'audit_response': 'We attended physical inventory counts, tested inventory pricing, and reviewed obsolescence provisions.',
                'risk_level': 'MEDIUM'
            })

        # Related party transactions
        if financial_data.get('related_party_transactions', []):
            key_matters.append({
                'matter': 'Related Party Transactions',
                'description': 'The company has significant transactions with related parties that require special audit attention.',
                'audit_response': 'We reviewed all related party transactions for proper authorization, pricing, and disclosure.',
                'risk_level': 'MEDIUM'
            })

        return key_matters

    def _process_financial_statements(self, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process and summarize financial statements"""
        return {
            'balance_sheet_summary': {
                'total_assets': financial_data.get('total_assets', 0),
                'current_assets': financial_data.get('current_assets', 0),
                'fixed_assets': financial_data.get('fixed_assets', 0),
                'total_liabilities': financial_data.get('total_liabilities', 0),
                'current_liabilities': financial_data.get('current_liabilities', 0),
                'equity': financial_data.get('equity', 0)
            },
            'profit_loss_summary': {
                'revenue': financial_data.get('revenue', 0),
                'gross_profit': financial_data.get('gross_profit', 0),
                'operating_expenses': financial_data.get('operating_expenses', 0),
                'net_profit': financial_data.get('net_profit', 0),
                'tax_expense': financial_data.get('tax_expense', 0)
            },
            'key_ratios': self._calculate_key_ratios(financial_data)
        }

    def _calculate_key_ratios(self, financial_data: Dict[str, Any]) -> Dict[str, float]:
        """Calculate key financial ratios"""
        ratios = {}

        # Liquidity ratios
        current_assets = financial_data.get('current_assets', 0)
        current_liabilities = financial_data.get('current_liabilities', 0)
        if current_liabilities > 0:
            ratios['current_ratio'] = round(current_assets / current_liabilities, 2)

        # Profitability ratios
        revenue = financial_data.get('revenue', 0)
        net_profit = financial_data.get('net_profit', 0)
        if revenue > 0:
            ratios['net_profit_margin'] = round((net_profit / revenue) * 100, 2)

        # Leverage ratios
        total_assets = financial_data.get('total_assets', 0)
        total_liabilities = financial_data.get('total_liabilities', 0)
        if total_assets > 0:
            ratios['debt_to_assets'] = round((total_liabilities / total_assets) * 100, 2)

        return ratios

    def _summarize_audit_procedures(self, engagement) -> List[Dict[str, Any]]:
        """Summarize audit procedures performed"""
        procedures = []

        # Get completed checklist items
        checklist_items = engagement.checklist_items.filter(is_completed=True)

        for item in checklist_items:
            procedures.append({
                'category': item.category,
                'procedure': item.description,
                'reference': item.work_paper_reference or 'N/A',
                'completed_by': item.assigned_to.get_full_name() if item.assigned_to else 'N/A',
                'completion_date': item.completion_date.date().isoformat() if item.completion_date else None
            })

        return procedures

    def _compile_findings(self, engagement, financial_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Compile audit findings and recommendations"""
        findings = []

        # Get findings from anomaly flags
        anomaly_flags = []
        for document in engagement.documents.all():
            anomaly_flags.extend(document.anomaly_flags.filter(is_resolved=False))

        for flag in anomaly_flags:
            findings.append({
                'finding_type': 'ANOMALY',
                'description': flag.description,
                'severity': flag.severity,
                'affected_area': flag.affected_field,
                'recommendation': f"Review and resolve {flag.anomaly_type.lower().replace('_', ' ')}",
                'management_response': None,
                'status': 'OPEN'
            })

        # Add manual findings from financial_data
        for finding in financial_data.get('findings', []):
            findings.append({
                'finding_type': 'AUDIT_FINDING',
                'description': finding.get('description', ''),
                'severity': finding.get('materiality', 'MEDIUM'),
                'affected_area': finding.get('area', ''),
                'recommendation': finding.get('recommendation', ''),
                'management_response': finding.get('management_response', ''),
                'status': finding.get('status', 'OPEN')
            })

        return findings

    def _generate_compliance_summary(self, engagement, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate compliance summary"""
        compliance = {
            'companies_act_compliance': True,
            'income_tax_compliance': True,
            'gst_compliance': True,
            'other_statutory_compliance': True,
            'non_compliance_items': []
        }

        # Check for compliance issues
        findings = financial_data.get('findings', [])
        for finding in findings:
            if 'compliance' in finding.get('description', '').lower():
                compliance['non_compliance_items'].append({
                    'law': finding.get('law', 'General'),
                    'description': finding.get('description', ''),
                    'impact': finding.get('impact', 'Medium')
                })

        return compliance

    def _generate_annexures(self, engagement, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate report annexures"""
        return {
            'annexure_a': 'Management Representation Letter',
            'annexure_b': 'List of Books and Records Examined',
            'annexure_c': 'Summary of Audit Adjustments',
            'annexure_d': 'Details of Related Party Transactions',
            'annexure_e': 'Subsequent Events Review'
        }

    def _generate_management_letter(self, engagement, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate management letter with recommendations"""
        recommendations = []

        # Internal control recommendations
        recommendations.append({
            'area': 'Internal Controls',
            'observation': 'Strengthen segregation of duties in cash handling',
            'recommendation': 'Implement proper authorization levels and independent verification',
            'priority': 'HIGH',
            'timeline': '3 months'
        })

        # Process improvement recommendations
        recommendations.append({
            'area': 'Process Improvement',
            'observation': 'Manual reconciliation processes are time-consuming',
            'recommendation': 'Consider automation of bank reconciliation process',
            'priority': 'MEDIUM',
            'timeline': '6 months'
        })

        return {
            'recommendations': recommendations,
            'follow_up_required': True,
            'next_review_date': None
        }

    def _get_report_metadata(self, engagement) -> Dict[str, Any]:
        """Get report metadata"""
        return {
            'report_version': '1.0',
            'template_version': '2024.1',
            'generated_by': 'AuditSmartAI',
            'audit_standards': ['SA 700', 'SA 701', 'SA 720'],
            'report_format': 'Comprehensive',
            'page_count': 0,  # Will be calculated after rendering
            'appendices_count': 5
        }

    def _get_audit_approach(self, engagement) -> str:
        """Get audit approach description"""
        if engagement.overall_risk_level == 'HIGH':
            return "Risk-based audit approach with extensive substantive testing"
        elif engagement.overall_risk_level == 'MEDIUM':
            return "Balanced approach combining controls testing and substantive procedures"
        else:
            return "Controls reliance approach with targeted substantive testing"

    def _get_overall_assessment(self, financial_data: Dict[str, Any]) -> str:
        """Get overall assessment"""
        findings = financial_data.get('findings', [])
        high_findings = [f for f in findings if f.get('materiality') == 'HIGH']

        if not high_findings:
            return "SATISFACTORY"
        elif len(high_findings) <= 2:
            return "NEEDS_IMPROVEMENT"
        else:
            return "UNSATISFACTORY"

    def _get_basis_for_opinion(self, material_issues: List[Dict[str, Any]]) -> str:
        """Get basis for opinion text"""
        if not material_issues:
            return "We conducted our audit in accordance with Standards on Auditing. Our responsibilities under those standards are further described in the Auditor's Responsibilities section."
        else:
            return f"The following matters have been identified that affect our opinion: {'; '.join([issue.get('description', '') for issue in material_issues])}"

    def _generate_statutory_compliance(self, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate statutory audit specific compliance"""
        return {
            'companies_act_sections': {
                'section_128': 'Books of accounts maintained properly',
                'section_129': 'Financial statements prepared as per Schedule III',
                'section_134': 'Board report contains required disclosures',
                'section_143': 'Auditor report requirements complied with'
            },
            'compliance_status': 'COMPLIANT',
            'exceptions': []
        }

    def _generate_gst_compliance(self, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate GST audit specific compliance"""
        return {
            'gst_returns_filed': True,
            'input_tax_credit_claimed': financial_data.get('itc_claimed', 0),
            'output_tax_liability': financial_data.get('output_tax', 0),
            'gst_paid': financial_data.get('gst_paid', 0),
            'reconciliation_status': 'COMPLETED',
            'discrepancies': financial_data.get('gst_discrepancies', [])
        }

# Legacy function for backward compatibility
def generate_full_engagement_report(engagement_type: str, data: dict) -> dict:
    """
    Generate full engagement report (CARO + 3CD + custom).

    Args:
        engagement_type (str): Type of audit (Tax Audit / CARO etc.)
        data (dict): Financial and compliance input

    Returns:
        dict: Structured audit report
    """
    report = {"engagement_type": engagement_type, "sections": {}}

    if engagement_type == "Tax Audit":
        report["sections"]["Form 3CD Summary"] = generate_3cd_summary(data)
    elif engagement_type == "CARO":
        report["sections"]["CARO Compliance"] = generate_caro_report(data)

    report["summary"] = "Report generated based on system analysis."
    return report
