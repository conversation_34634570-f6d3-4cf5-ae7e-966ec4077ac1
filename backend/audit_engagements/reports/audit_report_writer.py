"""audit_report_writer.py - Consolidates multiple reports for a given engagement"""

from .tax_audit_3cd import generate_3cd_summary
from .caro_generator import generate_caro_report

def generate_full_engagement_report(engagement_type: str, data: dict) -> dict:
    """
    Generate full engagement report (CARO + 3CD + custom).

    Args:
        engagement_type (str): Type of audit (Tax Audit / CARO etc.)
        data (dict): Financial and compliance input

    Returns:
        dict: Structured audit report
    """
    report = {"engagement_type": engagement_type, "sections": {}}

    if engagement_type == "Tax Audit":
        report["sections"]["Form 3CD Summary"] = generate_3cd_summary(data)
    elif engagement_type == "CARO":
        report["sections"]["CARO Compliance"] = generate_caro_report(data)

    report["summary"] = "Report generated based on system analysis."
    return report
