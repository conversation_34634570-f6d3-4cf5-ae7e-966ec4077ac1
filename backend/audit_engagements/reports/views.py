"""views.py - Audit Report generation API views"""

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.http import HttpResponse
from django.template.loader import render_to_string
from django.utils import timezone
from audit_engagements.models import AuditEngagement
from .audit_report_writer import AuditReportGenerator
from .serializers import AuditReportRequestSerializer, AuditReportResponseSerializer
import json
import io
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib import colors
from reportlab.lib.units import inch

class AuditReportViewSet(viewsets.ViewSet):
    """ViewSet for audit report generation and management"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.report_generator = AuditReportGenerator()
    
    @action(detail=False, methods=['post'])
    def generate_report(self, request):
        """Generate comprehensive audit report"""
        serializer = AuditReportRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        engagement_id = serializer.validated_data['engagement_id']
        financial_data = serializer.validated_data.get('financial_data', {})
        report_format = serializer.validated_data.get('format', 'JSON')
        
        try:
            engagement = AuditEngagement.objects.get(id=engagement_id)
        except AuditEngagement.DoesNotExist:
            return Response({'error': 'Engagement not found'}, status=status.HTTP_404_NOT_FOUND)
        
        # Generate comprehensive report
        report_data = self.report_generator.generate_comprehensive_report(engagement, financial_data)
        
        if report_format == 'PDF':
            # Generate PDF report
            pdf_content = self._generate_pdf_report(report_data)
            response = HttpResponse(pdf_content, content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="audit_report_{engagement.engagement_number}.pdf"'
            return response
        
        elif report_format == 'WORD':
            # Generate Word document (placeholder)
            return Response({'message': 'Word format not yet implemented'}, status=status.HTTP_501_NOT_IMPLEMENTED)
        
        else:
            # Return JSON format
            response_serializer = AuditReportResponseSerializer(data=report_data)
            if response_serializer.is_valid():
                return Response(response_serializer.validated_data)
            else:
                return Response(report_data)
    
    @action(detail=False, methods=['get'])
    def engagement_reports(self, request):
        """Get available reports for an engagement"""
        engagement_id = request.query_params.get('engagement_id')
        if not engagement_id:
            return Response({'error': 'engagement_id parameter required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            engagement = AuditEngagement.objects.get(id=engagement_id)
        except AuditEngagement.DoesNotExist:
            return Response({'error': 'Engagement not found'}, status=status.HTTP_404_NOT_FOUND)
        
        available_reports = self._get_available_reports(engagement)
        return Response(available_reports)
    
    @action(detail=False, methods=['post'])
    def generate_3cd(self, request):
        """Generate Form 3CD report"""
        engagement_id = request.data.get('engagement_id')
        financial_data = request.data.get('financial_data', {})
        
        if not engagement_id:
            return Response({'error': 'engagement_id required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            engagement = AuditEngagement.objects.get(id=engagement_id)
        except AuditEngagement.DoesNotExist:
            return Response({'error': 'Engagement not found'}, status=status.HTTP_404_NOT_FOUND)
        
        from .tax_audit_3cd import generate_3cd_summary
        report = generate_3cd_summary(financial_data)
        
        return Response({
            'engagement': engagement.engagement_number,
            'report_type': 'Form 3CD',
            'generated_at': timezone.now().isoformat(),
            'report_data': report
        })
    
    @action(detail=False, methods=['post'])
    def generate_caro(self, request):
        """Generate CARO report"""
        engagement_id = request.data.get('engagement_id')
        compliance_data = request.data.get('compliance_data', {})
        
        if not engagement_id:
            return Response({'error': 'engagement_id required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            engagement = AuditEngagement.objects.get(id=engagement_id)
        except AuditEngagement.DoesNotExist:
            return Response({'error': 'Engagement not found'}, status=status.HTTP_404_NOT_FOUND)
        
        from .caro_generator import generate_caro_report
        report = generate_caro_report(compliance_data)
        
        return Response({
            'engagement': engagement.engagement_number,
            'report_type': 'CARO 2020',
            'generated_at': timezone.now().isoformat(),
            'report_data': report
        })
    
    @action(detail=False, methods=['get'])
    def report_templates(self, request):
        """Get available report templates"""
        templates = {
            'STATUTORY_AUDIT': {
                'name': 'Statutory Audit Report',
                'description': 'Standard statutory audit report as per Companies Act',
                'sections': ['Opinion', 'Basis for Opinion', 'Key Audit Matters', 'Other Information']
            },
            'TAX_AUDIT': {
                'name': 'Tax Audit Report',
                'description': 'Tax audit report with Form 3CD',
                'sections': ['Form 3CD', 'Tax Compliance', 'Disallowances', 'TDS Verification']
            },
            'CARO_AUDIT': {
                'name': 'CARO Report',
                'description': 'Companies (Auditor Report) Order 2020',
                'sections': ['CARO Clauses', 'Compliance Status', 'Exceptions', 'Recommendations']
            },
            'INTERNAL_AUDIT': {
                'name': 'Internal Audit Report',
                'description': 'Internal audit findings and recommendations',
                'sections': ['Executive Summary', 'Findings', 'Recommendations', 'Management Response']
            }
        }
        
        return Response(templates)
    
    @action(detail=False, methods=['post'])
    def preview_report(self, request):
        """Generate report preview"""
        engagement_id = request.data.get('engagement_id')
        sections = request.data.get('sections', [])
        
        if not engagement_id:
            return Response({'error': 'engagement_id required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            engagement = AuditEngagement.objects.get(id=engagement_id)
        except AuditEngagement.DoesNotExist:
            return Response({'error': 'Engagement not found'}, status=status.HTTP_404_NOT_FOUND)
        
        # Generate preview with selected sections
        preview_data = self._generate_report_preview(engagement, sections)
        
        return Response(preview_data)
    
    def _generate_pdf_report(self, report_data):
        """Generate PDF report from report data"""
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1  # Center alignment
        )
        
        engagement_info = report_data.get('engagement_info', {})
        title = f"AUDIT REPORT<br/>{engagement_info.get('client_name', 'N/A')}<br/>Financial Year: {engagement_info.get('financial_year', 'N/A')}"
        story.append(Paragraph(title, title_style))
        story.append(Spacer(1, 20))
        
        # Executive Summary
        story.append(Paragraph("EXECUTIVE SUMMARY", styles['Heading2']))
        exec_summary = report_data.get('executive_summary', {})
        story.append(Paragraph(f"Scope of Audit: {exec_summary.get('scope_of_audit', 'N/A')}", styles['Normal']))
        story.append(Paragraph(f"Overall Assessment: {exec_summary.get('overall_assessment', 'N/A')}", styles['Normal']))
        story.append(Spacer(1, 12))
        
        # Audit Opinion
        story.append(Paragraph("AUDIT OPINION", styles['Heading2']))
        opinion = report_data.get('audit_opinion', {})
        story.append(Paragraph(f"Opinion Type: {opinion.get('opinion_type', 'N/A')}", styles['Normal']))
        story.append(Paragraph(opinion.get('opinion_text', 'N/A'), styles['Normal']))
        story.append(Spacer(1, 12))
        
        # Key Audit Matters
        story.append(Paragraph("KEY AUDIT MATTERS", styles['Heading2']))
        key_matters = report_data.get('key_audit_matters', [])
        for matter in key_matters:
            story.append(Paragraph(f"• {matter.get('matter', 'N/A')}: {matter.get('description', 'N/A')}", styles['Normal']))
        story.append(Spacer(1, 12))
        
        # Financial Summary
        story.append(Paragraph("FINANCIAL SUMMARY", styles['Heading2']))
        financial = report_data.get('financial_statements', {})
        if financial:
            # Create financial summary table
            data = [
                ['Particulars', 'Amount (₹)'],
                ['Total Assets', f"{financial.get('balance_sheet_summary', {}).get('total_assets', 0):,.2f}"],
                ['Total Liabilities', f"{financial.get('balance_sheet_summary', {}).get('total_liabilities', 0):,.2f}"],
                ['Revenue', f"{financial.get('profit_loss_summary', {}).get('revenue', 0):,.2f}"],
                ['Net Profit', f"{financial.get('profit_loss_summary', {}).get('net_profit', 0):,.2f}"]
            ]
            
            table = Table(data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(table)
        
        story.append(Spacer(1, 12))
        
        # Findings
        story.append(Paragraph("AUDIT FINDINGS", styles['Heading2']))
        findings = report_data.get('findings_and_recommendations', [])
        for i, finding in enumerate(findings[:5], 1):  # Limit to first 5 findings
            story.append(Paragraph(f"{i}. {finding.get('description', 'N/A')}", styles['Normal']))
            story.append(Paragraph(f"   Recommendation: {finding.get('recommendation', 'N/A')}", styles['Normal']))
            story.append(Spacer(1, 6))
        
        # Build PDF
        doc.build(story)
        buffer.seek(0)
        return buffer.getvalue()
    
    def _get_available_reports(self, engagement):
        """Get list of available reports for engagement"""
        reports = [
            {
                'type': 'COMPREHENSIVE',
                'name': 'Comprehensive Audit Report',
                'description': 'Complete audit report with all sections'
            }
        ]
        
        if engagement.engagement_type == 'TAX_AUDIT':
            reports.append({
                'type': 'FORM_3CD',
                'name': 'Form 3CD Report',
                'description': 'Tax audit report as per Income Tax Act'
            })
        
        if engagement.requires_caro:
            reports.append({
                'type': 'CARO',
                'name': 'CARO 2020 Report',
                'description': 'Companies (Auditor Report) Order 2020'
            })
        
        if engagement.engagement_type == 'INTERNAL_AUDIT':
            reports.append({
                'type': 'INTERNAL_AUDIT',
                'name': 'Internal Audit Report',
                'description': 'Internal audit findings and recommendations'
            })
        
        return reports
    
    def _generate_report_preview(self, engagement, sections):
        """Generate report preview with selected sections"""
        preview = {
            'engagement_number': engagement.engagement_number,
            'client_name': engagement.client.name,
            'report_sections': []
        }
        
        for section in sections:
            if section == 'executive_summary':
                preview['report_sections'].append({
                    'section': 'Executive Summary',
                    'content': 'This section provides an overview of the audit scope, approach, and key findings.'
                })
            elif section == 'audit_opinion':
                preview['report_sections'].append({
                    'section': 'Audit Opinion',
                    'content': 'This section contains the auditor\'s opinion on the financial statements.'
                })
            elif section == 'key_audit_matters':
                preview['report_sections'].append({
                    'section': 'Key Audit Matters',
                    'content': 'This section describes the matters that were of most significance in the audit.'
                })
        
        return preview
