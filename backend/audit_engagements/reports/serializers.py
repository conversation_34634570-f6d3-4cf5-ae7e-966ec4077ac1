"""serializers.py - Audit Report API serializers"""

from rest_framework import serializers

class AuditReportRequestSerializer(serializers.Serializer):
    """Serializer for audit report generation requests"""
    engagement_id = serializers.IntegerField()
    financial_data = serializers.JSONField(required=False, default=dict)
    format = serializers.ChoiceField(
        choices=['JSON', 'PDF', 'WORD'],
        default='JSON'
    )
    sections = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        default=list
    )
    include_annexures = serializers.BooleanField(default=True)
    include_management_letter = serializers.BooleanField(default=True)

class EngagementInfoSerializer(serializers.Serializer):
    """Serializer for engagement information"""
    engagement_number = serializers.CharField()
    client_name = serializers.CharField()
    client_pan = serializers.CharField()
    client_gstin = serializers.CharField(allow_null=True)
    engagement_type = serializers.Char<PERSON><PERSON>()
    financial_year = serializers.CharField()
    period_start = serializers.CharField()
    period_end = serializers.CharField()
    engagement_partner = serializers.CharField(allow_null=True)
    audit_manager = serializers.CharField(allow_null=True)
    report_date = serializers.CharField()

class ExecutiveSummarySerializer(serializers.Serializer):
    """Serializer for executive summary"""
    scope_of_audit = serializers.CharField()
    audit_approach = serializers.CharField()
    key_findings_count = serializers.IntegerField()
    material_misstatements = serializers.IntegerField()
    overall_assessment = serializers.CharField()
    recommendations_count = serializers.IntegerField()

class AuditOpinionSerializer(serializers.Serializer):
    """Serializer for audit opinion"""
    opinion_type = serializers.CharField()
    opinion_text = serializers.CharField()
    basis_for_opinion = serializers.CharField()
    material_issues = serializers.ListField(child=serializers.JSONField())

class KeyAuditMatterSerializer(serializers.Serializer):
    """Serializer for key audit matters"""
    matter = serializers.CharField()
    description = serializers.CharField()
    audit_response = serializers.CharField()
    risk_level = serializers.CharField()

class FinancialRatiosSerializer(serializers.Serializer):
    """Serializer for financial ratios"""
    current_ratio = serializers.FloatField(required=False)
    net_profit_margin = serializers.FloatField(required=False)
    debt_to_assets = serializers.FloatField(required=False)

class BalanceSheetSummarySerializer(serializers.Serializer):
    """Serializer for balance sheet summary"""
    total_assets = serializers.FloatField()
    current_assets = serializers.FloatField()
    fixed_assets = serializers.FloatField()
    total_liabilities = serializers.FloatField()
    current_liabilities = serializers.FloatField()
    equity = serializers.FloatField()

class ProfitLossSummarySerializer(serializers.Serializer):
    """Serializer for profit & loss summary"""
    revenue = serializers.FloatField()
    gross_profit = serializers.FloatField()
    operating_expenses = serializers.FloatField()
    net_profit = serializers.FloatField()
    tax_expense = serializers.FloatField()

class FinancialStatementsSerializer(serializers.Serializer):
    """Serializer for financial statements"""
    balance_sheet_summary = BalanceSheetSummarySerializer()
    profit_loss_summary = ProfitLossSummarySerializer()
    key_ratios = FinancialRatiosSerializer()

class AuditProcedureSerializer(serializers.Serializer):
    """Serializer for audit procedures"""
    category = serializers.CharField()
    procedure = serializers.CharField()
    reference = serializers.CharField()
    completed_by = serializers.CharField()
    completion_date = serializers.CharField(allow_null=True)

class AuditFindingSerializer(serializers.Serializer):
    """Serializer for audit findings"""
    finding_type = serializers.CharField()
    description = serializers.CharField()
    severity = serializers.CharField()
    affected_area = serializers.CharField()
    recommendation = serializers.CharField()
    management_response = serializers.CharField(allow_null=True)
    status = serializers.CharField()

class ComplianceItemSerializer(serializers.Serializer):
    """Serializer for compliance items"""
    law = serializers.CharField()
    description = serializers.CharField()
    impact = serializers.CharField()

class ComplianceSummarySerializer(serializers.Serializer):
    """Serializer for compliance summary"""
    companies_act_compliance = serializers.BooleanField()
    income_tax_compliance = serializers.BooleanField()
    gst_compliance = serializers.BooleanField()
    other_statutory_compliance = serializers.BooleanField()
    non_compliance_items = serializers.ListField(child=ComplianceItemSerializer())

class AnnexuresSerializer(serializers.Serializer):
    """Serializer for report annexures"""
    annexure_a = serializers.CharField()
    annexure_b = serializers.CharField()
    annexure_c = serializers.CharField()
    annexure_d = serializers.CharField()
    annexure_e = serializers.CharField()

class ManagementRecommendationSerializer(serializers.Serializer):
    """Serializer for management recommendations"""
    area = serializers.CharField()
    observation = serializers.CharField()
    recommendation = serializers.CharField()
    priority = serializers.CharField()
    timeline = serializers.CharField()

class ManagementLetterSerializer(serializers.Serializer):
    """Serializer for management letter"""
    recommendations = serializers.ListField(child=ManagementRecommendationSerializer())
    follow_up_required = serializers.BooleanField()
    next_review_date = serializers.CharField(allow_null=True)

class ReportMetadataSerializer(serializers.Serializer):
    """Serializer for report metadata"""
    report_version = serializers.CharField()
    template_version = serializers.CharField()
    generated_by = serializers.CharField()
    audit_standards = serializers.ListField(child=serializers.CharField())
    report_format = serializers.CharField()
    page_count = serializers.IntegerField()
    appendices_count = serializers.IntegerField()

class AuditReportResponseSerializer(serializers.Serializer):
    """Serializer for complete audit report response"""
    engagement_info = EngagementInfoSerializer()
    executive_summary = ExecutiveSummarySerializer()
    audit_opinion = AuditOpinionSerializer()
    key_audit_matters = serializers.ListField(child=KeyAuditMatterSerializer())
    financial_statements = FinancialStatementsSerializer()
    audit_procedures = serializers.ListField(child=AuditProcedureSerializer())
    findings_and_recommendations = serializers.ListField(child=AuditFindingSerializer())
    compliance_summary = ComplianceSummarySerializer()
    annexures = AnnexuresSerializer()
    management_letter = ManagementLetterSerializer()
    generated_at = serializers.CharField()
    report_metadata = ReportMetadataSerializer()
    
    # Optional engagement-specific sections
    form_3cd = serializers.JSONField(required=False)
    caro_compliance = serializers.JSONField(required=False)
    statutory_compliance = serializers.JSONField(required=False)
    gst_compliance = serializers.JSONField(required=False)

class Form3CDRequestSerializer(serializers.Serializer):
    """Serializer for Form 3CD generation requests"""
    engagement_id = serializers.IntegerField()
    financial_data = serializers.JSONField()
    assessment_year = serializers.CharField()
    previous_year = serializers.CharField()

class CARORequestSerializer(serializers.Serializer):
    """Serializer for CARO report generation requests"""
    engagement_id = serializers.IntegerField()
    compliance_data = serializers.JSONField()
    caro_year = serializers.CharField(default='2020')

class ReportTemplateSerializer(serializers.Serializer):
    """Serializer for report templates"""
    name = serializers.CharField()
    description = serializers.CharField()
    sections = serializers.ListField(child=serializers.CharField())

class ReportPreviewRequestSerializer(serializers.Serializer):
    """Serializer for report preview requests"""
    engagement_id = serializers.IntegerField()
    sections = serializers.ListField(child=serializers.CharField())

class ReportPreviewResponseSerializer(serializers.Serializer):
    """Serializer for report preview response"""
    engagement_number = serializers.CharField()
    client_name = serializers.CharField()
    report_sections = serializers.ListField(child=serializers.JSONField())

class BulkReportRequestSerializer(serializers.Serializer):
    """Serializer for bulk report generation"""
    engagement_ids = serializers.ListField(child=serializers.IntegerField())
    report_type = serializers.ChoiceField(choices=['COMPREHENSIVE', 'FORM_3CD', 'CARO'])
    format = serializers.ChoiceField(choices=['JSON', 'PDF'], default='PDF')

class ReportStatusSerializer(serializers.Serializer):
    """Serializer for report generation status"""
    engagement_id = serializers.IntegerField()
    status = serializers.ChoiceField(choices=['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED'])
    progress_percentage = serializers.IntegerField()
    estimated_completion = serializers.CharField(allow_null=True)
    error_message = serializers.CharField(allow_null=True)
