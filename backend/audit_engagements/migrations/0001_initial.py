# Generated by Django 5.2.4 on 2025-07-09 06:04

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("clients", "0002_alter_client_options_client_annual_turnover_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AuditEngagement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "engagement_type",
                    models.CharField(
                        choices=[
                            ("STATUTORY_AUDIT", "Statutory Audit"),
                            ("TAX_AUDIT", "Tax Audit (44AB)"),
                            ("CARO_AUDIT", "CARO Audit"),
                            ("INTERNAL_AUDIT", "Internal Audit"),
                            ("GST_AUDIT", "GST Audit"),
                            ("STOCK_AUDIT", "Stock Audit"),
                            ("CONCURRENT_AUDIT", "Concurrent Audit"),
                            ("FORENSIC_AUDIT", "Forensic Audit"),
                            ("MANAGEMENT_AUDIT", "Management Audit"),
                            ("COMPLIANCE_AUDIT", "Compliance Audit"),
                            ("INFORMATION_SYSTEM_AUDIT", "Information System Audit"),
                            ("ENVIRONMENTAL_AUDIT", "Environmental Audit"),
                        ],
                        max_length=30,
                    ),
                ),
                ("engagement_number", models.CharField(max_length=20, unique=True)),
                ("financial_year", models.CharField(max_length=9)),
                ("period_start", models.DateField()),
                ("period_end", models.DateField()),
                ("planned_start_date", models.DateField()),
                ("planned_end_date", models.DateField()),
                ("actual_start_date", models.DateField(blank=True, null=True)),
                ("actual_end_date", models.DateField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("PLANNING", "Planning"),
                            ("FIELDWORK", "Fieldwork"),
                            ("REVIEW", "Review"),
                            ("REPORTING", "Reporting"),
                            ("COMPLETED", "Completed"),
                            ("ON_HOLD", "On Hold"),
                            ("CANCELLED", "Cancelled"),
                        ],
                        default="PLANNING",
                        max_length=15,
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("LOW", "Low"),
                            ("MEDIUM", "Medium"),
                            ("HIGH", "High"),
                            ("URGENT", "Urgent"),
                        ],
                        default="MEDIUM",
                        max_length=10,
                    ),
                ),
                ("scope_of_work", models.TextField()),
                ("special_instructions", models.TextField(blank=True, null=True)),
                (
                    "materiality_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=15, null=True
                    ),
                ),
                ("budgeted_hours", models.IntegerField(blank=True, null=True)),
                ("actual_hours", models.IntegerField(default=0)),
                (
                    "budget_amount",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                (
                    "overall_risk_level",
                    models.CharField(
                        choices=[
                            ("LOW", "Low"),
                            ("MEDIUM", "Medium"),
                            ("HIGH", "High"),
                        ],
                        default="MEDIUM",
                        max_length=10,
                    ),
                ),
                ("requires_caro", models.BooleanField(default=False)),
                ("requires_3cd", models.BooleanField(default=False)),
                ("requires_3ca_3cb", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "audit_manager",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="manager_engagements",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "audit_senior",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="senior_engagements",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "client",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="engagements",
                        to="clients.client",
                    ),
                ),
                (
                    "engagement_partner",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="partner_engagements",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "team_members",
                    models.ManyToManyField(
                        blank=True,
                        related_name="team_engagements",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="AuditTeamAssignment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("PARTNER", "Engagement Partner"),
                            ("MANAGER", "Audit Manager"),
                            ("SENIOR", "Senior Auditor"),
                            ("JUNIOR", "Junior Auditor"),
                            ("ASSISTANT", "Audit Assistant"),
                            ("SPECIALIST", "Specialist"),
                            ("REVIEWER", "Reviewer"),
                        ],
                        max_length=15,
                    ),
                ),
                ("assigned_date", models.DateField()),
                ("planned_hours", models.IntegerField(blank=True, null=True)),
                ("actual_hours", models.IntegerField(default=0)),
                (
                    "hourly_rate",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=8, null=True
                    ),
                ),
                ("responsibilities", models.TextField(blank=True, null=True)),
                ("areas_assigned", models.TextField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "engagement",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="team_assignments",
                        to="audit_engagements.auditengagement",
                    ),
                ),
                (
                    "team_member",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["role", "assigned_date"],
            },
        ),
        migrations.CreateModel(
            name="EngagementChecklist",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("PLANNING", "Planning"),
                            ("RISK_ASSESSMENT", "Risk Assessment"),
                            ("INTERNAL_CONTROLS", "Internal Controls"),
                            ("SUBSTANTIVE_TESTING", "Substantive Testing"),
                            ("COMPLETION", "Completion"),
                            ("REPORTING", "Reporting"),
                        ],
                        max_length=20,
                    ),
                ),
                ("item_number", models.CharField(max_length=10)),
                ("description", models.TextField()),
                (
                    "reference_standard",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("is_completed", models.BooleanField(default=False)),
                ("completion_date", models.DateTimeField(blank=True, null=True)),
                (
                    "work_paper_reference",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("notes", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "assigned_to",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "engagement",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="checklist_items",
                        to="audit_engagements.auditengagement",
                    ),
                ),
            ],
            options={
                "ordering": ["category", "item_number"],
            },
        ),
        migrations.CreateModel(
            name="EngagementLetter",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("letter_number", models.CharField(max_length=20, unique=True)),
                ("subject", models.CharField(max_length=255)),
                ("content", models.TextField()),
                ("terms_and_conditions", models.TextField()),
                ("issue_date", models.DateField()),
                ("validity_date", models.DateField()),
                ("signed_date", models.DateField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("DRAFT", "Draft"),
                            ("SENT", "Sent to Client"),
                            ("SIGNED", "Signed by Client"),
                            ("EXPIRED", "Expired"),
                        ],
                        default="DRAFT",
                        max_length=10,
                    ),
                ),
                (
                    "letter_file",
                    models.FileField(
                        blank=True, null=True, upload_to="engagement_letters/"
                    ),
                ),
                (
                    "signed_file",
                    models.FileField(
                        blank=True, null=True, upload_to="engagement_letters/signed/"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "engagement",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="engagement_letter",
                        to="audit_engagements.auditengagement",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddIndex(
            model_name="auditengagement",
            index=models.Index(
                fields=["engagement_number"], name="audit_engag_engagem_49cf23_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="auditengagement",
            index=models.Index(
                fields=["financial_year"], name="audit_engag_financi_9ec512_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="auditengagement",
            index=models.Index(fields=["status"], name="audit_engag_status_31b7a2_idx"),
        ),
        migrations.AddIndex(
            model_name="auditengagement",
            index=models.Index(
                fields=["engagement_type"], name="audit_engag_engagem_4de41f_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="auditteamassignment",
            unique_together={("engagement", "team_member", "role")},
        ),
        migrations.AlterUniqueTogether(
            name="engagementchecklist",
            unique_together={("engagement", "item_number")},
        ),
    ]
