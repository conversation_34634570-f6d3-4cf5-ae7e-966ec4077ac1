"""models.py - Defines audit engagement models"""

from django.db import models
from clients.models import Client

class AuditEngagement(models.Model):
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name="engagements")
    engagement_type = models.CharField(max_length=100, choices=[
        ("Tax Audit", "Tax Audit"),
        ("Statutory Audit", "Statutory Audit"),
        ("CARO", "CARO Audit"),
    ])
    financial_year = models.CharField(max_length=9)
    start_date = models.DateField()
    end_date = models.DateField()
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.client.name} - {self.engagement_type} ({self.financial_year})"
