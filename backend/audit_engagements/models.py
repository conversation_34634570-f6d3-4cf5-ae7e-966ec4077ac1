"""models.py - Enhanced audit engagement models for comprehensive management"""

from django.db import models
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from clients.models import Client
from users.models import User

class AuditEngagement(models.Model):
    ENGAGEMENT_TYPE_CHOICES = [
        ('STATUTORY_AUDIT', 'Statutory Audit'),
        ('TAX_AUDIT', 'Tax Audit (44AB)'),
        ('CARO_AUDIT', 'CARO Audit'),
        ('INTERNAL_AUDIT', 'Internal Audit'),
        ('GST_AUDIT', 'GST Audit'),
        ('STOCK_AUDIT', 'Stock Audit'),
        ('CONCURRENT_AUDIT', 'Concurrent Audit'),
        ('FORENSIC_AUDIT', 'Forensic Audit'),
        ('MANAGEMENT_AUDIT', 'Management Audit'),
        ('COMPLIANCE_AUDIT', 'Compliance Audit'),
        ('INFORMATION_SYSTEM_AUDIT', 'Information System Audit'),
        ('ENVIRONMENTAL_AUDIT', 'Environmental Audit'),
    ]

    STATUS_CHOICES = [
        ('PLANNING', 'Planning'),
        ('FIELDWORK', 'Fieldwork'),
        ('REVIEW', 'Review'),
        ('REPORTING', 'Reporting'),
        ('COMPLETED', 'Completed'),
        ('ON_HOLD', 'On Hold'),
        ('CANCELLED', 'Cancelled'),
    ]

    PRIORITY_CHOICES = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('URGENT', 'Urgent'),
    ]

    # Basic Engagement Information
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name="engagements")
    engagement_type = models.CharField(max_length=30, choices=ENGAGEMENT_TYPE_CHOICES)
    engagement_number = models.CharField(max_length=20, unique=True)

    # Financial Year and Dates
    financial_year = models.CharField(max_length=9)  # e.g., "2023-2024"
    period_start = models.DateField()
    period_end = models.DateField()

    # Engagement Timeline
    planned_start_date = models.DateField()
    planned_end_date = models.DateField()
    actual_start_date = models.DateField(blank=True, null=True)
    actual_end_date = models.DateField(blank=True, null=True)

    # Status and Priority
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='PLANNING')
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='MEDIUM')

    # Team Assignment
    engagement_partner = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='partner_engagements'
    )
    audit_manager = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='manager_engagements'
    )
    audit_senior = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, blank=True,
        related_name='senior_engagements'
    )
    team_members = models.ManyToManyField(
        User, blank=True, related_name='team_engagements'
    )

    # Engagement Details
    scope_of_work = models.TextField()
    special_instructions = models.TextField(blank=True, null=True)
    materiality_amount = models.DecimalField(max_digits=15, decimal_places=2, blank=True, null=True)

    # Budget and Hours
    budgeted_hours = models.IntegerField(blank=True, null=True)
    actual_hours = models.IntegerField(default=0)
    budget_amount = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)

    # Risk Assessment
    overall_risk_level = models.CharField(max_length=10, choices=[
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
    ], default='MEDIUM')

    # Compliance Requirements
    requires_caro = models.BooleanField(default=False)
    requires_3cd = models.BooleanField(default=False)
    requires_3ca_3cb = models.BooleanField(default=False)

    # System Fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['engagement_number']),
            models.Index(fields=['financial_year']),
            models.Index(fields=['status']),
            models.Index(fields=['engagement_type']),
        ]

    def __str__(self):
        return f"{self.engagement_number} - {self.client.name} ({self.engagement_type})"

    @property
    def is_overdue(self):
        from django.utils import timezone
        if self.status != 'COMPLETED' and self.planned_end_date:
            return timezone.now().date() > self.planned_end_date
        return False

    @property
    def completion_percentage(self):
        # This will be calculated based on checklist completion
        # For now, return a basic calculation
        if self.status == 'COMPLETED':
            return 100
        elif self.status == 'REPORTING':
            return 80
        elif self.status == 'REVIEW':
            return 60
        elif self.status == 'FIELDWORK':
            return 40
        elif self.status == 'PLANNING':
            return 20
        return 0


class EngagementLetter(models.Model):
    """Model for managing engagement letters"""

    STATUS_CHOICES = [
        ('DRAFT', 'Draft'),
        ('SENT', 'Sent to Client'),
        ('SIGNED', 'Signed by Client'),
        ('EXPIRED', 'Expired'),
    ]

    engagement = models.OneToOneField(AuditEngagement, on_delete=models.CASCADE, related_name='engagement_letter')
    letter_number = models.CharField(max_length=20, unique=True)

    # Letter Content
    subject = models.CharField(max_length=255)
    content = models.TextField()
    terms_and_conditions = models.TextField()

    # Dates
    issue_date = models.DateField()
    validity_date = models.DateField()
    signed_date = models.DateField(blank=True, null=True)

    # Status
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='DRAFT')

    # File Attachments
    letter_file = models.FileField(upload_to='engagement_letters/', blank=True, null=True)
    signed_file = models.FileField(upload_to='engagement_letters/signed/', blank=True, null=True)

    # System Fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Engagement Letter - {self.letter_number}"


class AuditTeamAssignment(models.Model):
    """Model for detailed team member assignments with roles and responsibilities"""

    ROLE_CHOICES = [
        ('PARTNER', 'Engagement Partner'),
        ('MANAGER', 'Audit Manager'),
        ('SENIOR', 'Senior Auditor'),
        ('JUNIOR', 'Junior Auditor'),
        ('ASSISTANT', 'Audit Assistant'),
        ('SPECIALIST', 'Specialist'),
        ('REVIEWER', 'Reviewer'),
    ]

    engagement = models.ForeignKey(AuditEngagement, on_delete=models.CASCADE, related_name='team_assignments')
    team_member = models.ForeignKey(User, on_delete=models.CASCADE)
    role = models.CharField(max_length=15, choices=ROLE_CHOICES)

    # Assignment Details
    assigned_date = models.DateField()
    planned_hours = models.IntegerField(blank=True, null=True)
    actual_hours = models.IntegerField(default=0)
    hourly_rate = models.DecimalField(max_digits=8, decimal_places=2, blank=True, null=True)

    # Responsibilities
    responsibilities = models.TextField(blank=True, null=True)
    areas_assigned = models.TextField(blank=True, null=True)  # JSON field for specific audit areas

    # Status
    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ['engagement', 'team_member', 'role']
        ordering = ['role', 'assigned_date']

    def __str__(self):
        return f"{self.team_member.get_full_name()} - {self.role} ({self.engagement.engagement_number})"


class EngagementChecklist(models.Model):
    """Model for engagement-specific checklists"""

    CATEGORY_CHOICES = [
        ('PLANNING', 'Planning'),
        ('RISK_ASSESSMENT', 'Risk Assessment'),
        ('INTERNAL_CONTROLS', 'Internal Controls'),
        ('SUBSTANTIVE_TESTING', 'Substantive Testing'),
        ('COMPLETION', 'Completion'),
        ('REPORTING', 'Reporting'),
    ]

    engagement = models.ForeignKey(AuditEngagement, on_delete=models.CASCADE, related_name='checklist_items')
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)

    # Checklist Item Details
    item_number = models.CharField(max_length=10)  # e.g., "P.1", "R.5", "S.12"
    description = models.TextField()
    reference_standard = models.CharField(max_length=100, blank=True, null=True)  # SA reference

    # Assignment and Status
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    is_completed = models.BooleanField(default=False)
    completion_date = models.DateTimeField(blank=True, null=True)

    # Work Paper Reference
    work_paper_reference = models.CharField(max_length=50, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)

    # System Fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['category', 'item_number']
        unique_together = ['engagement', 'item_number']

    def __str__(self):
        return f"{self.item_number} - {self.description[:50]}"
