"""views.py - Enhanced Audit Engagement API views"""

from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count, Avg
from django.utils import timezone
from .models import AuditEngagement, EngagementLetter, AuditTeamAssignment, EngagementChecklist
from api.serializers import (
    AuditEngagementSerializer, AuditEngagementDetailSerializer,
    EngagementLetterSerializer, AuditTeamAssignmentSerializer,
    EngagementChecklistSerializer
)

class AuditEngagementViewSet(viewsets.ModelViewSet):
    queryset = AuditEngagement.objects.all()
    serializer_class = AuditEngagementSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['engagement_type', 'status', 'priority', 'financial_year', 'is_active']
    search_fields = ['engagement_number', 'client__name', 'scope_of_work']
    ordering_fields = ['created_at', 'planned_start_date', 'planned_end_date', 'priority']
    ordering = ['-created_at']
    
    def get_serializer_class(self):
        if self.action == 'retrieve':
            return AuditEngagementDetailSerializer
        return AuditEngagementSerializer
    
    def get_queryset(self):
        queryset = AuditEngagement.objects.select_related('client', 'engagement_partner', 'audit_manager', 'audit_senior')
        
        # Filter by active status for list view
        if self.action == 'list':
            queryset = queryset.filter(is_active=True)
        
        # Filter by current user's assignments
        user = self.request.user
        if hasattr(user, 'partner_engagements') or hasattr(user, 'manager_engagements') or hasattr(user, 'senior_engagements'):
            if self.request.query_params.get('my_engagements') == 'true':
                queryset = queryset.filter(
                    Q(engagement_partner=user) |
                    Q(audit_manager=user) |
                    Q(audit_senior=user) |
                    Q(team_members=user)
                ).distinct()
        
        return queryset
    
    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """Get engagement dashboard statistics"""
        now = timezone.now().date()
        
        stats = {
            'total_engagements': AuditEngagement.objects.filter(is_active=True).count(),
            'by_status': dict(
                AuditEngagement.objects.filter(is_active=True)
                .values_list('status')
                .annotate(count=Count('id'))
            ),
            'by_type': dict(
                AuditEngagement.objects.filter(is_active=True)
                .values_list('engagement_type')
                .annotate(count=Count('id'))
            ),
            'overdue_engagements': AuditEngagement.objects.filter(
                is_active=True,
                planned_end_date__lt=now,
                status__in=['PLANNING', 'FIELDWORK', 'REVIEW', 'REPORTING']
            ).count(),
            'upcoming_deadlines': AuditEngagement.objects.filter(
                is_active=True,
                planned_end_date__gte=now,
                planned_end_date__lte=now + timezone.timedelta(days=7),
                status__in=['PLANNING', 'FIELDWORK', 'REVIEW', 'REPORTING']
            ).count(),
            'avg_completion_time': AuditEngagement.objects.filter(
                status='COMPLETED',
                actual_start_date__isnull=False,
                actual_end_date__isnull=False
            ).aggregate(
                avg_days=Avg('actual_end_date') - Avg('actual_start_date')
            )['avg_days'] or 0
        }
        return Response(stats)
    
    @action(detail=True, methods=['post'])
    def start_engagement(self, request, pk=None):
        """Start an engagement"""
        engagement = self.get_object()
        if engagement.status == 'PLANNING':
            engagement.status = 'FIELDWORK'
            engagement.actual_start_date = timezone.now().date()
            engagement.save()
            return Response({'status': 'Engagement started'})
        return Response({'error': 'Engagement cannot be started'}, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def complete_engagement(self, request, pk=None):
        """Complete an engagement"""
        engagement = self.get_object()
        if engagement.status in ['FIELDWORK', 'REVIEW', 'REPORTING']:
            engagement.status = 'COMPLETED'
            engagement.actual_end_date = timezone.now().date()
            engagement.save()
            return Response({'status': 'Engagement completed'})
        return Response({'error': 'Engagement cannot be completed'}, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['get'])
    def team_assignments(self, request, pk=None):
        """Get team assignments for an engagement"""
        engagement = self.get_object()
        assignments = engagement.team_assignments.filter(is_active=True)
        serializer = AuditTeamAssignmentSerializer(assignments, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def assign_team_member(self, request, pk=None):
        """Assign a team member to an engagement"""
        engagement = self.get_object()
        serializer = AuditTeamAssignmentSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(engagement=engagement)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['get'])
    def checklist(self, request, pk=None):
        """Get checklist items for an engagement"""
        engagement = self.get_object()
        checklist_items = engagement.checklist_items.all().order_by('category', 'item_number')
        serializer = EngagementChecklistSerializer(checklist_items, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def generate_checklist(self, request, pk=None):
        """Generate default checklist for an engagement"""
        engagement = self.get_object()
        
        # Import the checklist generation logic
        from .services import generate_engagement_checklist
        checklist_items = generate_engagement_checklist(engagement)
        
        return Response({
            'message': f'Generated {len(checklist_items)} checklist items',
            'items': checklist_items
        })

class EngagementLetterViewSet(viewsets.ModelViewSet):
    queryset = EngagementLetter.objects.all()
    serializer_class = EngagementLetterSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['status']
    ordering = ['-created_at']
    
    @action(detail=True, methods=['post'])
    def send_to_client(self, request, pk=None):
        """Mark engagement letter as sent to client"""
        letter = self.get_object()
        letter.status = 'SENT'
        letter.save()
        return Response({'status': 'Letter marked as sent'})
    
    @action(detail=True, methods=['post'])
    def mark_signed(self, request, pk=None):
        """Mark engagement letter as signed by client"""
        letter = self.get_object()
        letter.status = 'SIGNED'
        letter.signed_date = timezone.now().date()
        letter.save()
        return Response({'status': 'Letter marked as signed'})

class AuditTeamAssignmentViewSet(viewsets.ModelViewSet):
    queryset = AuditTeamAssignment.objects.all()
    serializer_class = AuditTeamAssignmentSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['engagement', 'role', 'is_active']
    ordering = ['role', 'assigned_date']

class EngagementChecklistViewSet(viewsets.ModelViewSet):
    queryset = EngagementChecklist.objects.all()
    serializer_class = EngagementChecklistSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['engagement', 'category', 'is_completed', 'assigned_to']
    ordering = ['category', 'item_number']
    
    @action(detail=True, methods=['post'])
    def mark_completed(self, request, pk=None):
        """Mark a checklist item as completed"""
        item = self.get_object()
        item.is_completed = True
        item.completion_date = timezone.now()
        item.save()
        return Response({'status': 'Item marked as completed'})
    
    @action(detail=True, methods=['post'])
    def mark_incomplete(self, request, pk=None):
        """Mark a checklist item as incomplete"""
        item = self.get_object()
        item.is_completed = False
        item.completion_date = None
        item.save()
        return Response({'status': 'Item marked as incomplete'})
