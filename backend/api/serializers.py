"""serializers.py - API input/output serializers"""

from rest_framework import serializers
from audit.models import Audit
from clients.models import Client

class ClientSerializer(serializers.ModelSerializer):
    class Meta:
        model = Client
        fields = ['id', 'name']

class AuditSerializer(serializers.ModelSerializer):
    client = ClientSerializer(read_only=True)

    class Meta:
        model = Audit
        fields = ['id', 'title', 'client', 'created_at']
