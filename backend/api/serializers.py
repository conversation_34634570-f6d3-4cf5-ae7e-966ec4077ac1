"""serializers.py - Enhanced API serializers for comprehensive client and engagement management"""

from rest_framework import serializers
from audit.models import Audit
from clients.models import Client
from audit_engagements.models import AuditEngagement, EngagementLetter, AuditTeamAssignment, EngagementChecklist
from users.models import User

class UserSerializer(serializers.ModelSerializer):
    full_name = serializers.CharField(source='get_full_name', read_only=True)

    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'full_name', 'email', 'is_reviewer']

class ClientSerializer(serializers.ModelSerializer):
    display_name = serializers.CharField(source='get_display_name', read_only=True)
    is_company = serializers.BooleanField(read_only=True)
    requires_caro = serializers.BooleanField(read_only=True)

    class Meta:
        model = Client
        fields = [
            'id', 'name', 'display_name', 'entity_type', 'industry', 'size_category',
            'pan_number', 'cin_number', 'gst_number', 'tan_number',
            'contact_email', 'contact_phone', 'registered_address', 'city', 'state', 'pincode',
            'date_of_incorporation', 'annual_turnover', 'number_of_employees',
            'previous_auditor', 'audit_history_notes', 'is_company', 'requires_caro',
            'created_at', 'updated_at', 'is_active'
        ]
        read_only_fields = ['created_at', 'updated_at']

class ClientSummarySerializer(serializers.ModelSerializer):
    """Lightweight serializer for client listings"""
    display_name = serializers.CharField(source='get_display_name', read_only=True)

    class Meta:
        model = Client
        fields = ['id', 'name', 'display_name', 'entity_type', 'industry', 'pan_number', 'gst_number']

class AuditTeamAssignmentSerializer(serializers.ModelSerializer):
    team_member = UserSerializer(read_only=True)
    team_member_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = AuditTeamAssignment
        fields = [
            'id', 'team_member', 'team_member_id', 'role', 'assigned_date',
            'planned_hours', 'actual_hours', 'hourly_rate', 'responsibilities',
            'areas_assigned', 'is_active'
        ]

class EngagementChecklistSerializer(serializers.ModelSerializer):
    assigned_to = UserSerializer(read_only=True)
    assigned_to_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)

    class Meta:
        model = EngagementChecklist
        fields = [
            'id', 'category', 'item_number', 'description', 'reference_standard',
            'assigned_to', 'assigned_to_id', 'is_completed', 'completion_date',
            'work_paper_reference', 'notes', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class EngagementLetterSerializer(serializers.ModelSerializer):
    class Meta:
        model = EngagementLetter
        fields = [
            'id', 'letter_number', 'subject', 'content', 'terms_and_conditions',
            'issue_date', 'validity_date', 'signed_date', 'status',
            'letter_file', 'signed_file', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

class AuditEngagementSerializer(serializers.ModelSerializer):
    client = ClientSummarySerializer(read_only=True)
    client_id = serializers.IntegerField(write_only=True)

    engagement_partner = UserSerializer(read_only=True)
    engagement_partner_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)

    audit_manager = UserSerializer(read_only=True)
    audit_manager_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)

    audit_senior = UserSerializer(read_only=True)
    audit_senior_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)

    team_members = UserSerializer(many=True, read_only=True)
    team_member_ids = serializers.ListField(
        child=serializers.IntegerField(), write_only=True, required=False
    )

    # Computed fields
    is_overdue = serializers.BooleanField(read_only=True)
    completion_percentage = serializers.IntegerField(read_only=True)

    class Meta:
        model = AuditEngagement
        fields = [
            'id', 'client', 'client_id', 'engagement_type', 'engagement_number',
            'financial_year', 'period_start', 'period_end',
            'planned_start_date', 'planned_end_date', 'actual_start_date', 'actual_end_date',
            'status', 'priority', 'engagement_partner', 'engagement_partner_id',
            'audit_manager', 'audit_manager_id', 'audit_senior', 'audit_senior_id',
            'team_members', 'team_member_ids', 'scope_of_work', 'special_instructions',
            'materiality_amount', 'budgeted_hours', 'actual_hours', 'budget_amount',
            'overall_risk_level', 'requires_caro', 'requires_3cd', 'requires_3ca_3cb',
            'is_overdue', 'completion_percentage', 'created_at', 'updated_at', 'is_active'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def create(self, validated_data):
        team_member_ids = validated_data.pop('team_member_ids', [])
        engagement = AuditEngagement.objects.create(**validated_data)
        if team_member_ids:
            engagement.team_members.set(team_member_ids)
        return engagement

    def update(self, instance, validated_data):
        team_member_ids = validated_data.pop('team_member_ids', None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        if team_member_ids is not None:
            instance.team_members.set(team_member_ids)

        return instance

class AuditEngagementDetailSerializer(AuditEngagementSerializer):
    """Detailed serializer with nested relationships"""
    client = ClientSerializer(read_only=True)
    team_assignments = AuditTeamAssignmentSerializer(many=True, read_only=True)
    checklist_items = EngagementChecklistSerializer(many=True, read_only=True)
    engagement_letter = EngagementLetterSerializer(read_only=True)

class AuditSerializer(serializers.ModelSerializer):
    client = ClientSummarySerializer(read_only=True)

    class Meta:
        model = Audit
        fields = ['id', 'title', 'client', 'created_at']
