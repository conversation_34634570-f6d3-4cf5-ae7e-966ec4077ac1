"""views.py - API logic for clients and audits"""

from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from audit.models import Audit
from clients.models import Client
from .serializers import AuditSerializer, ClientSerializer

class ClientViewSet(viewsets.ModelViewSet):
    queryset = Client.objects.all()
    serializer_class = ClientSerializer

class AuditViewSet(viewsets.ModelViewSet):
    queryset = Audit.objects.select_related("client").all()
    serializer_class = AuditSerializer

    @action(detail=True, methods=['get'])
    def checklist(self, request, pk=None):
        """Return a static checklist for the given audit (placeholder)"""
        checklist = [
            "Verify Books of Accounts",
            "Reconcile GSTR-2B with purchase register",
            "Check TDS deductions with 26AS",
            "Ensure Depreciation under Clause 18 is correctly applied"
        ]
        return Response({"audit_id": pk, "checklist": checklist}, status=status.HTTP_200_OK)
