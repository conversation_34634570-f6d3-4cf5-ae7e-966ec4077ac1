"""bank_statement_parser.py - Extract transactions from bank statement text"""

import re
from datetime import datetime

def parse_bank_statement(text: str) -> list:
    """
    Parse raw bank statement text into structured transactions.

    Args:
        text (str): Extracted text from OCR or PDF

    Returns:
        list[dict]: List of transaction entries
    """
    lines = text.splitlines()
    transactions = []

    pattern = re.compile(r"(\d{2}/\d{2}/\d{4})\s+(.+?)\s+(-?\d+\.\d{2})\s+(-?\d+\.\d{2})")

    for line in lines:
        match = pattern.search(line)
        if match:
            date_str, description, debit_credit, balance = match.groups()
            transactions.append({
                "date": datetime.strptime(date_str, "%d/%m/%Y").date(),
                "description": description.strip(),
                "amount": float(debit_credit),
                "balance": float(balance)
            })
    return transactions
