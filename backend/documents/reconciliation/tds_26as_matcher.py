"""tds_26as_matcher.py - Reconcile TDS entries with Form 26AS"""

def match_tds_entries(tds_data: list, form26as_data: list) -> dict:
    """
    Compare client TDS entries with Form 26AS

    Args:
        tds_data (list): TDS recorded by client
        form26as_data (list): Downloaded Form 26AS data

    Returns:
        dict: Matching result summary
    """
    matched = []
    mismatched = []

    form26as_map = {(entry["pan"], entry["amount"]): entry for entry in form26as_data}

    for tds in tds_data:
        key = (tds["pan"], tds["amount"])
        if key in form26as_map:
            matched.append({**tds, "status": "Matched"})
        else:
            mismatched.append({**tds, "status": "Not Found in 26AS"})

    return {
        "matched_count": len(matched),
        "mismatched_count": len(mismatched),
        "matched": matched,
        "mismatched": mismatched
    }
