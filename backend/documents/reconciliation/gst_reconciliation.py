"""gst_reconciliation.py - Reconcile GSTR data with purchase register"""

def reconcile_gst(purchase_data: list, gstr_2b_data: list) -> dict:
    """
    Match purchases with GSTR-2B entries.

    Args:
        purchase_data (list): Invoices from client's books
        gstr_2b_data (list): Entries from GSTR-2B download

    Returns:
        dict: Match report with matched and unmatched entries
    """
    matched = []
    unmatched = []

    gstr_map = {(entry["invoice_no"], entry["gstin"]): entry for entry in gstr_2b_data}

    for purchase in purchase_data:
        key = (purchase["invoice_no"], purchase["gstin"])
        if key in gstr_map:
            matched.append({**purchase, "status": "Matched"})
        else:
            unmatched.append({**purchase, "status": "Not Found in GSTR-2B"})

    return {
        "total": len(purchase_data),
        "matched": matched,
        "unmatched": unmatched
    }
