"""serializers.py - Document processing API serializers"""

from rest_framework import serializers
from .models import (
    UploadedDocument, DocumentAnalysis, ExtractedEntity,
    DocumentAnomalyFlag, DocumentTemplate
)
from users.models import User
from clients.models import Client
from audit_engagements.models import AuditEngagement

class UserSummarySerializer(serializers.ModelSerializer):
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'full_name']

class ClientSummarySerializer(serializers.ModelSerializer):
    class Meta:
        model = Client
        fields = ['id', 'name', 'pan_number']

class EngagementSummarySerializer(serializers.ModelSerializer):
    class Meta:
        model = AuditEngagement
        fields = ['id', 'engagement_number', 'engagement_type']

class DocumentAnalysisSerializer(serializers.ModelSerializer):
    class Meta:
        model = DocumentAnalysis
        fields = [
            'id', 'analysis_type', 'results', 'confidence_score',
            'processing_time', 'model_name', 'model_version', 'created_at'
        ]
        read_only_fields = ['created_at']

class ExtractedEntitySerializer(serializers.ModelSerializer):
    validated_by = UserSummarySerializer(read_only=True)
    
    class Meta:
        model = ExtractedEntity
        fields = [
            'id', 'entity_type', 'entity_value', 'confidence_score',
            'page_number', 'bounding_box', 'is_validated', 'validated_by',
            'validated_at', 'created_at'
        ]
        read_only_fields = ['created_at', 'validated_at']

class DocumentAnomalyFlagSerializer(serializers.ModelSerializer):
    resolved_by = UserSummarySerializer(read_only=True)
    
    class Meta:
        model = DocumentAnomalyFlag
        fields = [
            'id', 'anomaly_type', 'severity', 'description', 'affected_field',
            'expected_value', 'actual_value', 'detection_algorithm',
            'confidence_score', 'is_resolved', 'resolution_notes',
            'resolved_by', 'resolved_at', 'created_at'
        ]
        read_only_fields = ['created_at', 'resolved_at']

class DocumentTemplateSerializer(serializers.ModelSerializer):
    created_by = UserSummarySerializer(read_only=True)
    
    class Meta:
        model = DocumentTemplate
        fields = [
            'id', 'name', 'document_type', 'description', 'field_mappings',
            'validation_rules', 'is_active', 'usage_count', 'accuracy_score',
            'created_by', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'usage_count', 'accuracy_score']

class UploadedDocumentSerializer(serializers.ModelSerializer):
    client = ClientSummarySerializer(read_only=True)
    client_id = serializers.IntegerField(write_only=True)
    
    engagement = EngagementSummarySerializer(read_only=True)
    engagement_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    
    uploaded_by = UserSummarySerializer(read_only=True)
    
    # Nested related data
    analyses = DocumentAnalysisSerializer(many=True, read_only=True)
    extracted_entities = ExtractedEntitySerializer(many=True, read_only=True)
    anomaly_flags = DocumentAnomalyFlagSerializer(many=True, read_only=True)
    
    # Computed fields
    file_size_mb = serializers.SerializerMethodField()
    processing_duration = serializers.SerializerMethodField()
    
    class Meta:
        model = UploadedDocument
        fields = [
            'id', 'client', 'client_id', 'engagement', 'engagement_id',
            'document_name', 'document_type', 'file', 'file_size',
            'file_size_mb', 'file_hash', 'processing_status',
            'processing_started_at', 'processing_completed_at',
            'processing_duration', 'processing_error', 'extracted_text',
            'extracted_data', 'confidence_score', 'uploaded_by',
            'upload_date', 'updated_at', 'is_duplicate', 'duplicate_of',
            'has_anomalies', 'requires_review', 'analyses',
            'extracted_entities', 'anomaly_flags'
        ]
        read_only_fields = [
            'file_size', 'file_hash', 'processing_started_at',
            'processing_completed_at', 'processing_error', 'extracted_text',
            'extracted_data', 'confidence_score', 'upload_date', 'updated_at',
            'is_duplicate', 'duplicate_of', 'has_anomalies', 'requires_review'
        ]
    
    def get_file_size_mb(self, obj):
        """Get file size in MB"""
        if obj.file_size:
            return round(obj.file_size / (1024 * 1024), 2)
        return None
    
    def get_processing_duration(self, obj):
        """Get processing duration in seconds"""
        if obj.processing_started_at and obj.processing_completed_at:
            delta = obj.processing_completed_at - obj.processing_started_at
            return delta.total_seconds()
        return None

class UploadedDocumentSummarySerializer(serializers.ModelSerializer):
    """Lightweight serializer for document listings"""
    client_name = serializers.CharField(source='client.name', read_only=True)
    uploaded_by_name = serializers.CharField(source='uploaded_by.get_full_name', read_only=True)
    file_size_mb = serializers.SerializerMethodField()
    
    class Meta:
        model = UploadedDocument
        fields = [
            'id', 'document_name', 'document_type', 'client_name',
            'processing_status', 'confidence_score', 'file_size_mb',
            'has_anomalies', 'requires_review', 'uploaded_by_name',
            'upload_date'
        ]
    
    def get_file_size_mb(self, obj):
        """Get file size in MB"""
        if obj.file_size:
            return round(obj.file_size / (1024 * 1024), 2)
        return None

class DocumentProcessingStatsSerializer(serializers.Serializer):
    """Serializer for document processing statistics"""
    total_documents = serializers.IntegerField()
    by_status = serializers.DictField()
    by_type = serializers.DictField()
    with_anomalies = serializers.IntegerField()
    requiring_review = serializers.IntegerField()
    duplicates = serializers.IntegerField()
    avg_confidence = serializers.FloatField()

class BulkProcessRequestSerializer(serializers.Serializer):
    """Serializer for bulk processing requests"""
    document_ids = serializers.ListField(
        child=serializers.IntegerField(),
        min_length=1,
        max_length=100
    )

class EntityValidationRequestSerializer(serializers.Serializer):
    """Serializer for entity validation requests"""
    entity_ids = serializers.ListField(
        child=serializers.IntegerField(),
        min_length=1
    )
    validation_notes = serializers.CharField(required=False, allow_blank=True)

class AnomalyResolutionSerializer(serializers.Serializer):
    """Serializer for anomaly resolution"""
    resolution_notes = serializers.CharField(required=True)
    mark_similar_resolved = serializers.BooleanField(default=False)

class DocumentSearchSerializer(serializers.Serializer):
    """Serializer for advanced document search"""
    query = serializers.CharField(required=False, allow_blank=True)
    document_types = serializers.ListField(
        child=serializers.CharField(),
        required=False
    )
    date_from = serializers.DateField(required=False)
    date_to = serializers.DateField(required=False)
    client_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False
    )
    has_anomalies = serializers.BooleanField(required=False)
    requires_review = serializers.BooleanField(required=False)
    min_confidence = serializers.FloatField(required=False, min_value=0, max_value=1)
    max_confidence = serializers.FloatField(required=False, min_value=0, max_value=1)
