"""models.py - Document metadata & storage"""

from django.db import models
from clients.models import Client

class UploadedDocument(models.Model):
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name="documents")
    document_name = models.CharField(max_length=255)
    document_type = models.CharField(max_length=50, choices=[
        ("BANK_STATEMENT", "Bank Statement"),
        ("GST_REPORT", "GST Report"),
        ("FORM_26AS", "Form 26AS"),
        ("INVOICE", "Invoice"),
        ("OTHERS", "Others")
    ])
    upload_date = models.DateTimeField(auto_now_add=True)
    file = models.FileField(upload_to='documents/')

    def __str__(self):
        return f"{self.client.name} - {self.document_name} ({self.document_type})"
