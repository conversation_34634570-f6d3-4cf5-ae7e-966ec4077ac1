"""models.py - Enhanced Document metadata & storage with ML processing"""

from django.db import models
from django.core.validators import FileExtensionValidator
from clients.models import Client
from audit_engagements.models import AuditEngagement
from users.models import User
import json

class UploadedDocument(models.Model):
    DOCUMENT_TYPES = [
        ("BANK_STATEMENT", "Bank Statement"),
        ("GST_REPORT", "GST Report"),
        ("FORM_26AS", "Form 26AS"),
        ("FORM_16", "Form 16"),
        ("INVOICE", "Invoice"),
        ("PURCHASE_ORDER", "Purchase Order"),
        ("BALANCE_SHEET", "Balance Sheet"),
        ("PROFIT_LOSS", "Profit & Loss Statement"),
        ("TRIAL_BALANCE", "Trial Balance"),
        ("LEDGER", "Ledger"),
        ("JOURNAL", "Journal Entries"),
        ("TALLY_EXPORT", "Tally Export"),
        ("EXCEL_REPORT", "Excel Report"),
        ("PDF_REPORT", "PDF Report"),
        ("CONFIRMATION", "Confirmation"),
        ("CONTRACT", "Contract/Agreement"),
        ("BOARD_RESOLUTION", "Board Resolution"),
        ("AUDIT_REPORT", "Audit Report"),
        ("OTHERS", "Others")
    ]

    PROCESSING_STATUS = [
        ('PENDING', 'Pending Processing'),
        ('PROCESSING', 'Processing'),
        ('COMPLETED', 'Processing Completed'),
        ('FAILED', 'Processing Failed'),
        ('MANUAL_REVIEW', 'Manual Review Required'),
    ]

    # Basic Information
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name="documents")
    engagement = models.ForeignKey(AuditEngagement, on_delete=models.CASCADE, related_name="documents", null=True, blank=True)
    document_name = models.CharField(max_length=255)
    document_type = models.CharField(max_length=50, choices=DOCUMENT_TYPES)

    # File Information
    file = models.FileField(
        upload_to='documents/',
        validators=[FileExtensionValidator(allowed_extensions=['pdf', 'xlsx', 'xls', 'csv', 'jpg', 'jpeg', 'png', 'tiff'])]
    )
    file_size = models.BigIntegerField(null=True, blank=True)  # in bytes
    file_hash = models.CharField(max_length=64, null=True, blank=True)  # SHA-256 hash

    # Processing Information
    processing_status = models.CharField(max_length=20, choices=PROCESSING_STATUS, default='PENDING')
    processing_started_at = models.DateTimeField(null=True, blank=True)
    processing_completed_at = models.DateTimeField(null=True, blank=True)
    processing_error = models.TextField(null=True, blank=True)

    # Extracted Data
    extracted_text = models.TextField(null=True, blank=True)
    extracted_data = models.JSONField(null=True, blank=True)  # Structured data extracted by ML
    confidence_score = models.FloatField(null=True, blank=True)  # ML confidence score

    # Metadata
    uploaded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    upload_date = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Flags
    is_duplicate = models.BooleanField(default=False)
    duplicate_of = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True)
    has_anomalies = models.BooleanField(default=False)
    requires_review = models.BooleanField(default=False)

    class Meta:
        ordering = ['-upload_date']
        indexes = [
            models.Index(fields=['document_type']),
            models.Index(fields=['processing_status']),
            models.Index(fields=['file_hash']),
            models.Index(fields=['upload_date']),
        ]

    def __str__(self):
        return f"{self.client.name} - {self.document_name} ({self.document_type})"

class DocumentAnalysis(models.Model):
    """ML analysis results for documents"""

    ANALYSIS_TYPES = [
        ('OCR', 'Optical Character Recognition'),
        ('CLASSIFICATION', 'Document Classification'),
        ('EXTRACTION', 'Data Extraction'),
        ('ANOMALY_DETECTION', 'Anomaly Detection'),
        ('VALIDATION', 'Data Validation'),
        ('RECONCILIATION', 'Reconciliation Analysis'),
    ]

    document = models.ForeignKey(UploadedDocument, on_delete=models.CASCADE, related_name='analyses')
    analysis_type = models.CharField(max_length=20, choices=ANALYSIS_TYPES)

    # Analysis Results
    results = models.JSONField()  # Detailed analysis results
    confidence_score = models.FloatField()
    processing_time = models.FloatField()  # in seconds

    # Model Information
    model_name = models.CharField(max_length=100)
    model_version = models.CharField(max_length=20)

    # System Fields
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.analysis_type} - {self.document.document_name}"

class ExtractedEntity(models.Model):
    """Entities extracted from documents"""

    ENTITY_TYPES = [
        ('AMOUNT', 'Amount'),
        ('DATE', 'Date'),
        ('COMPANY_NAME', 'Company Name'),
        ('PAN', 'PAN Number'),
        ('GST_NUMBER', 'GST Number'),
        ('INVOICE_NUMBER', 'Invoice Number'),
        ('ACCOUNT_NUMBER', 'Account Number'),
        ('ADDRESS', 'Address'),
        ('PERSON_NAME', 'Person Name'),
        ('TAX_RATE', 'Tax Rate'),
        ('DESCRIPTION', 'Description'),
        ('OTHER', 'Other'),
    ]

    document = models.ForeignKey(UploadedDocument, on_delete=models.CASCADE, related_name='extracted_entities')
    entity_type = models.CharField(max_length=20, choices=ENTITY_TYPES)
    entity_value = models.TextField()
    confidence_score = models.FloatField()

    # Position in document
    page_number = models.IntegerField(null=True, blank=True)
    bounding_box = models.JSONField(null=True, blank=True)  # x, y, width, height

    # Validation
    is_validated = models.BooleanField(default=False)
    validated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    validated_at = models.DateTimeField(null=True, blank=True)

    # System Fields
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['page_number', 'entity_type']

    def __str__(self):
        return f"{self.entity_type}: {self.entity_value[:50]}"

class DocumentAnomalyFlag(models.Model):
    """Anomalies detected in documents"""

    ANOMALY_TYPES = [
        ('DUPLICATE_ENTRY', 'Duplicate Entry'),
        ('MISSING_DATA', 'Missing Data'),
        ('INVALID_FORMAT', 'Invalid Format'),
        ('CALCULATION_ERROR', 'Calculation Error'),
        ('UNUSUAL_AMOUNT', 'Unusual Amount'),
        ('DATE_INCONSISTENCY', 'Date Inconsistency'),
        ('TAX_MISMATCH', 'Tax Calculation Mismatch'),
        ('SEQUENCE_BREAK', 'Sequence Break'),
        ('SUSPICIOUS_PATTERN', 'Suspicious Pattern'),
        ('DATA_QUALITY', 'Data Quality Issue'),
    ]

    SEVERITY_LEVELS = [
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    ]

    document = models.ForeignKey(UploadedDocument, on_delete=models.CASCADE, related_name='anomaly_flags')
    anomaly_type = models.CharField(max_length=20, choices=ANOMALY_TYPES)
    severity = models.CharField(max_length=10, choices=SEVERITY_LEVELS)

    # Anomaly Details
    description = models.TextField()
    affected_field = models.CharField(max_length=100, null=True, blank=True)
    expected_value = models.TextField(null=True, blank=True)
    actual_value = models.TextField(null=True, blank=True)

    # ML Detection Info
    detection_algorithm = models.CharField(max_length=100)
    confidence_score = models.FloatField()

    # Resolution
    is_resolved = models.BooleanField(default=False)
    resolution_notes = models.TextField(null=True, blank=True)
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    resolved_at = models.DateTimeField(null=True, blank=True)

    # System Fields
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-severity', '-created_at']

    def __str__(self):
        return f"{self.anomaly_type} - {self.severity} ({self.document.document_name})"

class DocumentTemplate(models.Model):
    """Templates for document structure recognition"""

    name = models.CharField(max_length=255)
    document_type = models.CharField(max_length=50, choices=UploadedDocument.DOCUMENT_TYPES)
    description = models.TextField()

    # Template Structure
    field_mappings = models.JSONField()  # Field extraction rules
    validation_rules = models.JSONField()  # Validation rules

    # Template Metadata
    is_active = models.BooleanField(default=True)
    usage_count = models.IntegerField(default=0)
    accuracy_score = models.FloatField(default=0.0)

    # System Fields
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return f"Template: {self.name} ({self.document_type})"
