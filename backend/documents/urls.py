"""urls.py - Document processing API routing"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    UploadedDocumentViewSet,
    DocumentAnalysisViewSet,
    ExtractedEntityViewSet,
    DocumentAnomalyFlagViewSet,
    DocumentTemplateViewSet
)

router = DefaultRouter()
router.register(r'documents', UploadedDocumentViewSet)
router.register(r'analyses', DocumentAnalysisViewSet)
router.register(r'entities', ExtractedEntityViewSet)
router.register(r'anomalies', DocumentAnomalyFlagViewSet)
router.register(r'templates', DocumentTemplateViewSet)

urlpatterns = [
    path('', include(router.urls)),
]
