"""views.py - Enhanced Document processing API views"""

from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count
from django.utils import timezone
from .models import (
    UploadedDocument, DocumentAnalysis, ExtractedEntity, 
    DocumentAnomalyFlag, DocumentTemplate
)
from .serializers import (
    UploadedDocumentSerializer, DocumentAnalysisSerializer,
    ExtractedEntitySerializer, DocumentAnomalyFlagSerializer,
    DocumentTemplateSerializer
)
from .ocr_processor import DocumentProcessor

class UploadedDocumentViewSet(viewsets.ModelViewSet):
    queryset = UploadedDocument.objects.all()
    serializer_class = UploadedDocumentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['document_type', 'processing_status', 'client', 'engagement', 'has_anomalies', 'requires_review']
    search_fields = ['document_name', 'extracted_text']
    ordering_fields = ['upload_date', 'processing_completed_at', 'confidence_score']
    ordering = ['-upload_date']
    
    def perform_create(self, serializer):
        document = serializer.save(uploaded_by=self.request.user)
        
        # Trigger ML processing asynchronously
        self._process_document_async(document)
    
    def _process_document_async(self, document):
        """Process document with ML pipeline (in production, use Celery)"""
        try:
            processor = DocumentProcessor()
            processor.process_document(document)
        except Exception as e:
            # Log error in production
            print(f"Document processing error: {e}")
    
    @action(detail=True, methods=['post'])
    def reprocess(self, request, pk=None):
        """Reprocess a document through ML pipeline"""
        document = self.get_object()
        
        # Clear previous processing results
        document.processing_status = 'PENDING'
        document.processing_error = None
        document.extracted_text = None
        document.extracted_data = None
        document.confidence_score = None
        document.save()
        
        # Clear related data
        document.analyses.all().delete()
        document.extracted_entities.all().delete()
        document.anomaly_flags.all().delete()
        
        # Reprocess
        self._process_document_async(document)
        
        return Response({'status': 'Document queued for reprocessing'})
    
    @action(detail=True, methods=['get'])
    def analysis_results(self, request, pk=None):
        """Get detailed analysis results for a document"""
        document = self.get_object()
        
        analyses = document.analyses.all()
        entities = document.extracted_entities.all()
        anomalies = document.anomaly_flags.all()
        
        return Response({
            'document': UploadedDocumentSerializer(document).data,
            'analyses': DocumentAnalysisSerializer(analyses, many=True).data,
            'entities': ExtractedEntitySerializer(entities, many=True).data,
            'anomalies': DocumentAnomalyFlagSerializer(anomalies, many=True).data
        })
    
    @action(detail=False, methods=['get'])
    def processing_stats(self, request):
        """Get document processing statistics"""
        stats = {
            'total_documents': UploadedDocument.objects.count(),
            'by_status': dict(
                UploadedDocument.objects.values_list('processing_status')
                .annotate(count=Count('id'))
            ),
            'by_type': dict(
                UploadedDocument.objects.values_list('document_type')
                .annotate(count=Count('id'))
            ),
            'with_anomalies': UploadedDocument.objects.filter(has_anomalies=True).count(),
            'requiring_review': UploadedDocument.objects.filter(requires_review=True).count(),
            'duplicates': UploadedDocument.objects.filter(is_duplicate=True).count(),
            'avg_confidence': UploadedDocument.objects.filter(
                confidence_score__isnull=False
            ).aggregate(avg=models.Avg('confidence_score'))['avg'] or 0
        }
        
        return Response(stats)
    
    @action(detail=False, methods=['get'])
    def pending_review(self, request):
        """Get documents requiring manual review"""
        documents = UploadedDocument.objects.filter(
            Q(requires_review=True) | Q(has_anomalies=True)
        ).order_by('-upload_date')
        
        serializer = UploadedDocumentSerializer(documents, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def mark_reviewed(self, request, pk=None):
        """Mark a document as reviewed"""
        document = self.get_object()
        document.requires_review = False
        document.save()
        
        return Response({'status': 'Document marked as reviewed'})
    
    @action(detail=False, methods=['post'])
    def bulk_process(self, request):
        """Bulk process multiple documents"""
        document_ids = request.data.get('document_ids', [])
        
        if not document_ids:
            return Response({'error': 'No document IDs provided'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        documents = UploadedDocument.objects.filter(id__in=document_ids)
        processed_count = 0
        
        for document in documents:
            try:
                self._process_document_async(document)
                processed_count += 1
            except Exception as e:
                continue
        
        return Response({
            'status': f'Queued {processed_count} documents for processing',
            'processed_count': processed_count,
            'total_requested': len(document_ids)
        })

class DocumentAnalysisViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = DocumentAnalysis.objects.all()
    serializer_class = DocumentAnalysisSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['document', 'analysis_type', 'model_name']
    ordering = ['-created_at']

class ExtractedEntityViewSet(viewsets.ModelViewSet):
    queryset = ExtractedEntity.objects.all()
    serializer_class = ExtractedEntitySerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['document', 'entity_type', 'is_validated']
    search_fields = ['entity_value']
    ordering = ['entity_type', '-confidence_score']
    
    @action(detail=True, methods=['post'])
    def validate_entity(self, request, pk=None):
        """Validate an extracted entity"""
        entity = self.get_object()
        entity.is_validated = True
        entity.validated_by = request.user
        entity.validated_at = timezone.now()
        entity.save()
        
        return Response({'status': 'Entity validated'})
    
    @action(detail=False, methods=['get'])
    def validation_stats(self, request):
        """Get entity validation statistics"""
        stats = {
            'total_entities': ExtractedEntity.objects.count(),
            'validated': ExtractedEntity.objects.filter(is_validated=True).count(),
            'pending_validation': ExtractedEntity.objects.filter(is_validated=False).count(),
            'by_type': dict(
                ExtractedEntity.objects.values_list('entity_type')
                .annotate(count=Count('id'))
            ),
            'avg_confidence': ExtractedEntity.objects.aggregate(
                avg=models.Avg('confidence_score')
            )['avg'] or 0
        }
        
        return Response(stats)

class DocumentAnomalyFlagViewSet(viewsets.ModelViewSet):
    queryset = DocumentAnomalyFlag.objects.all()
    serializer_class = DocumentAnomalyFlagSerializer
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['document', 'anomaly_type', 'severity', 'is_resolved']
    ordering = ['-severity', '-created_at']
    
    @action(detail=True, methods=['post'])
    def resolve_anomaly(self, request, pk=None):
        """Resolve an anomaly flag"""
        anomaly = self.get_object()
        resolution_notes = request.data.get('resolution_notes', '')
        
        anomaly.is_resolved = True
        anomaly.resolution_notes = resolution_notes
        anomaly.resolved_by = request.user
        anomaly.resolved_at = timezone.now()
        anomaly.save()
        
        return Response({'status': 'Anomaly resolved'})
    
    @action(detail=False, methods=['get'])
    def anomaly_dashboard(self, request):
        """Get anomaly dashboard statistics"""
        stats = {
            'total_anomalies': DocumentAnomalyFlag.objects.count(),
            'unresolved': DocumentAnomalyFlag.objects.filter(is_resolved=False).count(),
            'by_severity': dict(
                DocumentAnomalyFlag.objects.values_list('severity')
                .annotate(count=Count('id'))
            ),
            'by_type': dict(
                DocumentAnomalyFlag.objects.values_list('anomaly_type')
                .annotate(count=Count('id'))
            ),
            'recent_anomalies': DocumentAnomalyFlagSerializer(
                DocumentAnomalyFlag.objects.filter(is_resolved=False)
                .order_by('-created_at')[:10],
                many=True
            ).data
        }
        
        return Response(stats)

class DocumentTemplateViewSet(viewsets.ModelViewSet):
    queryset = DocumentTemplate.objects.filter(is_active=True)
    serializer_class = DocumentTemplateSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['document_type', 'is_active']
    search_fields = ['name', 'description']
    ordering = ['name']
    
    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)
    
    @action(detail=True, methods=['post'])
    def test_template(self, request, pk=None):
        """Test a template against a document"""
        template = self.get_object()
        document_id = request.data.get('document_id')
        
        if not document_id:
            return Response({'error': 'document_id is required'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        try:
            document = UploadedDocument.objects.get(id=document_id)
        except UploadedDocument.DoesNotExist:
            return Response({'error': 'Document not found'}, 
                          status=status.HTTP_404_NOT_FOUND)
        
        # Test template extraction logic here
        # This would involve applying the template's field mappings to the document
        
        return Response({
            'status': 'Template test completed',
            'template': template.name,
            'document': document.document_name,
            'extraction_results': {}  # Would contain actual results
        })
