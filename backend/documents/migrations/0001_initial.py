# Generated by Django 5.2.4 on 2025-07-09 06:07

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("audit_engagements", "0001_initial"),
        ("clients", "0002_alter_client_options_client_annual_turnover_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="DocumentTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                (
                    "document_type",
                    models.CharField(
                        choices=[
                            ("BANK_STATEMENT", "Bank Statement"),
                            ("GST_REPORT", "GST Report"),
                            ("FORM_26AS", "Form 26AS"),
                            ("FORM_16", "Form 16"),
                            ("INVOICE", "Invoice"),
                            ("PURCHASE_ORDER", "Purchase Order"),
                            ("BALANCE_SHEET", "Balance Sheet"),
                            ("PROFIT_LOSS", "Profit & Loss Statement"),
                            ("TRIAL_BALANCE", "Trial Balance"),
                            ("LEDGER", "Ledger"),
                            ("JOURNAL", "Journal Entries"),
                            ("TALLY_EXPORT", "Tally Export"),
                            ("EXCEL_REPORT", "Excel Report"),
                            ("PDF_REPORT", "PDF Report"),
                            ("CONFIRMATION", "Confirmation"),
                            ("CONTRACT", "Contract/Agreement"),
                            ("BOARD_RESOLUTION", "Board Resolution"),
                            ("AUDIT_REPORT", "Audit Report"),
                            ("OTHERS", "Others"),
                        ],
                        max_length=50,
                    ),
                ),
                ("description", models.TextField()),
                ("field_mappings", models.JSONField()),
                ("validation_rules", models.JSONField()),
                ("is_active", models.BooleanField(default=True)),
                ("usage_count", models.IntegerField(default=0)),
                ("accuracy_score", models.FloatField(default=0.0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="UploadedDocument",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("document_name", models.CharField(max_length=255)),
                (
                    "document_type",
                    models.CharField(
                        choices=[
                            ("BANK_STATEMENT", "Bank Statement"),
                            ("GST_REPORT", "GST Report"),
                            ("FORM_26AS", "Form 26AS"),
                            ("FORM_16", "Form 16"),
                            ("INVOICE", "Invoice"),
                            ("PURCHASE_ORDER", "Purchase Order"),
                            ("BALANCE_SHEET", "Balance Sheet"),
                            ("PROFIT_LOSS", "Profit & Loss Statement"),
                            ("TRIAL_BALANCE", "Trial Balance"),
                            ("LEDGER", "Ledger"),
                            ("JOURNAL", "Journal Entries"),
                            ("TALLY_EXPORT", "Tally Export"),
                            ("EXCEL_REPORT", "Excel Report"),
                            ("PDF_REPORT", "PDF Report"),
                            ("CONFIRMATION", "Confirmation"),
                            ("CONTRACT", "Contract/Agreement"),
                            ("BOARD_RESOLUTION", "Board Resolution"),
                            ("AUDIT_REPORT", "Audit Report"),
                            ("OTHERS", "Others"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "file",
                    models.FileField(
                        upload_to="documents/",
                        validators=[
                            django.core.validators.FileExtensionValidator(
                                allowed_extensions=[
                                    "pdf",
                                    "xlsx",
                                    "xls",
                                    "csv",
                                    "jpg",
                                    "jpeg",
                                    "png",
                                    "tiff",
                                ]
                            )
                        ],
                    ),
                ),
                ("file_size", models.BigIntegerField(blank=True, null=True)),
                ("file_hash", models.CharField(blank=True, max_length=64, null=True)),
                (
                    "processing_status",
                    models.CharField(
                        choices=[
                            ("PENDING", "Pending Processing"),
                            ("PROCESSING", "Processing"),
                            ("COMPLETED", "Processing Completed"),
                            ("FAILED", "Processing Failed"),
                            ("MANUAL_REVIEW", "Manual Review Required"),
                        ],
                        default="PENDING",
                        max_length=20,
                    ),
                ),
                ("processing_started_at", models.DateTimeField(blank=True, null=True)),
                (
                    "processing_completed_at",
                    models.DateTimeField(blank=True, null=True),
                ),
                ("processing_error", models.TextField(blank=True, null=True)),
                ("extracted_text", models.TextField(blank=True, null=True)),
                ("extracted_data", models.JSONField(blank=True, null=True)),
                ("confidence_score", models.FloatField(blank=True, null=True)),
                ("upload_date", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_duplicate", models.BooleanField(default=False)),
                ("has_anomalies", models.BooleanField(default=False)),
                ("requires_review", models.BooleanField(default=False)),
                (
                    "client",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="documents",
                        to="clients.client",
                    ),
                ),
                (
                    "duplicate_of",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="documents.uploadeddocument",
                    ),
                ),
                (
                    "engagement",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="documents",
                        to="audit_engagements.auditengagement",
                    ),
                ),
                (
                    "uploaded_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-upload_date"],
            },
        ),
        migrations.CreateModel(
            name="ExtractedEntity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "entity_type",
                    models.CharField(
                        choices=[
                            ("AMOUNT", "Amount"),
                            ("DATE", "Date"),
                            ("COMPANY_NAME", "Company Name"),
                            ("PAN", "PAN Number"),
                            ("GST_NUMBER", "GST Number"),
                            ("INVOICE_NUMBER", "Invoice Number"),
                            ("ACCOUNT_NUMBER", "Account Number"),
                            ("ADDRESS", "Address"),
                            ("PERSON_NAME", "Person Name"),
                            ("TAX_RATE", "Tax Rate"),
                            ("DESCRIPTION", "Description"),
                            ("OTHER", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                ("entity_value", models.TextField()),
                ("confidence_score", models.FloatField()),
                ("page_number", models.IntegerField(blank=True, null=True)),
                ("bounding_box", models.JSONField(blank=True, null=True)),
                ("is_validated", models.BooleanField(default=False)),
                ("validated_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "validated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "document",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="extracted_entities",
                        to="documents.uploadeddocument",
                    ),
                ),
            ],
            options={
                "ordering": ["page_number", "entity_type"],
            },
        ),
        migrations.CreateModel(
            name="DocumentAnomalyFlag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "anomaly_type",
                    models.CharField(
                        choices=[
                            ("DUPLICATE_ENTRY", "Duplicate Entry"),
                            ("MISSING_DATA", "Missing Data"),
                            ("INVALID_FORMAT", "Invalid Format"),
                            ("CALCULATION_ERROR", "Calculation Error"),
                            ("UNUSUAL_AMOUNT", "Unusual Amount"),
                            ("DATE_INCONSISTENCY", "Date Inconsistency"),
                            ("TAX_MISMATCH", "Tax Calculation Mismatch"),
                            ("SEQUENCE_BREAK", "Sequence Break"),
                            ("SUSPICIOUS_PATTERN", "Suspicious Pattern"),
                            ("DATA_QUALITY", "Data Quality Issue"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "severity",
                    models.CharField(
                        choices=[
                            ("LOW", "Low"),
                            ("MEDIUM", "Medium"),
                            ("HIGH", "High"),
                            ("CRITICAL", "Critical"),
                        ],
                        max_length=10,
                    ),
                ),
                ("description", models.TextField()),
                (
                    "affected_field",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("expected_value", models.TextField(blank=True, null=True)),
                ("actual_value", models.TextField(blank=True, null=True)),
                ("detection_algorithm", models.CharField(max_length=100)),
                ("confidence_score", models.FloatField()),
                ("is_resolved", models.BooleanField(default=False)),
                ("resolution_notes", models.TextField(blank=True, null=True)),
                ("resolved_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "resolved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "document",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="anomaly_flags",
                        to="documents.uploadeddocument",
                    ),
                ),
            ],
            options={
                "ordering": ["-severity", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="DocumentAnalysis",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "analysis_type",
                    models.CharField(
                        choices=[
                            ("OCR", "Optical Character Recognition"),
                            ("CLASSIFICATION", "Document Classification"),
                            ("EXTRACTION", "Data Extraction"),
                            ("ANOMALY_DETECTION", "Anomaly Detection"),
                            ("VALIDATION", "Data Validation"),
                            ("RECONCILIATION", "Reconciliation Analysis"),
                        ],
                        max_length=20,
                    ),
                ),
                ("results", models.JSONField()),
                ("confidence_score", models.FloatField()),
                ("processing_time", models.FloatField()),
                ("model_name", models.CharField(max_length=100)),
                ("model_version", models.CharField(max_length=20)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "document",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="analyses",
                        to="documents.uploadeddocument",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.AddIndex(
            model_name="uploadeddocument",
            index=models.Index(
                fields=["document_type"], name="documents_u_documen_eeeef3_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="uploadeddocument",
            index=models.Index(
                fields=["processing_status"], name="documents_u_process_1e1b45_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="uploadeddocument",
            index=models.Index(
                fields=["file_hash"], name="documents_u_file_ha_7977fd_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="uploadeddocument",
            index=models.Index(
                fields=["upload_date"], name="documents_u_upload__794aa5_idx"
            ),
        ),
    ]
