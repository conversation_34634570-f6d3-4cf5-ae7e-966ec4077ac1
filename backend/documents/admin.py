"""admin.py - Document processing admin configuration"""

from django.contrib import admin
from .models import (
    UploadedDocument, DocumentAnalysis, ExtractedEntity,
    DocumentAnomalyFlag, DocumentTemplate
)

@admin.register(UploadedDocument)
class UploadedDocumentAdmin(admin.ModelAdmin):
    list_display = (
        'document_name', 'client', 'document_type', 'processing_status',
        'confidence_score', 'has_anomalies', 'requires_review', 'upload_date'
    )
    list_filter = (
        'document_type', 'processing_status', 'has_anomalies',
        'requires_review', 'is_duplicate', 'upload_date'
    )
    search_fields = ('document_name', 'client__name', 'extracted_text')
    readonly_fields = (
        'file_size', 'file_hash', 'processing_started_at', 'processing_completed_at',
        'extracted_text', 'extracted_data', 'confidence_score', 'upload_date', 'updated_at'
    )
    raw_id_fields = ('client', 'engagement', 'uploaded_by', 'duplicate_of')

@admin.register(DocumentAnalysis)
class DocumentAnalysisAdmin(admin.ModelAdmin):
    list_display = (
        'document', 'analysis_type', 'confidence_score', 'processing_time',
        'model_name', 'created_at'
    )
    list_filter = ('analysis_type', 'model_name', 'created_at')
    search_fields = ('document__document_name',)
    readonly_fields = ('created_at',)
    raw_id_fields = ('document',)

@admin.register(ExtractedEntity)
class ExtractedEntityAdmin(admin.ModelAdmin):
    list_display = (
        'document', 'entity_type', 'entity_value', 'confidence_score',
        'is_validated', 'validated_by', 'created_at'
    )
    list_filter = ('entity_type', 'is_validated', 'created_at')
    search_fields = ('document__document_name', 'entity_value')
    readonly_fields = ('created_at', 'validated_at')
    raw_id_fields = ('document', 'validated_by')

@admin.register(DocumentAnomalyFlag)
class DocumentAnomalyFlagAdmin(admin.ModelAdmin):
    list_display = (
        'document', 'anomaly_type', 'severity', 'confidence_score',
        'is_resolved', 'resolved_by', 'created_at'
    )
    list_filter = ('anomaly_type', 'severity', 'is_resolved', 'created_at')
    search_fields = ('document__document_name', 'description')
    readonly_fields = ('created_at', 'resolved_at')
    raw_id_fields = ('document', 'resolved_by')

@admin.register(DocumentTemplate)
class DocumentTemplateAdmin(admin.ModelAdmin):
    list_display = (
        'name', 'document_type', 'is_active', 'usage_count',
        'accuracy_score', 'created_by', 'created_at'
    )
    list_filter = ('document_type', 'is_active', 'created_at')
    search_fields = ('name', 'description')
    readonly_fields = ('usage_count', 'accuracy_score', 'created_at', 'updated_at')
    raw_id_fields = ('created_by',)
