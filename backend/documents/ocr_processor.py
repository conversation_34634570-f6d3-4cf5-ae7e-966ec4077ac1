"""ocr_processor.py - Enhanced document processing with ML"""

import hashlib
import mimetypes
from typing import Dict, Any, List
from django.utils import timezone
from django.core.files.base import ContentFile
from .models import UploadedDocument, DocumentAnalysis, ExtractedEntity, DocumentAnomalyFlag
from document_processing.ml_services import DocumentClassifier, OCRProcessor, FinancialDataExtractor, AnomalyDetector

class DocumentProcessor:
    """Main document processing orchestrator"""

    def __init__(self):
        self.classifier = DocumentClassifier()
        self.ocr_processor = OCRProcessor()
        self.data_extractor = FinancialDataExtractor()
        self.anomaly_detector = AnomalyDetector()

    def process_document(self, document: UploadedDocument) -> Dict[str, Any]:
        """
        Process a document through the complete ML pipeline.

        Args:
            document (UploadedDocument): Document to process

        Returns:
            Dict[str, Any]: Processing results
        """
        try:
            document.processing_status = 'PROCESSING'
            document.processing_started_at = timezone.now()
            document.save()

            # Calculate file hash for duplicate detection
            file_hash = self._calculate_file_hash(document.file)
            document.file_hash = file_hash

            # Check for duplicates
            duplicate = self._check_for_duplicates(document, file_hash)
            if duplicate:
                document.is_duplicate = True
                document.duplicate_of = duplicate

            # Extract text based on file type
            extracted_text = self._extract_text(document)
            document.extracted_text = extracted_text

            # Classify document if type is not specified or is 'OTHERS'
            if document.document_type == 'OTHERS' or not document.document_type:
                doc_type, confidence = self.classifier.classify_document(extracted_text)
                if confidence > 0.7:  # High confidence threshold
                    document.document_type = doc_type
                    document.confidence_score = confidence

            # Extract structured data
            extracted_data = self.data_extractor.extract_financial_data(
                extracted_text, document.document_type
            )
            document.extracted_data = extracted_data

            # Detect anomalies
            anomalies = self.anomaly_detector.detect_anomalies(
                extracted_data, document.document_type
            )

            # Save anomalies
            self._save_anomalies(document, anomalies)

            # Save extracted entities
            self._save_extracted_entities(document, extracted_data)

            # Update processing status
            document.processing_status = 'COMPLETED'
            document.processing_completed_at = timezone.now()
            document.has_anomalies = len(anomalies) > 0
            document.requires_review = len(anomalies) > 0 or document.confidence_score < 0.8

            document.save()

            return {
                'status': 'success',
                'document_type': document.document_type,
                'confidence_score': document.confidence_score,
                'anomalies_count': len(anomalies),
                'extracted_entities_count': len(extracted_data.get('amounts', [])) + len(extracted_data.get('dates', []))
            }

        except Exception as e:
            document.processing_status = 'FAILED'
            document.processing_error = str(e)
            document.processing_completed_at = timezone.now()
            document.save()

            return {
                'status': 'error',
                'error': str(e)
            }

    def _calculate_file_hash(self, file) -> str:
        """Calculate SHA-256 hash of file"""
        hasher = hashlib.sha256()
        for chunk in file.chunks():
            hasher.update(chunk)
        return hasher.hexdigest()

    def _check_for_duplicates(self, document: UploadedDocument, file_hash: str) -> UploadedDocument:
        """Check for duplicate documents"""
        return UploadedDocument.objects.filter(
            file_hash=file_hash,
            client=document.client
        ).exclude(id=document.id).first()

    def _extract_text(self, document: UploadedDocument) -> str:
        """Extract text from document based on file type"""
        file_path = document.file.path
        mime_type, _ = mimetypes.guess_type(file_path)

        if mime_type and mime_type.startswith('image/'):
            # Process image files with OCR
            with open(file_path, 'rb') as f:
                image_bytes = f.read()

            ocr_result = self.ocr_processor.extract_text_from_image(image_bytes)

            # Save OCR analysis
            DocumentAnalysis.objects.create(
                document=document,
                analysis_type='OCR',
                results=ocr_result,
                confidence_score=ocr_result.get('confidence', 0),
                processing_time=ocr_result.get('processing_time', 0),
                model_name='Tesseract',
                model_version='4.0'
            )

            return ocr_result.get('text', '')

        elif mime_type == 'application/pdf':
            # Process PDF files
            return self._extract_text_from_pdf(file_path)

        elif mime_type in ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']:
            # Process Excel files
            return self._extract_text_from_excel(file_path)

        else:
            return ''

    def _extract_text_from_pdf(self, file_path: str) -> str:
        """Extract text from PDF file"""
        try:
            import PyMuPDF as fitz
            doc = fitz.open(file_path)
            text = ""
            for page in doc:
                text += page.get_text()
            doc.close()
            return text
        except ImportError:
            # Fallback if PyMuPDF is not available
            return "PDF processing not available"
        except Exception as e:
            return f"Error processing PDF: {str(e)}"

    def _extract_text_from_excel(self, file_path: str) -> str:
        """Extract text from Excel file"""
        try:
            import pandas as pd
            df = pd.read_excel(file_path)
            return df.to_string()
        except Exception as e:
            return f"Error processing Excel: {str(e)}"

    def _save_anomalies(self, document: UploadedDocument, anomalies: List[Dict[str, Any]]):
        """Save detected anomalies"""
        for anomaly in anomalies:
            DocumentAnomalyFlag.objects.create(
                document=document,
                anomaly_type=anomaly['type'],
                severity=anomaly['severity'],
                description=anomaly['description'],
                affected_field=anomaly.get('affected_field'),
                expected_value=anomaly.get('expected_value'),
                actual_value=str(anomaly.get('actual_value', '')),
                detection_algorithm='ML_PIPELINE',
                confidence_score=anomaly.get('confidence', 0.5)
            )

    def _save_extracted_entities(self, document: UploadedDocument, extracted_data: Dict[str, Any]):
        """Save extracted entities"""
        # Save amounts
        for amount in extracted_data.get('amounts', []):
            ExtractedEntity.objects.create(
                document=document,
                entity_type='AMOUNT',
                entity_value=str(amount['value']),
                confidence_score=0.8  # Default confidence
            )

        # Save dates
        for date in extracted_data.get('dates', []):
            ExtractedEntity.objects.create(
                document=document,
                entity_type='DATE',
                entity_value=date['text'],
                confidence_score=0.7
            )

        # Save PAN numbers
        for pan in extracted_data.get('pan_numbers', []):
            ExtractedEntity.objects.create(
                document=document,
                entity_type='PAN',
                entity_value=pan,
                confidence_score=0.9
            )

        # Save GST numbers
        for gst in extracted_data.get('gst_numbers', []):
            ExtractedEntity.objects.create(
                document=document,
                entity_type='GST_NUMBER',
                entity_value=gst,
                confidence_score=0.9
            )

# Legacy function for backward compatibility
def extract_text_from_image(image_bytes: bytes) -> str:
    """
    Extract text from an image file using OCR (Tesseract)

    Args:
        image_bytes (bytes): Raw image file content

    Returns:
        str: Extracted text
    """
    processor = OCRProcessor()
    result = processor.extract_text_from_image(image_bytes)
    return result.get('text', '')
