"""ocr_processor.py - Extract text from scanned documents"""

import pytesseract
from PIL import Image
import io

def extract_text_from_image(image_bytes: bytes) -> str:
    """
    Extract text from an image file using OCR (Tesseract)

    Args:
        image_bytes (bytes): Raw image file content

    Returns:
        str: Extracted text
    """
    image = Image.open(io.BytesIO(image_bytes))
    text = pytesseract.image_to_string(image)
    return text
